# Git
.git
.gitignore

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
*.sqlite3
db.sqlite3
media/
staticfiles/
.pytest_cache/
node_modules/

# Documentation
docs/_build/
*.md
!README.md

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Logs
*.log
logs/

# Database
*.db
*.sqlite
*.sqlite3

# Environment files
.env*
!.env.example
