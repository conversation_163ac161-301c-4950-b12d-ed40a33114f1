# Django Configuration
DEBUG=1
SECRET_KEY=django-insecure-!gor+b)2b3f+^*@=2(5rgj@0&6i2!n5ou!0bs*k^6+5juna$l7
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration (Docker containers)
POSTGRES_DB=hoplogic
POSTGRES_USER=hoplogic_user
POSTGRES_PASSWORD=hoplogic_password
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
DATABASE_URL=postgresql://hoplogic_user:hoplogic_password@localhost:5432/hoplogic

# AI Model Configuration
OPENAI_API_KEY=your-openai-api-key-here
OLLAMA_BASE_URL=http://localhost:11434

# Optional: Redis Configuration (for future caching)
REDIS_URL=redis://localhost:6379/0
