# Hoplogic Django to Rust Migration Plan (Refined)

## Executive Summary

This document outlines a comprehensive 14-week migration plan to rewrite the hoplogic brewing recipe application backend from Django to Rust, following inside-out development methodology and implementing CQRS with hexagonal architecture.

## Current Application Analysis

### Architecture Overview
- **Framework:** Django 5.2+ with Django REST Framework
- **Database:** PostgreSQL with custom BaseModel (soft deletes, stripe-like IDs)
- **Agent System:** LangChain/LangGraph with custom tool factory and dependency injection
- **Frontend:** Server-side rendered templates with JavaScript for dynamic chat

### Core Domain Models
- **Recipe:** Central aggregate with calculated properties (OG, IBU, SRM, ABV)
- **Ingredients:** Hop, Fermentable, Yeast with specialized brewing properties
- **Inclusions:** Type-specific hop additions (Boil, Whirlpool, DryHop, etc.)
- **Supporting:** BeerStyle, WaterProfile, MashStep, FermentationPhase, User

### Agent System
- MasterBrewerAgent using LangGraph's ReAct pattern
- Dynamic tool generation from service methods via `@agent_accessible` decorator
- Custom dependency injection framework with singleton lifecycle
- Memory persistence per recipe conversation thread

## Target Architecture

### Technology Stack
- **Language:** Rust with async/await
- **Web Framework:** Axum for HTTP API
- **Database:** MongoDB with document modeling
- **Agent Framework:** Rig for AI agent implementation
- **Architecture:** Hexagonal Architecture with Domain-Driven Design
- **Pattern:** CQRS for read/write separation
- **Observability:** tracing, metrics, structured logging

### Design Principles
- **Inside-Out Development:** Start with pure domain logic, build outward
- **Iterative Design:** Minimal, testable, incrementally valuable phases
- **Hexagonal Architecture:** Clear separation of concerns with ports/adapters
- **Domain-Driven Design:** Rich domain models with business logic encapsulation
- **CQRS:** Command/Query separation for optimal read/write patterns

## Inside-Out Development Methodology

### Phase Structure Philosophy
Each phase follows the inside-out principle:
1. **Domain First:** Pure business logic with no external dependencies
2. **Ports Definition:** Abstract interfaces for external concerns
3. **Application Services:** Orchestration and use case implementation
4. **Infrastructure Last:** Concrete implementations of ports

### Iterative Design Principles
- Each phase delivers working, testable functionality
- No over-engineering or premature optimization
- Dependencies added only when actually needed
- Continuous validation of design decisions through testing

## CQRS Implementation Strategy

### Command vs Query Separation

#### Commands (Write Operations)
- `CreateRecipe` - Initialize new recipe with basic properties
- `UpdateRecipeBasics` - Modify name, description, batch size, efficiency
- `AddHopInclusion` - Add hop addition with specific type and timing
- `UpdateHopInclusion` - Modify existing hop addition parameters
- `RemoveHopInclusion` - Remove hop addition from recipe
- `AddFermentableInclusion` - Add grain/extract to recipe
- `UpdateFermentableInclusion` - Modify fermentable quantities/properties
- `RemoveFermentableInclusion` - Remove fermentable from recipe
- `SetWaterProfile` - Assign water profile to recipe
- `SetBeerStyle` - Assign target beer style
- `AddMashStep` - Add mashing step with time/temperature
- `AddFermentationPhase` - Add fermentation phase with conditions

#### Queries (Read Operations)
- `GetRecipeById` - Retrieve complete recipe with all inclusions
- `GetRecipeCalculations` - Get computed values (OG, IBU, SRM, ABV)
- `GetRecipesByUser` - List user's recipes with basic info
- `SearchIngredients` - Find hops/fermentables by criteria
- `GetBeerStyles` - Retrieve beer style guidelines
- `GetRecipeHistory` - Retrieve recipe modification history

### MongoDB Document Design Strategy

#### Write Model (Command Side)
```rust
// Normalized documents optimized for writes
RecipeDocument {
    id: ObjectId,
    user_id: String,
    name: String,
    description: String,
    batch_size_gallons: f64,
    mash_efficiency_percent: f64,
    target_original_gravity: f64,
    water_profile_id: Option<String>,
    beer_style_id: Option<String>,
    created_at: DateTime,
    updated_at: DateTime,
    version: u64, // For optimistic concurrency
}

HopInclusionDocument {
    id: ObjectId,
    recipe_id: ObjectId,
    hop_id: ObjectId,
    inclusion_type: HopInclusionType,
    quantity: f64,
    quantity_unit: QuantityUnit,
    timing_parameters: BTreeMap<String, Value>, // Type-specific params
    notes: String,
}
```

#### Read Model (Query Side)
```rust
// Denormalized documents optimized for reads
RecipeReadModel {
    id: String,
    user_id: String,
    name: String,
    description: String,
    batch_size_gallons: f64,

    // Pre-calculated values
    calculated_original_gravity: f64,
    calculated_final_gravity: f64,
    calculated_abv: f64,
    calculated_ibu: f64,
    calculated_srm: f64,

    // Embedded related data
    hop_inclusions: Vec<HopInclusionReadModel>,
    fermentable_inclusions: Vec<FermentableInclusionReadModel>,
    water_profile: Option<WaterProfileReadModel>,
    beer_style: Option<BeerStyleReadModel>,

    // Metadata
    last_updated: DateTime,
    calculation_version: u64,
}
```

### CQRS Integration with Agent System

#### Agent Command Integration
- Agents issue commands through application services
- Commands are validated and processed synchronously
- Read models are updated asynchronously via event handlers
- Agent tools query read models for current recipe state

#### Real-time Collaboration
- Commands generate domain events
- Events trigger read model updates
- WebSocket notifications sent to connected clients
- Frontend receives updated recipe state automatically

### Event Sourcing Decision
**Decision: Simple CQRS without Event Sourcing**

**Rationale:**
- Recipe modifications are not high-frequency operations
- Audit trail can be maintained through versioning
- Simpler implementation reduces complexity
- Can evolve to event sourcing later if needed

**Implementation:**
- Commands update write models directly
- Domain events published for read model updates
- Change tracking through document versioning
- Audit log as separate concern if needed

## Project Structure Visualization

```
hoplogic-rust/
├── Cargo.toml
├── README.md
└── src/
    ├── main.rs                          # Application entry point
    ├── lib.rs                           # Library root
    │
    ├── domain/                          # Pure business logic (no dependencies)
    │   ├── mod.rs
    │   ├── recipe/                      # Recipe aggregate
    │   │   ├── mod.rs
    │   │   ├── aggregate.rs             # Recipe aggregate root
    │   │   ├── entities.rs              # Recipe entities
    │   │   ├── value_objects.rs         # Recipe value objects
    │   │   ├── events.rs                # Domain events
    │   │   ├── calculations/            # Business calculations
    │   │   │   ├── mod.rs
    │   │   │   ├── gravity.rs           # OG/FG calculations
    │   │   │   ├── ibu.rs               # IBU calculations
    │   │   │   ├── srm.rs               # Color calculations
    │   │   │   └── abv.rs               # Alcohol calculations
    │   │   └── errors.rs                # Domain-specific errors
    │   │
    │   ├── ingredients/                 # Ingredient domain
    │   │   ├── mod.rs
    │   │   ├── hop.rs                   # Hop entity and value objects
    │   │   ├── fermentable.rs           # Fermentable entity
    │   │   ├── yeast.rs                 # Yeast entity
    │   │   └── water_profile.rs         # Water profile entity
    │   │
    │   ├── inclusions/                  # Inclusion domain
    │   │   ├── mod.rs
    │   │   ├── hop_inclusion.rs         # Hop inclusion types
    │   │   ├── fermentable_inclusion.rs # Fermentable inclusion
    │   │   └── yeast_inclusion.rs       # Yeast inclusion
    │   │
    │   ├── brewing/                     # Brewing process domain
    │   │   ├── mod.rs
    │   │   ├── mash_step.rs             # Mashing process
    │   │   ├── fermentation_phase.rs    # Fermentation process
    │   │   └── beer_style.rs            # Beer style guidelines
    │   │
    │   └── shared/                      # Shared domain concepts
    │       ├── mod.rs
    │       ├── user_id.rs               # User identity
    │       ├── recipe_id.rs             # Recipe identity
    │       ├── units.rs                 # Measurement units
    │       └── validation.rs            # Domain validation rules
    │
    ├── application/                     # Application services and use cases
    │   ├── mod.rs
    │   ├── ports/                       # Abstract interfaces (ports)
    │   │   ├── mod.rs
    │   │   ├── repositories.rs          # Repository interfaces
    │   │   ├── event_publisher.rs       # Event publishing interface
    │   │   ├── agent_tools.rs           # Agent tool interfaces
    │   │   └── notifications.rs         # Notification interfaces
    │   │
    │   ├── commands/                    # Command handlers (CQRS write side)
    │   │   ├── mod.rs
    │   │   ├── recipe/                  # Recipe command handlers
    │   │   │   ├── mod.rs
    │   │   │   ├── create_recipe.rs
    │   │   │   ├── update_recipe.rs
    │   │   │   ├── add_hop_inclusion.rs
    │   │   │   └── remove_hop_inclusion.rs
    │   │   ├── ingredients/             # Ingredient command handlers
    │   │   │   ├── mod.rs
    │   │   │   ├── create_hop.rs
    │   │   │   └── update_hop.rs
    │   │   └── shared/                  # Shared command infrastructure
    │   │       ├── mod.rs
    │   │       ├── command_handler.rs   # Command handler trait
    │   │       └── validation.rs        # Command validation
    │   │
    │   ├── queries/                     # Query handlers (CQRS read side)
    │   │   ├── mod.rs
    │   │   ├── recipe/                  # Recipe query handlers
    │   │   │   ├── mod.rs
    │   │   │   ├── get_recipe.rs
    │   │   │   ├── get_recipe_calculations.rs
    │   │   │   └── list_user_recipes.rs
    │   │   ├── ingredients/             # Ingredient query handlers
    │   │   │   ├── mod.rs
    │   │   │   ├── search_hops.rs
    │   │   │   └── search_fermentables.rs
    │   │   └── shared/                  # Shared query infrastructure
    │   │       ├── mod.rs
    │   │       ├── query_handler.rs     # Query handler trait
    │   │       └── pagination.rs        # Pagination support
    │   │
    │   ├── services/                    # Application services
    │   │   ├── mod.rs
    │   │   ├── recipe_service.rs        # Recipe orchestration
    │   │   ├── ingredient_service.rs    # Ingredient management
    │   │   └── calculation_service.rs   # Recipe calculations
    │   │
    │   └── events/                      # Event handling
    │       ├── mod.rs
    │       ├── handlers/                # Event handlers
    │       │   ├── mod.rs
    │       │   ├── recipe_updated.rs    # Recipe update handler
    │       │   └── calculation_updated.rs # Calculation update handler
    │       └── projections/             # Read model projections
    │           ├── mod.rs
    │           └── recipe_projection.rs # Recipe read model projection
    │
    ├── infrastructure/                  # External adapters and implementations
    │   ├── mod.rs
    │   ├── persistence/                 # Database implementations
    │   │   ├── mod.rs
    │   │   ├── mongodb/                 # MongoDB adapter
    │   │   │   ├── mod.rs
    │   │   │   ├── connection.rs        # Database connection
    │   │   │   ├── repositories/        # Repository implementations
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── recipe_repository.rs
    │   │   │   │   ├── ingredient_repository.rs
    │   │   │   │   └── read_model_repository.rs
    │   │   │   ├── models/              # MongoDB document models
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── recipe_document.rs
    │   │   │   │   ├── ingredient_document.rs
    │   │   │   │   └── read_models.rs
    │   │   │   └── migrations/          # Database migrations
    │   │   │       ├── mod.rs
    │   │   │       └── initial_schema.rs
    │   │   └── memory/                  # In-memory implementations (testing)
    │   │       ├── mod.rs
    │   │       └── repositories.rs
    │   │
    │   ├── web/                         # HTTP/Web layer
    │   │   ├── mod.rs
    │   │   ├── axum/                    # Axum web framework adapter
    │   │   │   ├── mod.rs
    │   │   │   ├── server.rs            # Server setup
    │   │   │   ├── routes/              # HTTP routes
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── recipe_routes.rs
    │   │   │   │   ├── ingredient_routes.rs
    │   │   │   │   ├── auth_routes.rs
    │   │   │   │   └── agent_routes.rs
    │   │   │   ├── handlers/            # HTTP handlers
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── recipe_handlers.rs
    │   │   │   │   ├── ingredient_handlers.rs
    │   │   │   │   └── agent_handlers.rs
    │   │   │   ├── middleware/          # HTTP middleware
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── auth.rs
    │   │   │   │   ├── logging.rs
    │   │   │   │   └── cors.rs
    │   │   │   ├── extractors/          # Request extractors
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── user.rs
    │   │   │   │   └── pagination.rs
    │   │   │   └── responses/           # Response types
    │   │   │       ├── mod.rs
    │   │   │       ├── recipe_response.rs
    │   │   │       └── error_response.rs
    │   │   └── static_files/            # Static file serving
    │   │       ├── mod.rs
    │   │       └── handler.rs
    │   │
    │   ├── agents/                      # AI agent implementations
    │   │   ├── mod.rs
    │   │   ├── rig/                     # Rig framework adapter
    │   │   │   ├── mod.rs
    │   │   │   ├── master_brewer.rs     # Master brewer agent
    │   │   │   ├── tools/               # Agent tools
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── recipe_tools.rs
    │   │   │   │   ├── ingredient_tools.rs
    │   │   │   │   └── calculation_tools.rs
    │   │   │   ├── memory/              # Conversation memory
    │   │   │   │   ├── mod.rs
    │   │   │   │   └── mongodb_memory.rs
    │   │   │   └── prompts/             # Agent prompts
    │   │   │       ├── mod.rs
    │   │   │       └── master_brewer.rs
    │   │   └── tools/                   # Tool implementations
    │   │       ├── mod.rs
    │   │       ├── recipe_tool_adapter.rs
    │   │       └── bjcp_retriever.rs
    │   │
    │   ├── events/                      # Event infrastructure
    │   │   ├── mod.rs
    │   │   ├── in_memory_publisher.rs   # In-memory event publisher
    │   │   └── mongodb_publisher.rs     # MongoDB-based event publisher
    │   │
    │   └── observability/               # Observability implementations
    │       ├── mod.rs
    │       ├── logging.rs               # Structured logging setup
    │       ├── metrics.rs               # Metrics collection
    │       └── tracing.rs               # Distributed tracing
    │
    ├── config/                          # Configuration management
    │   ├── mod.rs
    │   ├── app_config.rs                # Application configuration
    │   ├── database_config.rs           # Database configuration
    │   ├── agent_config.rs              # Agent configuration
    │   └── observability_config.rs      # Observability configuration
    │
    └── tests/                           # Test organization
        ├── mod.rs
        ├── unit/                        # Unit tests (mirror src structure)
        │   ├── domain/
        │   ├── application/
        │   └── infrastructure/
        ├── integration/                 # Integration tests
        │   ├── mod.rs
        │   ├── recipe_workflows.rs
        │   └── agent_interactions.rs
        ├── acceptance/                  # Acceptance tests
        │   ├── mod.rs
        │   └── recipe_collaboration.rs
        └── fixtures/                    # Test data and fixtures
            ├── mod.rs
            ├── recipes.rs
            └── ingredients.rs
```

This structure demonstrates:
- **Clear separation** between domain, application, and infrastructure layers
- **CQRS organization** with separate command and query handlers
- **Hexagonal architecture** with ports (interfaces) and adapters (implementations)
- **Domain-driven design** with rich domain models and business logic encapsulation
- **Test organization** that mirrors the main codebase structure
- **Configuration management** separated by concern
- **Observability** as a first-class concern

## Inside-Out Migration Phases (14 Weeks)

### Phase 1: Pure Domain Foundation (Weeks 1-2)
**Philosophy:** Build the core business logic with zero external dependencies

#### Week 1: Core Domain Models
**Deliverables:**
- Recipe aggregate with business rules
- Ingredient entities (Hop, Fermentable, Yeast)
- Inclusion value objects with type-specific behavior
- Domain events for state changes

**Key Principles:**
- No database, no HTTP, no external crates beyond std
- Rich domain models with encapsulated business logic
- Comprehensive unit tests for all business rules
- Domain events as pure data structures

**Example Domain Model:**
```rust
// src/domain/recipe/aggregate.rs
pub struct Recipe {
    id: RecipeId,
    user_id: UserId,
    name: String,
    description: String,
    batch_size: BatchSize,
    mash_efficiency: Percentage,
    target_og: SpecificGravity,
    hop_inclusions: Vec<HopInclusion>,
    fermentable_inclusions: Vec<FermentableInclusion>,
    // ... other fields
}

impl Recipe {
    pub fn calculate_original_gravity(&self) -> SpecificGravity {
        // Pure business logic - no external dependencies
        let total_points = self.fermentable_inclusions
            .iter()
            .map(|inclusion| inclusion.gravity_contribution(&self.batch_size))
            .sum::<f64>();

        SpecificGravity::from_points(total_points)
    }

    pub fn add_hop_inclusion(&mut self, inclusion: HopInclusion) -> Result<(), DomainError> {
        // Business rule validation
        self.validate_hop_inclusion(&inclusion)?;
        self.hop_inclusions.push(inclusion);
        Ok(())
    }
}
```

#### Week 2: Business Calculations & Validation
**Deliverables:**
- IBU calculation engine with Tinseth formula
- SRM color calculation with Morey equation
- ABV calculation with attenuation modeling
- Domain validation rules and error types

**Testing Strategy:**
- Property-based testing for calculation accuracy
- Unit tests for edge cases and business rules
- No mocks needed - pure functions

### Phase 2: Repository Ports Definition (Week 3)
**Philosophy:** Define abstract interfaces without implementation concerns

#### Deliverables:
- Repository trait definitions for all aggregates
- Query interfaces for read operations
- Event publisher interface
- Error types for repository operations

**Key Principles:**
- Traits only - no concrete implementations
- Focus on domain needs, not database structure
- Async interfaces where appropriate
- Clear separation of read/write concerns

**Example Repository Port:**
```rust
// src/application/ports/repositories.rs
#[async_trait]
pub trait RecipeRepository {
    async fn save(&self, recipe: &Recipe) -> Result<(), RepositoryError>;
    async fn find_by_id(&self, id: &RecipeId) -> Result<Option<Recipe>, RepositoryError>;
    async fn find_by_user(&self, user_id: &UserId) -> Result<Vec<Recipe>, RepositoryError>;
    async fn delete(&self, id: &RecipeId) -> Result<(), RepositoryError>;
}

#[async_trait]
pub trait RecipeQueryRepository {
    async fn get_recipe_with_calculations(&self, id: &RecipeId) -> Result<Option<RecipeReadModel>, RepositoryError>;
    async fn search_recipes(&self, criteria: &RecipeSearchCriteria) -> Result<Vec<RecipeReadModel>, RepositoryError>;
}
```

### Phase 3: Application Services & CQRS (Weeks 4-5)
**Philosophy:** Orchestrate domain operations through use cases

#### Week 4: Command Handlers
**Deliverables:**
- Command types for all write operations
- Command handlers with domain orchestration
- Command validation and error handling
- Unit tests with mock repositories

**Example Command Handler:**
```rust
// src/application/commands/recipe/create_recipe.rs
pub struct CreateRecipeCommand {
    pub user_id: UserId,
    pub name: String,
    pub description: String,
    pub batch_size_gallons: f64,
    pub target_og: f64,
}

pub struct CreateRecipeHandler<R: RecipeRepository> {
    repository: R,
    event_publisher: Box<dyn EventPublisher>,
}

impl<R: RecipeRepository> CreateRecipeHandler<R> {
    pub async fn handle(&self, command: CreateRecipeCommand) -> Result<RecipeId, ApplicationError> {
        // Validate command
        let recipe = Recipe::new(
            RecipeId::generate(),
            command.user_id,
            command.name,
            command.description,
            BatchSize::gallons(command.batch_size_gallons)?,
            SpecificGravity::new(command.target_og)?,
        )?;

        // Save to repository
        self.repository.save(&recipe).await?;

        // Publish domain event
        self.event_publisher.publish(RecipeCreated {
            recipe_id: recipe.id().clone(),
            user_id: recipe.user_id().clone(),
            created_at: Utc::now(),
        }).await?;

        Ok(recipe.id().clone())
    }
}
```

#### Week 5: Query Handlers & Read Models
**Deliverables:**
- Query types for all read operations
- Query handlers with read model projection
- Read model types optimized for UI consumption
- Comprehensive query testing

### Phase 4: Infrastructure Adapters (Weeks 6-8)
**Philosophy:** Implement concrete adapters for external systems

#### Week 6: MongoDB Repository Implementation
**Deliverables:**
- MongoDB connection and configuration
- Document models for write operations
- Repository implementations with proper error handling
- Database migration system

#### Week 7: Read Model Projections
**Deliverables:**
- MongoDB read model collections
- Event handlers for read model updates
- Query repository implementations
- Read model synchronization logic

#### Week 8: In-Memory Testing Implementations
**Deliverables:**
- In-memory repository implementations for testing
- Test fixtures and data builders
- Integration test infrastructure
- Performance benchmarking setup

### Phase 5: Web Layer & API (Weeks 9-10)
**Philosophy:** Expose application services through HTTP API

#### Week 9: Axum HTTP Infrastructure
**Deliverables:**
- Axum server setup with middleware
- Request/response types and serialization
- Authentication and authorization middleware
- Error handling and logging

#### Week 10: API Endpoints & Compatibility
**Deliverables:**
- REST API endpoints matching Django API
- Request validation and response formatting
- API versioning strategy
- Compatibility layer for frontend

### Phase 6: Agent System Integration (Weeks 11-12)
**Philosophy:** Integrate AI agents with domain services

#### Week 11: Rig Agent Framework Setup
**Deliverables:**
- Rig agent configuration and setup
- Tool interfaces for domain operations
- Conversation memory implementation
- Agent prompt engineering

#### Week 12: Agent-Domain Integration
**Deliverables:**
- Agent tools calling application services
- Real-time collaboration features
- WebSocket integration for live updates
- Agent conversation persistence

### Phase 7: Data Migration & Testing (Weeks 13-14)
**Philosophy:** Ensure data integrity and system reliability

#### Week 13: Data Migration Pipeline
**Deliverables:**
- PostgreSQL to MongoDB migration scripts
- Data validation and integrity checks
- Migration rollback procedures
- Performance optimization

#### Week 14: System Integration & Deployment
**Deliverables:**
- End-to-end integration testing
- Performance benchmarking and optimization
- Deployment pipeline and monitoring
- Production readiness checklist

## Success Metrics & Validation

### Performance Targets
- **API Response Time:** < 100ms for 95th percentile
- **Recipe Calculation Time:** < 10ms for complex recipes
- **Agent Response Time:** < 2s for tool-based responses
- **Memory Usage:** < 50% of Django equivalent

### Quality Metrics
- **Test Coverage:** > 90% for domain and application layers
- **Type Safety:** Zero runtime type errors in production
- **Error Handling:** Graceful degradation for all failure modes
- **Documentation:** Complete API documentation and developer guides

### Migration Validation
- **Data Integrity:** 100% data preservation during migration
- **API Compatibility:** Zero breaking changes for frontend
- **Feature Parity:** All Django features replicated in Rust
- **User Experience:** No degradation in application functionality

## Risk Mitigation Strategies

### Technical Risks
- **MongoDB Learning Curve:** Prototype document design early
- **Rig Framework Maturity:** Have fallback to direct LLM integration
- **Performance Assumptions:** Continuous benchmarking throughout development
- **CQRS Complexity:** Start simple, evolve based on actual needs

### Migration Risks
- **Data Loss:** Multiple backup strategies and validation checkpoints
- **Downtime:** Blue-green deployment with rollback capability
- **Feature Regression:** Comprehensive acceptance testing
- **Team Knowledge:** Pair programming and knowledge sharing sessions

### Timeline Risks
- **Scope Creep:** Strict adherence to inside-out methodology
- **Technical Debt:** Regular refactoring and code review
- **Integration Issues:** Early and frequent integration testing
- **Resource Constraints:** Parallel development tracks where possible

## Conclusion

This refined migration plan follows inside-out development principles to ensure each phase delivers tangible value while building toward the complete system. The CQRS implementation provides optimal read/write separation for the brewing domain, while the hexagonal architecture ensures clean separation of concerns and testability.

The 14-week timeline is aggressive but achievable with disciplined adherence to the iterative design principles and clear phase boundaries. Each phase builds incrementally on the previous work, ensuring continuous validation of design decisions and early identification of potential issues.
