# Hoplogic Django to Rust Migration Plan (Refined)

## Executive Summary

This document outlines a comprehensive 14-week migration plan to rewrite the hoplogic brewing recipe application backend from Django to Rust, following inside-out development methodology and implementing CQRS with hexagonal architecture.

## Current Application Analysis

### Architecture Overview
- **Framework:** Django 5.2+ with Django REST Framework
- **Database:** PostgreSQL with custom BaseModel (soft deletes, stripe-like IDs)
- **Agent System:** LangChain/LangGraph with custom tool factory and dependency injection
- **Frontend:** Server-side rendered templates with JavaScript for dynamic chat

### Core Domain Models
- **Recipe:** Central aggregate with calculated properties (OG, IBU, SRM, ABV)
- **Ingredients:** Hop, Fermentable, Yeast with specialized brewing properties
- **Inclusions:** Type-specific hop additions (Boil, Whirlpool, DryHop, etc.)
- **Supporting:** BeerStyle, WaterProfile, MashStep, FermentationPhase, User

### Agent System
- MasterBrewerAgent using LangGraph's ReAct pattern
- Dynamic tool generation from service methods via `@agent_accessible` decorator
- Custom dependency injection framework with singleton lifecycle
- Memory persistence per recipe conversation thread

## Target Architecture

### Technology Stack
- **Language:** Rust with async/await
- **Web Framework:** Axum for HTTP API
- **Database:** MongoDB with document modeling
- **Agent Framework:** Rig for AI agent implementation
- **Architecture:** Hexagonal Architecture with Domain-Driven Design
- **Pattern:** CQRS for read/write separation
- **Observability:** tracing, metrics, structured logging

### Design Principles
- **Inside-Out Development:** Start with pure domain logic, build outward
- **Iterative Design:** Minimal, testable, incrementally valuable phases
- **Hexagonal Architecture:** Clear separation of concerns with ports/adapters
- **Domain-Driven Design:** Rich domain models with business logic encapsulation
- **CQRS:** Command/Query separation for optimal read/write patterns

## Inside-Out Development Methodology

### Phase Structure Philosophy
Each phase follows the inside-out principle:
1. **Domain First:** Pure business logic with no external dependencies
2. **Ports Definition:** Abstract interfaces for external concerns
3. **Application Services:** Orchestration and use case implementation
4. **Infrastructure Last:** Concrete implementations of ports

### Iterative Design Principles
- Each phase delivers working, testable functionality
- No over-engineering or premature optimization
- Dependencies added only when actually needed
- Continuous validation of design decisions through testing

## CQRS Implementation Strategy

### Command vs Query Separation

#### Commands (Write Operations)
- `CreateRecipe` - Initialize new recipe with basic properties
- `UpdateRecipeBasics` - Modify name, description, batch size, efficiency
- `AddHopInclusion` - Add hop addition with specific type and timing
- `UpdateHopInclusion` - Modify existing hop addition parameters
- `RemoveHopInclusion` - Remove hop addition from recipe
- `AddFermentableInclusion` - Add grain/extract to recipe
- `UpdateFermentableInclusion` - Modify fermentable quantities/properties
- `RemoveFermentableInclusion` - Remove fermentable from recipe
- `SetWaterProfile` - Assign water profile to recipe
- `SetBeerStyle` - Assign target beer style
- `AddMashStep` - Add mashing step with time/temperature
- `AddFermentationPhase` - Add fermentation phase with conditions

#### Queries (Read Operations)
- `GetRecipeById` - Retrieve complete recipe with all inclusions
- `GetRecipeCalculations` - Get computed values (OG, IBU, SRM, ABV)
- `GetRecipesByUser` - List user's recipes with basic info
- `SearchIngredients` - Find hops/fermentables by criteria
- `GetBeerStyles` - Retrieve beer style guidelines
- `GetRecipeHistory` - Retrieve recipe modification history

### MongoDB Document Design Strategy

#### Event Store (Command Side)
```rust
// Event store optimized for append-only writes
EventStoreDocument {
    stream_id: String,           // Recipe-{uuid} or Ingredient-{uuid}
    event_id: String,            // Unique event identifier
    event_type: String,          // "RecipeCreated", "HopInclusionAdded", etc.
    event_version: u32,          // Schema version for event type
    aggregate_version: u64,      // Position in this aggregate's stream
    event_data: Document,        // Serialized domain event
    metadata: EventMetadata,     // User context, correlation ID, etc.
    created_at: DateTime,
}

EventMetadata {
    user_id: String,
    correlation_id: String,      // For tracing related events
    causation_id: Option<String>, // Event that caused this event
    agent_session_id: Option<String>, // For agent-initiated changes
}

// Snapshot documents for performance
SnapshotDocument {
    stream_id: String,
    aggregate_version: u64,      // Last event version included
    snapshot_data: Document,     // Serialized aggregate state
    created_at: DateTime,
}
```

#### Read Model (Query Side)
```rust
// Denormalized documents optimized for reads
RecipeReadModel {
    id: String,
    user_id: String,
    name: String,
    description: String,
    batch_size_gallons: f64,

    // Pre-calculated values
    calculated_original_gravity: f64,
    calculated_final_gravity: f64,
    calculated_abv: f64,
    calculated_ibu: f64,
    calculated_srm: f64,

    // Embedded related data
    hop_inclusions: Vec<HopInclusionReadModel>,
    fermentable_inclusions: Vec<FermentableInclusionReadModel>,
    water_profile: Option<WaterProfileReadModel>,
    beer_style: Option<BeerStyleReadModel>,

    // Metadata
    last_updated: DateTime,
    calculation_version: u64,
}
```

## Authorization Strategy

### Policy-Based Authorization at Application Layer

Authorization is handled at the application layer, above both REST API routes and agent tool calls, ensuring consistent security across all access patterns.

#### Authorization Architecture
```rust
// Authorization service that both REST and Agent systems use
pub struct AuthorizationService<P: AuthorizationPolicy> {
    policy: P,
}

impl<P: AuthorizationPolicy> AuthorizationService<P> {
    pub async fn authorize_command<C: Command>(
        &self,
        user_context: &UserContext,
        command: &C,
    ) -> Result<(), AuthorizationError> {
        self.policy.authorize(user_context, command).await
    }
}

// User context extracted from JWT tokens or agent sessions
pub struct UserContext {
    pub user_id: UserId,
    pub roles: Vec<Role>,
    pub permissions: Vec<Permission>,
    pub agent_session_id: Option<String>, // For agent-initiated actions
}

// Policy-based authorization for different resources
pub trait AuthorizationPolicy {
    async fn authorize<C: Command>(
        &self,
        user_context: &UserContext,
        command: &C,
    ) -> Result<(), AuthorizationError>;
}

// Recipe-specific authorization policies
pub struct RecipeAuthorizationPolicy;

impl AuthorizationPolicy for RecipeAuthorizationPolicy {
    async fn authorize<C: Command>(
        &self,
        user_context: &UserContext,
        command: &C,
    ) -> Result<(), AuthorizationError> {
        match command.command_type() {
            CommandType::CreateRecipe => {
                // Any authenticated user can create recipes
                Ok(())
            }
            CommandType::UpdateRecipe(recipe_id) => {
                // Only recipe owner can modify
                self.ensure_recipe_owner(user_context, recipe_id).await
            }
            CommandType::DeleteRecipe(recipe_id) => {
                // Only recipe owner can delete
                self.ensure_recipe_owner(user_context, recipe_id).await
            }
            // ... other command types
        }
    }
}
```

#### Integration Points
- **REST API**: Authorization middleware extracts user context from JWT tokens
- **Agent System**: Agent tools include user context from conversation session
- **Command Bus**: All commands pass through authorization before execution
- **Audit Trail**: Authorization decisions logged with user context in event metadata

### CQRS Integration with Agent System

#### Agent Command Integration
- Agents issue commands through the same authorization layer as REST API
- Commands include user context from the agent conversation session
- Commands are validated, authorized, and processed synchronously
- Events are stored in event store and trigger read model updates
- Agent tools query read models for current recipe state

#### Real-time Collaboration
- Commands generate domain events stored in event store
- Event handlers update read models asynchronously
- WebSocket notifications sent to connected clients with authorized data
- Frontend receives updated recipe state automatically
- Agent conversations maintain user context throughout session

### Event Sourcing Implementation
**Decision: Full Event Sourcing with CQRS**

**Rationale:**
- Complete audit trail of all recipe modifications
- Natural fit for collaborative recipe development
- Enables time-travel debugging and recipe history
- Supports complex business rules and validation
- Facilitates agent learning from recipe evolution patterns

**Implementation:**
- Commands generate domain events that are persisted to event store
- Aggregates are reconstituted from event streams
- Read models are projections built from event streams
- Snapshots for performance optimization of large aggregates
- Event versioning strategy for schema evolution

**Event Store Design:**
```rust
// Event store document structure
EventDocument {
    stream_id: String,        // Recipe aggregate ID
    event_id: String,         // Unique event identifier
    event_type: String,       // Event type name
    event_version: u32,       // Event schema version
    aggregate_version: u64,   // Position in aggregate stream
    event_data: Document,     // Serialized event payload
    metadata: Document,       // User ID, timestamp, correlation ID
    created_at: DateTime,
}
```

## Project Structure Visualization

```
hoplogic-rust/
├── Cargo.toml
├── README.md
└── src/
    ├── main.rs                          # Application entry point
    ├── lib.rs                           # Library root
    │
    ├── domain/                          # Pure business logic (no dependencies)
    │   ├── mod.rs
    │   ├── recipe/                      # Recipe aggregate
    │   │   ├── mod.rs
    │   │   ├── aggregate.rs             # Recipe aggregate root
    │   │   ├── entities.rs              # Recipe entities
    │   │   ├── value_objects.rs         # Recipe value objects
    │   │   ├── events.rs                # Domain events
    │   │   ├── calculations/            # Business calculations
    │   │   │   ├── mod.rs
    │   │   │   ├── gravity.rs           # OG/FG calculations
    │   │   │   ├── ibu.rs               # IBU calculations
    │   │   │   ├── srm.rs               # Color calculations
    │   │   │   └── abv.rs               # Alcohol calculations
    │   │   └── errors.rs                # Domain-specific errors
    │   │
    │   ├── ingredients/                 # Ingredient domain
    │   │   ├── mod.rs
    │   │   ├── hop.rs                   # Hop entity and value objects
    │   │   ├── fermentable.rs           # Fermentable entity
    │   │   ├── yeast.rs                 # Yeast entity
    │   │   └── water_profile.rs         # Water profile entity
    │   │
    │   ├── inclusions/                  # Inclusion domain
    │   │   ├── mod.rs
    │   │   ├── hop_inclusion.rs         # Hop inclusion types
    │   │   ├── fermentable_inclusion.rs # Fermentable inclusion
    │   │   └── yeast_inclusion.rs       # Yeast inclusion
    │   │
    │   ├── brewing/                     # Brewing process domain
    │   │   ├── mod.rs
    │   │   ├── mash_step.rs             # Mashing process
    │   │   ├── fermentation_phase.rs    # Fermentation process
    │   │   └── beer_style.rs            # Beer style guidelines
    │   │
    │   └── shared/                      # Shared domain concepts
    │       ├── mod.rs
    │       ├── user_id.rs               # User identity
    │       ├── recipe_id.rs             # Recipe identity
    │       ├── units.rs                 # Measurement units
    │       └── validation.rs            # Domain validation rules
    │
    ├── application/                     # Application services and use cases
    │   ├── mod.rs
    │   ├── ports/                       # Abstract interfaces (ports)
    │   │   ├── mod.rs
    │   │   ├── repositories.rs          # Repository interfaces
    │   │   ├── event_publisher.rs       # Event publishing interface
    │   │   ├── agent_tools.rs           # Agent tool interfaces
    │   │   └── notifications.rs         # Notification interfaces
    │   │
    │   ├── commands/                    # Command handlers (CQRS write side)
    │   │   ├── mod.rs
    │   │   ├── recipe/                  # Recipe command handlers
    │   │   │   ├── mod.rs
    │   │   │   ├── create_recipe.rs
    │   │   │   ├── update_recipe.rs
    │   │   │   ├── add_hop_inclusion.rs
    │   │   │   └── remove_hop_inclusion.rs
    │   │   ├── ingredients/             # Ingredient command handlers
    │   │   │   ├── mod.rs
    │   │   │   ├── create_hop.rs
    │   │   │   └── update_hop.rs
    │   │   └── shared/                  # Shared command infrastructure
    │   │       ├── mod.rs
    │   │       ├── command_handler.rs   # Command handler trait
    │   │       └── validation.rs        # Command validation
    │   │
    │   ├── queries/                     # Query handlers (CQRS read side)
    │   │   ├── mod.rs
    │   │   ├── recipe/                  # Recipe query handlers
    │   │   │   ├── mod.rs
    │   │   │   ├── get_recipe.rs
    │   │   │   ├── get_recipe_calculations.rs
    │   │   │   └── list_user_recipes.rs
    │   │   ├── ingredients/             # Ingredient query handlers
    │   │   │   ├── mod.rs
    │   │   │   ├── search_hops.rs
    │   │   │   └── search_fermentables.rs
    │   │   └── shared/                  # Shared query infrastructure
    │   │       ├── mod.rs
    │   │       ├── query_handler.rs     # Query handler trait
    │   │       └── pagination.rs        # Pagination support
    │   │
    │   ├── services/                    # Application services
    │   │   ├── mod.rs
    │   │   ├── recipe_service.rs        # Recipe orchestration
    │   │   ├── ingredient_service.rs    # Ingredient management
    │   │   └── calculation_service.rs   # Recipe calculations
    │   │
    │   └── events/                      # Event handling
    │       ├── mod.rs
    │       ├── handlers/                # Event handlers
    │       │   ├── mod.rs
    │       │   ├── recipe_updated.rs    # Recipe update handler
    │       │   └── calculation_updated.rs # Calculation update handler
    │       └── projections/             # Read model projections
    │           ├── mod.rs
    │           └── recipe_projection.rs # Recipe read model projection
    │
    ├── infrastructure/                  # External adapters and implementations
    │   ├── mod.rs
    │   ├── persistence/                 # Database implementations
    │   │   ├── mod.rs
    │   │   ├── mongodb/                 # MongoDB adapter
    │   │   │   ├── mod.rs
    │   │   │   ├── connection.rs        # Database connection
    │   │   │   ├── repositories/        # Repository implementations
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── recipe_repository.rs
    │   │   │   │   ├── ingredient_repository.rs
    │   │   │   │   └── read_model_repository.rs
    │   │   │   ├── models/              # MongoDB document models
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── recipe_document.rs
    │   │   │   │   ├── ingredient_document.rs
    │   │   │   │   └── read_models.rs
    │   │   │   └── migrations/          # Database migrations
    │   │   │       ├── mod.rs
    │   │   │       └── initial_schema.rs
    │   │   └── memory/                  # In-memory implementations (testing)
    │   │       ├── mod.rs
    │   │       └── repositories.rs
    │   │
    │   ├── web/                         # HTTP/Web layer
    │   │   ├── mod.rs
    │   │   ├── axum/                    # Axum web framework adapter
    │   │   │   ├── mod.rs
    │   │   │   ├── server.rs            # Server setup
    │   │   │   ├── routes/              # HTTP routes
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── recipe_routes.rs
    │   │   │   │   ├── ingredient_routes.rs
    │   │   │   │   ├── auth_routes.rs
    │   │   │   │   └── agent_routes.rs
    │   │   │   ├── handlers/            # HTTP handlers
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── recipe_handlers.rs
    │   │   │   │   ├── ingredient_handlers.rs
    │   │   │   │   └── agent_handlers.rs
    │   │   │   ├── middleware/          # HTTP middleware
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── auth.rs
    │   │   │   │   ├── logging.rs
    │   │   │   │   └── cors.rs
    │   │   │   ├── extractors/          # Request extractors
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── user.rs
    │   │   │   │   └── pagination.rs
    │   │   │   └── responses/           # Response types
    │   │   │       ├── mod.rs
    │   │   │       ├── recipe_response.rs
    │   │   │       └── error_response.rs
    │   │   └── static_files/            # Static file serving
    │   │       ├── mod.rs
    │   │       └── handler.rs
    │   │
    │   ├── agents/                      # AI agent implementations
    │   │   ├── mod.rs
    │   │   ├── rig/                     # Rig framework adapter
    │   │   │   ├── mod.rs
    │   │   │   ├── master_brewer.rs     # Master brewer agent
    │   │   │   ├── tools/               # Agent tools
    │   │   │   │   ├── mod.rs
    │   │   │   │   ├── recipe_tools.rs
    │   │   │   │   ├── ingredient_tools.rs
    │   │   │   │   └── calculation_tools.rs
    │   │   │   ├── memory/              # Conversation memory
    │   │   │   │   ├── mod.rs
    │   │   │   │   └── mongodb_memory.rs
    │   │   │   └── prompts/             # Agent prompts
    │   │   │       ├── mod.rs
    │   │   │       └── master_brewer.rs
    │   │   └── tools/                   # Tool implementations
    │   │       ├── mod.rs
    │   │       ├── recipe_tool_adapter.rs
    │   │       └── bjcp_retriever.rs
    │   │
    │   ├── events/                      # Event infrastructure
    │   │   ├── mod.rs
    │   │   ├── in_memory_publisher.rs   # In-memory event publisher
    │   │   └── mongodb_publisher.rs     # MongoDB-based event publisher
    │   │
    │   └── observability/               # Observability implementations
    │       ├── mod.rs
    │       ├── logging.rs               # Structured logging setup
    │       ├── metrics.rs               # Metrics collection
    │       └── tracing.rs               # Distributed tracing
    │
    ├── config/                          # Configuration management
    │   ├── mod.rs
    │   ├── app_config.rs                # Application configuration
    │   ├── database_config.rs           # Database configuration
    │   ├── agent_config.rs              # Agent configuration
    │   └── observability_config.rs      # Observability configuration
    │
    └── tests/                           # Test organization
        ├── mod.rs
        ├── unit/                        # Unit tests (mirror src structure)
        │   ├── domain/
        │   ├── application/
        │   └── infrastructure/
        ├── integration/                 # Integration tests
        │   ├── mod.rs
        │   ├── recipe_workflows.rs
        │   └── agent_interactions.rs
        ├── acceptance/                  # Acceptance tests
        │   ├── mod.rs
        │   └── recipe_collaboration.rs
        └── fixtures/                    # Test data and fixtures
            ├── mod.rs
            ├── recipes.rs
            └── ingredients.rs
```

This structure demonstrates:
- **Clear separation** between domain, application, and infrastructure layers
- **CQRS organization** with separate command and query handlers
- **Hexagonal architecture** with ports (interfaces) and adapters (implementations)
- **Domain-driven design** with rich domain models and business logic encapsulation
- **Test organization** that mirrors the main codebase structure
- **Configuration management** separated by concern
- **Observability** as a first-class concern

## Architecture Visualization

```mermaid
graph TB
    %% External Actors
    Frontend[Frontend UI<br/>Templates & JavaScript]
    Agent[AI Agent<br/>Rig Framework]
    CLI[CLI Tools<br/>Migration & Admin]

    %% Infrastructure Layer (Outer Ring)
    subgraph Infrastructure["🔧 Infrastructure Layer (Adapters)"]
        subgraph WebAdapters["Web Adapters"]
            AxumServer[Axum HTTP Server<br/>Routes & Handlers]
            WebSocket[WebSocket Handler<br/>Real-time Updates]
            StaticFiles[Static File Server<br/>Frontend Assets]
        end

        subgraph PersistenceAdapters["Persistence Adapters"]
            MongoWrite[(MongoDB<br/>Event Store)]
            MongoRead[(MongoDB<br/>Read Models)]
            MemoryRepo[In-Memory Repos<br/>Testing]
        end

        subgraph AgentAdapters["Agent Adapters"]
            RigAgent[Rig Agent Framework<br/>Master Brewer]
            AgentTools[Agent Tools<br/>Domain Integration]
            AgentMemory[Conversation Memory<br/>MongoDB Storage]
        end

        subgraph ObservabilityAdapters["Observability"]
            Logging[Structured Logging<br/>tracing]
            Metrics[Metrics Collection<br/>prometheus]
            Tracing[Distributed Tracing<br/>jaeger]
        end
    end

    %% Application Layer (Middle Ring)
    subgraph Application["⚙️ Application Layer (Use Cases)"]
        subgraph CommandSide["Command Side (Write)"]
            CommandHandlers[Command Handlers<br/>CreateRecipe<br/>AddHopInclusion<br/>UpdateRecipe]
            CommandBus[Command Bus<br/>Validation & Routing]
            AuthorizationService[Authorization Service<br/>Policy-Based Access Control]
        end

        subgraph QuerySide["Query Side (Read)"]
            QueryHandlers[Query Handlers<br/>GetRecipe<br/>SearchRecipes<br/>GetCalculations]
            QueryBus[Query Bus<br/>Caching & Routing]
        end

        subgraph ApplicationServices["Application Services"]
            RecipeService[Recipe Service<br/>Orchestration]
            CalculationService[Calculation Service<br/>Recipe Computations]
            EventHandlers[Event Handlers<br/>Read Model Updates]
        end

        subgraph Ports["Ports (Interfaces)"]
            RepoInterfaces[Repository Interfaces<br/>EventStore<br/>ReadModelStore]
            EventPublisher[Event Publisher<br/>Interface]
            AgentInterfaces[Agent Tool Interfaces<br/>Domain Operations]
        end
    end

    %% Domain Layer (Inner Ring)
    subgraph Domain["🏛️ Domain Layer (Business Logic)"]
        subgraph RecipeAggregate["Recipe Aggregate"]
            Recipe[Recipe Entity<br/>Business Rules<br/>Calculations]
            RecipeEvents[Domain Events<br/>RecipeCreated<br/>RecipeUpdated]
        end

        subgraph IngredientEntities["Ingredient Entities"]
            Hop[Hop Entity<br/>Alpha/Beta Acids]
            Fermentable[Fermentable Entity<br/>Extract Potential]
            Yeast[Yeast Entity<br/>Attenuation]
        end

        subgraph InclusionValueObjects["Inclusion Value Objects"]
            HopInclusions[Hop Inclusions<br/>Boil, Whirlpool<br/>DryHop, etc.]
            FermentableInclusions[Fermentable Inclusions<br/>Grain Bill]
            YeastInclusions[Yeast Inclusions<br/>Pitching Rate]
        end

        subgraph BusinessCalculations["Business Calculations"]
            GravityCalc[Gravity Calculations<br/>OG/FG/ABV]
            IBUCalc[IBU Calculations<br/>Tinseth Formula]
            SRMCalc[SRM Calculations<br/>Morey Equation]
        end

        subgraph SharedKernel["Shared Kernel"]
            ValueObjects[Value Objects<br/>RecipeId, UserId<br/>BatchSize, etc.]
            DomainErrors[Domain Errors<br/>Validation Rules]
        end
    end

    %% CQRS Flow Arrows
    Frontend -->|HTTP Requests| AxumServer
    Agent -->|Tool Calls| AgentTools
    CLI -->|Commands| AxumServer

    AxumServer -->|Commands| AuthorizationService
    AxumServer -->|Queries| QueryBus
    AgentTools -->|Commands| AuthorizationService
    AgentTools -->|Queries| QueryBus

    AuthorizationService -->|Authorized| CommandBus
    CommandBus -->|Route| CommandHandlers
    QueryBus -->|Route| QueryHandlers

    CommandHandlers -->|Use| RepoInterfaces
    CommandHandlers -->|Orchestrate| RecipeService
    QueryHandlers -->|Use| RepoInterfaces

    RecipeService -->|Domain Logic| Recipe
    CalculationService -->|Calculations| BusinessCalculations

    CommandHandlers -->|Store Events| MongoWrite
    MongoWrite -->|Event Stream| EventHandlers
    EventHandlers -->|Update| MongoRead

    %% Repository Implementations
    RepoInterfaces -.->|Implements| MongoWrite
    RepoInterfaces -.->|Implements| MongoRead
    RepoInterfaces -.->|Implements| MemoryRepo

    %% Agent Integration
    RigAgent -->|Uses| AgentTools
    AgentTools -->|Calls| ApplicationServices
    AgentMemory -->|Stores| MongoRead

    %% Styling
    classDef domainStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef applicationStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef infrastructureStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef externalStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px

    class Recipe,RecipeEvents,Hop,Fermentable,Yeast,HopInclusions,FermentableInclusions,YeastInclusions,GravityCalc,IBUCalc,SRMCalc,ValueObjects,DomainErrors domainStyle
    class CommandHandlers,QueryHandlers,CommandBus,QueryBus,RecipeService,CalculationService,EventHandlers,RepoInterfaces,EventPublisher,AgentInterfaces,AuthorizationService applicationStyle
    class AxumServer,WebSocket,StaticFiles,MongoWrite,MongoRead,MemoryRepo,RigAgent,AgentTools,AgentMemory,Logging,Metrics,Tracing infrastructureStyle
    class Frontend,Agent,CLI externalStyle
```

## Inside-Out Development Approach

### Phase 1: Pure Domain Foundation
**Philosophy:** Build the core business logic with zero external dependencies

#### Core Domain Models
**Deliverables:**
- Recipe aggregate with business rules
- Ingredient entities (Hop, Fermentable, Yeast)
- Inclusion value objects with type-specific behavior
- Domain events for state changes

**Key Principles:**
- No database, no HTTP, no external crates beyond std
- Rich domain models with encapsulated business logic
- Comprehensive unit tests for all business rules
- Domain events as pure data structures

**Example Domain Model:**
```rust
// src/domain/recipe/aggregate.rs
pub struct Recipe {
    id: RecipeId,
    user_id: UserId,
    name: String,
    description: String,
    batch_size: BatchSize,
    mash_efficiency: Percentage,
    target_og: SpecificGravity,
    hop_inclusions: Vec<HopInclusion>,
    fermentable_inclusions: Vec<FermentableInclusion>,
    uncommitted_events: Vec<DomainEvent>,
    version: u64,
}

impl Recipe {
    pub fn calculate_original_gravity(&self) -> SpecificGravity {
        // Pure business logic - no external dependencies
        let total_points = self.fermentable_inclusions
            .iter()
            .map(|inclusion| inclusion.gravity_contribution(&self.batch_size))
            .sum::<f64>();

        SpecificGravity::from_points(total_points)
    }

    pub fn add_hop_inclusion(&mut self, inclusion: HopInclusion) -> Result<(), DomainError> {
        // Business rule validation
        self.validate_hop_inclusion(&inclusion)?;
        self.hop_inclusions.push(inclusion.clone());

        // Record domain event
        self.record_event(HopInclusionAdded {
            recipe_id: self.id.clone(),
            inclusion,
            added_at: Utc::now(),
        });

        Ok(())
    }

    pub fn take_uncommitted_events(&mut self) -> Vec<DomainEvent> {
        std::mem::take(&mut self.uncommitted_events)
    }
}
```

#### Business Calculations & Validation
**Deliverables:**
- IBU calculation engine with Tinseth formula
- SRM color calculation with Morey equation
- ABV calculation with attenuation modeling
- Domain validation rules and error types

**Testing Strategy:**
- Property-based testing for calculation accuracy
- Unit tests for edge cases and business rules
- No mocks needed - pure functions

### Phase 2: Repository Ports Definition
**Philosophy:** Define abstract interfaces without implementation concerns

#### Deliverables:
- Event store trait definitions
- Query interfaces for read operations
- Authorization policy interfaces
- Error types for repository operations

**Key Principles:**
- Traits only - no concrete implementations
- Focus on domain needs, not database structure
- Async interfaces where appropriate
- Clear separation of event store and read models

**Example Repository Port:**
```rust
// src/application/ports/repositories.rs
#[async_trait]
pub trait EventStore {
    async fn append_events(
        &self,
        stream_id: &str,
        expected_version: u64,
        events: Vec<DomainEvent>,
        metadata: EventMetadata,
    ) -> Result<(), EventStoreError>;

    async fn load_events(
        &self,
        stream_id: &str,
        from_version: u64,
    ) -> Result<Vec<StoredEvent>, EventStoreError>;
}

#[async_trait]
pub trait ReadModelStore {
    async fn get_recipe(&self, id: &RecipeId) -> Result<Option<RecipeReadModel>, ReadModelError>;
    async fn search_recipes(&self, criteria: &RecipeSearchCriteria) -> Result<Vec<RecipeReadModel>, ReadModelError>;
    async fn update_recipe_projection(&self, recipe: &RecipeReadModel) -> Result<(), ReadModelError>;
}
```

### Phase 3: Application Services & CQRS
**Philosophy:** Orchestrate domain operations through use cases

#### Command Handlers
**Deliverables:**
- Command types for all write operations
- Command handlers with domain orchestration
- Command validation and error handling
- Authorization integration

**Example Command Handler:**
```rust
// src/application/commands/recipe/create_recipe.rs
pub struct CreateRecipeCommand {
    pub user_id: UserId,
    pub name: String,
    pub description: String,
    pub batch_size_gallons: f64,
    pub target_og: f64,
}

pub struct CreateRecipeHandler<E: EventStore, A: AuthorizationPolicy> {
    event_store: E,
    authorization: AuthorizationService<A>,
}

impl<E: EventStore, A: AuthorizationPolicy> CreateRecipeHandler<E, A> {
    pub async fn handle(
        &self,
        user_context: &UserContext,
        command: CreateRecipeCommand,
    ) -> Result<RecipeId, ApplicationError> {
        // Authorize command
        self.authorization.authorize_command(user_context, &command).await?;

        // Create new recipe aggregate
        let recipe_id = RecipeId::generate();
        let mut recipe = Recipe::new(
            recipe_id.clone(),
            command.user_id,
            command.name,
            command.description,
            BatchSize::gallons(command.batch_size_gallons)?,
            SpecificGravity::new(command.target_og)?,
        )?;

        // Get uncommitted events from aggregate
        let events = recipe.take_uncommitted_events();

        // Store events in event store
        self.event_store.append_events(
            &recipe_id.to_string(),
            0, // Expected version (new aggregate)
            events,
            EventMetadata {
                user_id: user_context.user_id.clone(),
                correlation_id: CorrelationId::generate(),
                causation_id: None,
                agent_session_id: user_context.agent_session_id.clone(),
            },
        ).await?;

        Ok(recipe_id)
    }
}
```

#### Query Handlers & Read Models
**Deliverables:**
- Query types for all read operations
- Query handlers with read model projection
- Read model types optimized for UI consumption
- Comprehensive query testing

### Phase 4: Infrastructure Adapters
**Philosophy:** Implement concrete adapters for external systems

#### MongoDB Event Store Implementation
**Deliverables:**
- MongoDB connection and configuration
- Event store document models and indexes
- Event store implementation with proper error handling
- Snapshot storage for aggregate optimization

#### Read Model Projections
**Deliverables:**
- MongoDB read model collections
- Event handlers for read model updates
- Query repository implementations
- Read model synchronization logic

#### In-Memory Testing Implementations
**Deliverables:**
- In-memory event store for testing
- In-memory read model store for testing
- Test fixtures and data builders
- Integration test infrastructure

### Phase 5: Web Layer & API
**Philosophy:** Expose application services through HTTP API

#### Axum HTTP Infrastructure
**Deliverables:**
- Axum server setup with middleware
- Request/response types and serialization
- JWT-based authentication middleware
- Authorization integration with application layer

#### API Endpoints & Compatibility
**Deliverables:**
- REST API endpoints matching Django API
- Request validation and response formatting
- API versioning strategy
- Compatibility layer for frontend

### Phase 6: Agent System Integration
**Philosophy:** Integrate AI agents with domain services

#### Rig Agent Framework Setup
**Deliverables:**
- Rig agent configuration and setup
- Tool interfaces for domain operations
- Conversation memory implementation
- Agent prompt engineering

#### Agent-Domain Integration
**Deliverables:**
- Agent tools calling application services through authorization layer
- Real-time collaboration features
- WebSocket integration for live updates
- Agent conversation persistence with user context

### Phase 7: Data Migration & Testing
**Philosophy:** Ensure data integrity and system reliability

#### Data Migration Pipeline
**Deliverables:**
- PostgreSQL to MongoDB event store migration scripts
- Data validation and integrity checks
- Migration rollback procedures
- Performance optimization

#### System Integration & Deployment
**Deliverables:**
- End-to-end integration testing
- Performance benchmarking and optimization
- Deployment pipeline and monitoring
- Production readiness checklist

## Success Metrics & Validation

### Performance Targets
- **API Response Time:** < 100ms for 95th percentile
- **Recipe Calculation Time:** < 10ms for complex recipes
- **Agent Response Time:** < 2s for tool-based responses
- **Memory Usage:** < 50% of Django equivalent

### Quality Metrics
- **Test Coverage:** > 90% for domain and application layers
- **Type Safety:** Zero runtime type errors in production
- **Error Handling:** Graceful degradation for all failure modes
- **Documentation:** Complete API documentation and developer guides

### Migration Validation
- **Data Integrity:** 100% data preservation during migration
- **API Compatibility:** Zero breaking changes for frontend
- **Feature Parity:** All Django features replicated in Rust
- **User Experience:** No degradation in application functionality

## Risk Mitigation Strategies

### Technical Risks
- **MongoDB Learning Curve:** Prototype document design early
- **Rig Framework Maturity:** Have fallback to direct LLM integration
- **Performance Assumptions:** Continuous benchmarking throughout development
- **CQRS Complexity:** Start simple, evolve based on actual needs

### Migration Risks
- **Data Loss:** Multiple backup strategies and validation checkpoints
- **Downtime:** Blue-green deployment with rollback capability
- **Feature Regression:** Comprehensive acceptance testing
- **Team Knowledge:** Pair programming and knowledge sharing sessions

### Timeline Risks
- **Scope Creep:** Strict adherence to inside-out methodology
- **Technical Debt:** Regular refactoring and code review
- **Integration Issues:** Early and frequent integration testing
- **Resource Constraints:** Parallel development tracks where possible

## Conclusion

This refined migration plan follows inside-out development principles to ensure each phase delivers tangible value while building toward the complete system. The event-sourced CQRS implementation provides complete audit trails and optimal read/write separation for the brewing domain, while the hexagonal architecture ensures clean separation of concerns and testability.

The authorization strategy ensures consistent security across both REST API and agent access patterns by implementing policy-based authorization at the application layer. The event sourcing approach provides complete traceability of recipe modifications, supporting both collaborative development and agent learning from recipe evolution patterns.

Each phase builds incrementally on the previous work, ensuring continuous validation of design decisions and early identification of potential issues. The elimination of `Box<dyn>` patterns in favor of generic type parameters provides better performance and compile-time guarantees while maintaining flexibility.
