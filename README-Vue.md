# Vue.js Frontend Integration

This project now uses Vue.js 3 for the recipe collaboration frontend, served by Django.

## Architecture

- **Django Backend**: Serves the Vue app as static files and provides API endpoints
- **Vue.js 3 Frontend**: Reactive frontend for the recipe collaboration page
- **Vite Build System**: Builds Vue components to static files that Djan<PERSON> serves

## Development Setup

### 1. Install Node.js Dependencies

```bash
npm install
```

### 2. Build Frontend for Production

```bash
# Build Vue app to static files
npm run build

# Or use Django management command
python manage.py build_frontend
```

### 3. Development Mode

```bash
# Run Vue dev server with hot reload
npm run dev

# Or use Django management command
python manage.py build_frontend --dev
```

## How It Works

1. **Django Template**: `templates/recipe_collaboration.html` loads the Vue app
2. **Vue Entry Point**: `src/recipe-collaboration.js` creates and mounts the Vue app
3. **Build Process**: Vite builds Vue components to `static/js/dist/`
4. **Django Serves**: Django serves the built files as static assets

## File Structure

```
├── package.json              # Node.js dependencies
├── vite.config.js            # Vite build configuration
├── src/
│   ├── recipe-collaboration.js    # Vue app entry point
│   ├── components/
│   │   ├── RecipeCollaborationApp.vue  # Main app component
│   │   ├── RecipePanel.vue             # Recipe display component
│   │   └── ChatPanel.vue               # Chat interface component
│   └── composables/
│       ├── useRecipe.js               # Recipe data management
│       └── useChat.js                 # Chat functionality
├── static/js/dist/           # Built Vue files (generated)
└── templates/
    └── recipe_collaboration.html     # Django template
```

## Benefits

- **Reactive UI**: Automatic updates when recipe data changes
- **Clean Architecture**: Separation of concerns between Django and Vue
- **Development Experience**: Hot reload during development
- **Production Ready**: Optimized builds for production
- **Django Integration**: Seamless integration with existing Django auth/routing

## Next Steps

1. Create Vue components (RecipePanel, ChatPanel, etc.)
2. Implement reactive data management with composables
3. Add WebSocket support for real-time updates
4. Build and test the complete reactive frontend
