"""
Django admin configuration for the hoplogic core application.

This module registers all models with the Django admin interface with
appropriate configurations for easy management.
"""

from django.contrib import admin

from .models import (
    BoilHopInclusion,
    DryHopInclusion,
    Fermentable,
    FermentableInclusion,
    FermentationPhase,
    FirstWortHopInclusion,
    FlameoutHopInclusion,
    Hop,
    MashStep,
    Recipe,
    User,
    WaterProfile,
    WhirlpoolHopInclusion,
    Yeast,
    YeastInclusion,
)

admin.site.register(User)
admin.site.register(Hop)
admin.site.register(Fermentable)
admin.site.register(BoilHopInclusion)
admin.site.register(FirstWortHopInclusion)
admin.site.register(FlameoutHopInclusion)
admin.site.register(WhirlpoolHopInclusion)
admin.site.register(DryHopInclusion)
admin.site.register(FermentableInclusion)
admin.site.register(WaterProfile)
admin.site.register(Yeast)
admin.site.register(YeastInclusion)
admin.site.register(MashStep)
admin.site.register(FermentationPhase)
admin.site.register(Recipe)
