from collections.abc import Callable
from dataclasses import dataclass
from enum import Enum
from os import environ

from langchain_core.documents import Document
from langchain_core.embeddings import Embeddings
from langchain_core.vectorstores import InMemoryVectorStore, VectorStore
from langchain_ollama import OllamaEmbeddings
from langchain_openai.embeddings import OpenAIEmbeddings


class EmbeddingVendor(Enum):
    OLLAMA = "ollama"
    OPENAI = "openai"


@dataclass(frozen=True, slots=True)
class EmbeddingConfiguration:
    model_provider: EmbeddingVendor
    model_name: str
    base_url: str | None
    api_key: str

    @classmethod
    def from_settings(cls) -> "EmbeddingConfiguration":
        return cls(
            model_provider=EmbeddingVendor.OPENAI,
            base_url=None,
            model_name="text-embedding-ada-002",
            api_key=environ.get("OPENAI_API_KEY", ""),
        )

    def make_embeddings(self) -> Embeddings:
        if self.model_provider == EmbeddingVendor.OLLAMA:
            return OllamaEmbeddings(model=self.model_name, base_url=self.base_url)
        elif self.model_provider == EmbeddingVendor.OPENAI:
            return OpenAIEmbeddings(
                model=self.model_name,
                base_url=self.base_url,
                api_key=self.api_key,
            )
        else:
            raise ValueError(f"Unknown model provider: {self.model_provider}")


def provide_vector_store(
    conf: EmbeddingConfiguration, hydrate: Callable[[Embeddings], list[Document]]
) -> VectorStore:
    conf = conf or EmbeddingConfiguration.from_settings()
    embeddings = conf.make_embeddings()
    vector_store = InMemoryVectorStore(embeddings)
    empty_store = len(vector_store.similarity_search("the", k=1)) == 0
    if empty_store:
        documents = hydrate(embeddings)
        vector_store.add_documents(documents)
    return vector_store
