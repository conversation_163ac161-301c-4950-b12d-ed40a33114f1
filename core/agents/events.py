import json
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any

from langchain_core.messages import ToolMessage


@dataclass(frozen=True, slots=True)
class ToolCall:
    """Represents a tool call made by the agent.

    This class is used to represent a tool call has made by the agent.
    It contains the ID of the tool call, the name of the tool, and the arguments passed
    to the tool. The arguments are represented as a dictionary because the tool
    arguments are represented as a JSON string. The keys of the dictionary are the
    names of the arguments, and the values are the values of the arguments.

    Attributes:
        id: The ID of the tool call.
        tool_name: The name of the tool.
        args: The arguments passed to the tool.
    """

    id: str
    tool_name: str
    args: dict[str, Any]

    @classmethod
    def from_call_data(cls, data: dict[str, Any]) -> "ToolCall":
        """Parses tool call data from the output of the agent.

        The tool call data is a dictionary with the following structure:
        {
            "id": "call_id",
            "function": {
                "name": "tool_name",
                "arguments": '{"arg1": "value1", "arg2": "value2"}'
            }
        }

        Args:
            data: The tool call data to parse.

        Returns:
            A ToolCall object representing the tool call.
        """
        id = data["id"]
        tool_name = data["function"]["name"]
        args = json.loads(data["function"]["arguments"])
        return cls(id, tool_name, args)


@dataclass(frozen=True, slots=True)
class ToolOutput:
    """Represents the output of a tool call.

    This class is used to represent the output of a tool call. It contains the
    ID of the tool call and the output of the tool.

    Attributes:
        id: The ID of the tool call.
        output: The output of the tool.
    """

    id: str
    output: str

    @staticmethod
    def from_message(message: ToolMessage) -> "ToolOutput":
        """Parses a tool output from a tool message.

        Args:
            message: The tool message to parse.

        Returns:
            A ToolOutput object representing the tool output.
        """
        id = message.tool_call_id
        output = message.content
        return ToolOutput(id, output)


class AgentEvent(ABC):
    """Represents an event generated by the agent.

    This class is used to represent an event generated by the agent. It contains
    the content of the event and the type of the event. The type of the event is
    represented by the class name of the event.
    """

    def __str__(self) -> str:
        return self.display()

    @abstractmethod
    def display(self) -> str:
        """Returns a string representation of the event."""
        pass

    @classmethod
    def from_output(cls, data: dict[str, Any]) -> "AgentEvent":
        """Parses an agent event from the output of the agent.

        The system parses output in one of three ways:
        1. A message from the agent to the user
        2. A tool call from the agent
        3. A tool output from the agent

        The data structure for each of these is different, so we need to parse them
        differently. The data structure for each of these is as follows:

        1. A message from the agent to the user:
            {
                "agent": {
                    "messages": [
                        {
                            "content": "message content",
                            "additional_kwargs": {}
                        }
                    ]
                }
            }

        2. A tool call from the agent:
            {
                "agent": {
                    "messages": [
                        {
                            "content": "",
                            "additional_kwargs": {
                                "tool_calls": [
                                    {
                                        "id": "call_id",
                                        "function": {
                                            "name": "tool_name",
                                            "arguments": '{"arg1": "value1", "arg2": "value2"}'
                                        }
                                    }
                                ]
                            }
                        }
                    ]
                }
            }

        3. A tool output from the agent:
            {
                "tools": {
                    "messages": [
                        {
                            "tool_call_id": "call_id",
                            "content": "tool output"
                        }
                    ]
                }
            }

        Args:
            data: The output of the agent to parse.

        Returns:
            An AgentEvent object representing the event.
        """

        messages = data.get("agent", {}).get("messages", [])
        for message in messages:
            # If the message has content, return an AI message event
            if message.content != "":
                return AIMessageEvent(message.content)

            # If the message has tool calls, return a tool call event
            tool_calls = message.additional_kwargs.get("tool_calls", [])
            if tool_calls:
                return ToolCallEvent.from_tool_calls(tool_calls)

        tool_data = data.get("tools", {}).get("messages", [])
        if tool_data:
            return ToolOutputEvent.from_tool_messages(tool_data)

        raise ValueError("No message found")


@dataclass(frozen=True, slots=True)
class ToolCallEvent(AgentEvent):
    """Represents a tool call event.

    This class is used to represent a tool call event. It contains the tool calls
    made by the agent.

    Attributes:
        tool_calls: The tool calls made by the agent.
    """

    tool_calls: list[ToolCall]

    @staticmethod
    def from_tool_calls(tool_calls: list[dict[str, Any]]) -> "ToolCallEvent":
        """Parses a tool call event from the output of the agent.

        Args:
            tool_calls: The tool calls to parse.

        Returns:
            A ToolCallEvent object representing the tool call event.
        """
        calls = [ToolCall.from_call_data(call) for call in tool_calls]
        return ToolCallEvent(calls)

    def display(self) -> str:
        if len(self.tool_calls) == 1:
            return "Calling 1 tool"
        return f"Calling {len(self.tool_calls)} tools"


@dataclass(frozen=True, slots=True)
class ToolOutputEvent(AgentEvent):
    """Represents a tool output event.

    This class is used to represent a tool output event. It contains the tool outputs
    from the agent. The tool outputs are represented as a list of ToolOutput objects.
    Each ToolOutput object contains the ID of the tool call and the output of the tool.
    The ID of the tool call is used to match the tool output to the tool call.
    The tool output is a string representing the output of the tool.

    The tool output event is generated when the agent has made one or more tool calls
    and the tool(s) have returned output.

    Attributes:
        tool_outputs: The tool outputs from the agent.
    """

    tool_outputs: list[ToolOutput]

    @staticmethod
    def from_tool_messages(tool_messages: list[ToolMessage]) -> "ToolOutputEvent":
        """Parses a tool output event from the output of the agent.

        Args:
            tool_messages: The tool messages to parse.

        Returns:
            A ToolOutputEvent object representing the tool output event.
        """
        return ToolOutputEvent(
            [ToolOutput.from_message(message) for message in tool_messages]
        )

    def display(self) -> str:
        return f"{len(self.tool_outputs)} tool calls completed"


@dataclass(frozen=True, slots=True)
class AIMessageEvent(AgentEvent):
    """Represents an AI message event.

    The AI message event is generated when the agent has generated a message to send
    to the user. The content of the message is represented as a string. The content
    of the message is the raw string output from the agent. The content of the
    message is not parsed in any way.

    Attributes:
        content: The content of the message.
    """

    content: str

    def display(self) -> str:
        return self.content
