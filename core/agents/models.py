from dataclasses import dataclass
from enum import Enum
from os import environ

from langchain_core.language_models import BaseLanguageModel
from langchain_ollama.chat_models import ChatOllama
from langchain_openai.chat_models import ChatOpenAI


class ChatModelProvider(Enum):
    OLLAMA = "ollama"
    OPENAI = "openai"


@dataclass(frozen=True, slots=True)
class ModelConfiguration:
    base_url: str | None
    api_key: str
    model_name: str
    model_provider: ChatModelProvider
    temperature: float

    @classmethod
    def from_settings(cls) -> "ModelConfiguration":
        return cls(
            model_provider=ChatModelProvider.OPENAI,
            base_url=None,
            api_key=environ.get("OPENAI_API_KEY", ""),
            model_name="gpt-4o",
            temperature=0.2,
        )

    def make_model(self) -> BaseLanguageModel:
        if self.model_provider == ChatModelProvider.OLLAMA:
            return ChatOllama(
                model=self.model_name,
                base_url=self.base_url,
                temperature=self.temperature,
            )

        if self.model_provider == ChatModelProvider.OPENAI:
            return ChatOpenAI(
                model=self.model_name,
                temperature=self.temperature,
                openai_api_key=self.api_key,
                openai_api_base=self.base_url,
            )

        raise ValueError(f"Unknown model provider: {self.model_provider}")
