"""
Schemas and types for scraped brewing ingredient data.
"""

from abc import ABC, abstractmethod
from enum import Enum

from pydantic import BaseModel, Field, validator


class IngredientType(str, Enum):
    """Types of brewing ingredients."""

    FERMENTABLES = "fermentables"
    YEASTS = "yeasts"
    HOPS = "hops"


class FermentableType(str, Enum):
    """Fermentable type choices matching Django model."""

    BASE_MALT = "BASE"
    SPECIALTY_MALT = "SPECIALTY"
    CRYSTAL_CARAMEL = "CRYSTAL"
    ROASTED = "ROASTED"
    ADJUNCT = "ADJUNCT"
    EXTRACT = "EXTRACT"
    CARAMEL = "CARAMEL"
    RAW = "RAW"


class YeastType(str, Enum):
    """Yeast type choices matching Django model."""

    ALE = "ALE"
    LAGER = "LAGER"
    WILD = "WILD"
    BRETT = "BRETT"
    BACTERIA = "BACTERIA"
    BLEND = "BLEND"
    SCOBY = "SCOBY"
    WINE = "WINE"
    MEAD = "MEAD"
    CIDER = "CIDER"
    DISTILLING = "DISTILLING"
    BRETTANOMYCES = "BRETTANOMYCES"
    SPECIALTY = "SPECIALTY"


class YeastForm(str, Enum):
    """Yeast form choices matching Django model."""

    DRY = "DRY"
    LIQUID = "LIQUID"
    SLANT = "SLANT"
    CULTURE = "CULTURE"


class IngredientData(BaseModel, ABC):
    """Base class for all ingredient data."""

    name: str = Field(..., description="Name of the ingredient")

    @abstractmethod
    def get_ingredient_type(self) -> IngredientType:
        """Return the type of this ingredient."""
        pass


class FermentableData(IngredientData):
    """Schema for fermentable ingredient data."""

    country_of_origin: str = Field(default="")
    notes: str = Field(default="")
    fermentable_type: FermentableType = Field(default=FermentableType.BASE_MALT)
    extract_potential_ppg: float = Field(default=37.0)
    color_lovibond: float = Field(default=2.0)
    requires_mashing: bool = Field(default=True)

    def get_ingredient_type(self) -> IngredientType:
        return IngredientType.FERMENTABLES

    @validator("extract_potential_ppg")
    def validate_extract_potential(cls, v):
        if v < 0 or v > 50:
            raise ValueError("Extract potential must be between 0 and 50 PPG")
        return v

    @validator("color_lovibond")
    def validate_color(cls, v):
        if v < 0:
            raise ValueError("Color must be non-negative")
        return v


class YeastData(IngredientData):
    """Schema for yeast strain data."""

    laboratory: str = Field(default="")
    product_id: str = Field(default="")
    yeast_type: YeastType = Field(default=YeastType.ALE)
    yeast_form: YeastForm = Field(default=YeastForm.DRY)
    min_temperature_fahrenheit: float = Field(default=65.0)
    max_temperature_fahrenheit: float = Field(default=75.0)
    attenuation_percent: float = Field(default=75.0)
    flocculation: str = Field(default="MEDIUM")
    alcohol_tolerance_percent: float | None = Field(None)
    description: str = Field(default="")

    def get_ingredient_type(self) -> IngredientType:
        return IngredientType.YEASTS


class HopData(IngredientData):
    """Schema for hop ingredient data."""

    country_of_origin: str = Field(default="")
    notes: str = Field(default="")
    alpha_acid: float = Field(default=0.0)
    beta_acid: float = Field(default=0.0)
    aroma: bool = Field(default=False)
    bittering: bool = Field(default=False)

    def get_ingredient_type(self) -> IngredientType:
        return IngredientType.HOPS


# Type alias for any ingredient data
AnyIngredientData = FermentableData | YeastData | HopData


class ScrapingResult(BaseModel):
    """Result of a scraping operation."""

    ingredient_type: IngredientType
    source_url: str
    ingredients: list[AnyIngredientData] = Field(default_factory=list)
    success: bool = True
    errors: list[str] = Field(default_factory=list)
