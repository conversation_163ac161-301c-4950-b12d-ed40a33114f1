"""
Ultra-simple LLM-driven ingredient scraper.

Let the LLM do ALL the work - no manual filtering, no complex logic.
"""

import json
import logging

import requests
from bs4 import BeautifulSoup
from langchain_core.messages import SystemMessage
from langchain_core.rate_limiters import InMemoryRateLimiter
from langchain_core.tools import tool
from langgraph.prebuilt import create_react_agent

from ..models import ModelConfiguration
from .schemas import (
    AnyIngredientData,
    FermentableData,
    HopData,
    IngredientType,
    ScrapingResult,
    YeastData,
)

logger = logging.getLogger(__name__)


class SimpleScraper:
    """Ultra-simple scraper that lets LLM do everything."""

    def __init__(self, model_config: ModelConfiguration | None = None):
        self.model_config = model_config or ModelConfiguration.from_settings()
        self.session = requests.Session()
        self.session.headers.update(
            {"User-Agent": "HopLogic-Scraper/1.0 (Educational/Research Purpose)"}
        )

        # Rate limiter
        self.rate_limiter = InMemoryRateLimiter(
            requests_per_second=0.2,  # Very conservative
            check_every_n_seconds=0.1,
            max_bucket_size=3,
        )

    def scrape_ingredients(
        self, url: str, ingredient_type: IngredientType, max_ingredients: int = 20
    ) -> ScrapingResult:
        """Let LLM find and extract all ingredients from a page (with pagination support)."""
        result = ScrapingResult(ingredient_type=ingredient_type, source_url=url)

        try:
            all_ingredients = []
            page_num = 1
            max_pages = min(10, (max_ingredients // 20) + 1)  # Reasonable limit

            while len(all_ingredients) < max_ingredients and page_num <= max_pages:
                # Construct page URL
                if page_num == 1:
                    page_url = url
                else:
                    # Handle pagination (common patterns)
                    if "?" in url:
                        page_url = f"{url}&page={page_num}"
                    else:
                        page_url = f"{url}?page={page_num}"

                logger.info(f"Scraping page {page_num}: {page_url}")

                # Get the page
                response = self.session.get(page_url, timeout=30)
                response.raise_for_status()

                # Let LLM extract from this page
                page_ingredients = self._llm_extract_all_ingredients(
                    page_url,
                    response.text,
                    ingredient_type,
                    min(
                        25, max_ingredients - len(all_ingredients)
                    ),  # Get up to 25 per page
                )

                if not page_ingredients:
                    logger.info(f"No ingredients found on page {page_num}, stopping")
                    break

                all_ingredients.extend(page_ingredients)
                logger.info(
                    f"Page {page_num}: Found {len(page_ingredients)} ingredients (total: {len(all_ingredients)})"
                )

                page_num += 1

            result.ingredients = all_ingredients[:max_ingredients]
            result.success = len(result.ingredients) > 0

            logger.info(
                f"LLM extracted {len(result.ingredients)} {ingredient_type.value} from {url} ({page_num-1} pages)"
            )

        except Exception as e:
            result.success = False
            result.errors.append(str(e))
            logger.error(f"Scraping failed: {e}")

        return result

    def _llm_extract_all_ingredients(
        self, url: str, html: str, ingredient_type: IngredientType, max_ingredients: int
    ) -> list[AnyIngredientData]:
        """Let LLM find all ingredients on the page and extract their data."""

        extracted_ingredients = []

        @tool
        def extract_ingredients(ingredients_json: str) -> str:
            """Extract all ingredients found on the page as JSON array."""
            nonlocal extracted_ingredients
            try:
                ingredients_data = json.loads(ingredients_json)

                for item in ingredients_data:
                    if ingredient_type == IngredientType.FERMENTABLES:
                        ingredient = FermentableData(**item)
                    elif ingredient_type == IngredientType.YEASTS:
                        ingredient = YeastData(**item)
                    elif ingredient_type == IngredientType.HOPS:
                        ingredient = HopData(**item)
                    else:
                        continue

                    extracted_ingredients.append(ingredient)

                return (
                    f"Successfully extracted {len(extracted_ingredients)} ingredients"
                )

            except Exception as e:
                logger.error(f"Extraction error: {e}")
                return f"Error: {e}"

        # Clean HTML
        soup = BeautifulSoup(html, "html.parser")
        for script in soup(["script", "style", "nav", "footer", "header"]):
            script.decompose()

        text_content = soup.get_text(separator="\n", strip=True)[:20000]  # Larger limit

        # Create agent
        llm = self.model_config.make_model().with_config(
            {"rate_limiter": self.rate_limiter}
        )
        agent = create_react_agent(llm, [extract_ingredients])

        # Get schema
        schema_example = self._get_schema_example(ingredient_type)

        prompt = f"""You are an expert brewer. Find ALL {ingredient_type.value} on this webpage and extract their complete data.

TASK: Scan the entire page content and identify every single {ingredient_type.value.rstrip('s')} mentioned. Extract complete brewing data for each one.

CRITICAL RULES:
1. Look for REAL ingredient names (e.g., "WLP001 California Ale", "Cascade", "Pilsner Malt")
2. IGNORE website navigation, menus, and generic text
3. Extract ALL available brewing specifications for each ingredient
4. Return a JSON array with up to {max_ingredients} ingredients
5. If you can't find specific data, use reasonable defaults

JSON format (return array of these objects):
{schema_example}

URL: {url}
Page content: {text_content}

Use extract_ingredients tool with a JSON array of all {ingredient_type.value} found."""

        try:
            agent.invoke({"messages": [SystemMessage(content=prompt)]})
            return extracted_ingredients[:max_ingredients]

        except Exception as e:
            logger.error(f"LLM extraction failed: {e}")
            return []

    def _get_schema_example(self, ingredient_type: IngredientType) -> str:
        """Get schema example."""
        if ingredient_type == IngredientType.FERMENTABLES:
            return """{
    "name": "Pilsner Malt",
    "country_of_origin": "Germany",
    "notes": "Light, crisp base malt",
    "fermentable_type": "BASE",
    "extract_potential_ppg": 37.0,
    "color_lovibond": 1.8,
    "requires_mashing": true
}"""
        elif ingredient_type == IngredientType.YEASTS:
            return """{
    "name": "WLP001 California Ale",
    "laboratory": "White Labs",
    "product_id": "WLP001",
    "yeast_type": "ALE",
    "yeast_form": "LIQUID",
    "min_temperature_fahrenheit": 68.0,
    "max_temperature_fahrenheit": 73.0,
    "attenuation_percent": 76.0,
    "flocculation": "MEDIUM",
    "alcohol_tolerance_percent": 11.0,
    "description": "Clean fermenting ale yeast"
}"""
        elif ingredient_type == IngredientType.HOPS:
            return """{
    "name": "Cascade",
    "country_of_origin": "USA",
    "notes": "Citrus and floral aroma",
    "alpha_acid": 5.5,
    "beta_acid": 6.0,
    "aroma": true,
    "bittering": true
}"""
        return "{}"

    def export_to_csv(self, result: ScrapingResult, filename: str):
        """Export results to CSV."""
        import csv
        from pathlib import Path

        if not result.ingredients:
            logger.warning("No ingredients to export")
            return

        fieldnames = list(result.ingredients[0].dict().keys())

        Path(filename).parent.mkdir(parents=True, exist_ok=True)

        with open(filename, "w", newline="", encoding="utf-8") as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for ingredient in result.ingredients:
                writer.writerow(ingredient.dict())

        logger.info(f"Exported {len(result.ingredients)} ingredients to {filename}")


def create_simple_scraper(
    model_config: ModelConfiguration | None = None,
) -> SimpleScraper:
    """Factory function."""
    return SimpleScraper(model_config)
