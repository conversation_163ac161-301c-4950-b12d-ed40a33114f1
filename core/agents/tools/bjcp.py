from logging import getLogger

from langchain.tools import Tool
from langchain.tools.retriever import create_retriever_tool
from langchain_community.document_loaders import PyPDFLoader
from langchain_core.documents import Document
from langchain_core.embeddings import Embeddings

from ..embedding import EmbeddingConfiguration, provide_vector_store

LOGGER = getLogger(__name__)


def _hydrate_bjcp(_: Embeddings) -> list[Document]:
    LOGGER.info("Hydrating Vector Store with BJCP Guidelines")

    loader = PyPDFLoader("bjcp.pdf")
    return loader.load()


def create_bjcp_retriever_tool(
    embedding_config: EmbeddingConfiguration | None = None,
) -> Tool:
    embedding_config = embedding_config or EmbeddingConfiguration.from_settings()
    vector_store = provide_vector_store(embedding_config, _hydrate_bjcp)
    retriever = vector_store.as_retriever()
    return create_retriever_tool(
        retriever=retriever,
        name="search_bjcp_guidelines",
        description="Allows you to search for information about beer styles from the BJCP guidelines.",
    )
