"""Dynamically Build Agent Tools from Services.

Hoplogic uses the service pattern for managing business logic. This means that
all business logic is encapsulated in service classes. This module provides a
factory for generating agent tools from these service methods dynamicallly.

In order to mark a service method as accessible to agents, add the
`@agent_accessible` decorator to the method. This decorator takes two
optional arguments:

1. description: The description of the tool for the agent. If not provided, uses the method's docstring.
2. serializer: The serializer to use for rendering the result. If not provided, the result is returned as-is.

The `@agent_accessible` decorator adds metadata to the method that is used
by the `ServiceToolFactory` to generate the agent tool.

Using the `ServiceToolFactory`, you can generate a list of tools from a service class. For convenience,
you can use the `create_service_tools` static method to generate all tools from a service class in one call.
For example:

    from .tools.factory import ServiceToolFactory

    class MyService:
        @agent_accessible(serializer=FermentableSerializer)
        def get_fermentable_by_id(self, fermentable_id: str) -> Optional[Fermentable]:
            \"\"\"Get a fermentable by ID.\"\"\"
            return self.repository.get_by_id(fermentable_id)

    # Elsewhere...
    tools = ServiceToolFactory.create_service_tools(MyService)

This will generate a tool that can be used by an agent to call the `get_fermentable_by_id` method.
"""

import inspect
import logging
from collections.abc import Callable
from dataclasses import dataclass

from langchain_core.runnables.config import var_child_runnable_config
from langchain_core.tools import StructuredTool, Tool
from pydantic import create_model
from rest_framework.renderers import JSONRenderer
from rest_framework.serializers import ModelSerializer

logger = logging.getLogger(__name__)

METADATA_KEY = "_tool_metadata"
RECIPE_ID_PARAM = "recipe_id"
USER_PARAM = "user"


def _get_recipe_id_from_context():
    config = var_child_runnable_config.get()
    return config["configurable"]["thread_id"]


def _get_user_from_context():
    """Get user from agent configuration."""
    config = var_child_runnable_config.get()
    user_id = config.get("configurable", {}).get("user_id")

    if user_id:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        try:
            return User.objects.get(id=user_id)
        except User.DoesNotExist:
            pass
    return None


@dataclass
class ParameterInfo:
    """Information about a method parameter for tool generation."""

    name: str
    param: inspect.Parameter
    type: type
    type_str: str
    required: bool

    @classmethod
    def from_parameter(
        cls, param_name: str, param: inspect.Parameter
    ) -> "ParameterInfo":
        """Factory method to create ParameterInfo from inspect.Parameter."""
        param_type = (
            param.annotation if param.annotation != inspect.Parameter.empty else str
        )
        param_type_str = getattr(param_type, "__name__", str(param_type))
        is_required = param.default == inspect.Parameter.empty

        return cls(
            name=param_name,
            param=param,
            type=param_type,
            type_str=param_type_str,
            required=is_required,
        )

    def is_recipe_id_param(self) -> bool:
        """Check if this parameter is the recipe_id parameter."""
        return self.name == RECIPE_ID_PARAM

    def is_user_param(self) -> bool:
        """Check if this parameter is the user parameter."""
        return self.name == USER_PARAM

    def to_pydantic_field(self):
        """Convert this parameter to a Pydantic field definition."""
        from pydantic import Field

        if self.required:
            return (self.type, Field(description=f"Parameter {self.name}"))
        else:
            return (
                self.type,
                Field(default=self.param.default, description=f"Parameter {self.name}"),
            )

    def to_doc_string(self) -> str:
        """Convert this parameter to documentation string."""
        status = "required" if self.required else "optional"
        return f"- {self.name} ({self.type_str}): {status}"


@dataclass
class ToolMetadata:
    """Metadata for tool generation from service methods."""

    description: str | None = None
    serializer: ModelSerializer | None = None

    def build_tool(self, method: Callable) -> Tool:
        orig_sig = inspect.signature(method)
        param_info = self._extract_parameter_info(orig_sig)
        args_schema = self._create_args_schema(method.__name__, param_info)
        enhanced_description = self._create_enhanced_description(param_info)
        tool_function = self._create_tool_function(method, orig_sig)

        return StructuredTool.from_function(
            func=tool_function,
            name=method.__name__,
            description=enhanced_description,
            args_schema=args_schema,
        )

    def _extract_parameter_info(self, sig: inspect.Signature) -> list[ParameterInfo]:
        """Extract parameter information from method signature in a single pass."""
        return [
            ParameterInfo.from_parameter(name, param)
            for name, param in sig.parameters.items()
            if name != "self"
        ]

    def _create_args_schema(self, method_name: str, param_info: list[ParameterInfo]):
        fields = {
            info.name: info.to_pydantic_field()
            for info in param_info
            if not info.is_recipe_id_param() and not info.is_user_param()
        }
        return create_model(f"{method_name}Args", **fields)

    def _create_enhanced_description(self, param_info: list[ParameterInfo]) -> str:
        param_docs = [
            info.to_doc_string() for info in param_info
            if not info.is_recipe_id_param() and not info.is_user_param()
        ]
        return f"{self.description}\n\nParameters:\n" + "\n".join(param_docs)

    def _create_tool_function(self, method: Callable, orig_sig: inspect.Signature):
        requires_recipe_id = RECIPE_ID_PARAM in orig_sig.parameters
        requires_user = USER_PARAM in orig_sig.parameters

        def tool_function(**kwargs) -> str:
            service_kwargs = dict(kwargs)
            if requires_recipe_id:
                recipe_id = _get_recipe_id_from_context()
                service_kwargs[RECIPE_ID_PARAM] = recipe_id

            if requires_user:
                user = _get_user_from_context()
                service_kwargs[USER_PARAM] = user

            result = method(**service_kwargs)
            return self._format_result(result)

        return tool_function

    def _format_result(self, result) -> str:
        if self.serializer:
            data = self.serializer(result, many=isinstance(result, list)).data
            result = JSONRenderer().render(data)
        return str(result)


def agent_accessible(
    description: str | None = None,
    serializer: ModelSerializer | None = None,
):
    """
    Decorator to mark service methods as accessible to agents.

    Args:
        description: Tool description for the agent. If not provided, uses the method's docstring.
        serializer: Serializer to use for rendering the result
    """

    def decorator(func):
        # Use docstring as fallback if no description provided
        final_description = description
        if final_description is None and func.__doc__:
            final_description = func.__doc__.strip()

        func._tool_metadata = ToolMetadata(
            description=final_description,
            serializer=serializer,
        )
        return func

    return decorator


class ServiceToolFactory:
    """Factory for generating agent tools from service classes."""

    def __init__(self, service_class):
        self.service_class = service_class
        from ...di import Container
        container = Container.instance()
        self.service_instance = container.resolve(service_class)

    @staticmethod
    def create_service_tools(service_class):
        """
        Convenience function to create all tools from a service class.

        Args:
            service_class: The service class to generate tools from

        Returns:
            List of generated tools
        """
        factory = ServiceToolFactory(service_class)
        return factory.generate_tools()

    def bindable_methods(self) -> list[str]:
        return [
            method_name
            for method_name in dir(self.service_class)
            if hasattr(getattr(self.service_class, method_name), METADATA_KEY)
        ]

    def generate_tools(self) -> list:
        return [
            self._create_tool_function(method_name)
            for method_name in self.bindable_methods()
        ]

    def _create_tool_function(self, method_name: str):
        method = getattr(self.service_instance, method_name)
        metadata = method._tool_metadata
        return metadata.build_tool(method)
