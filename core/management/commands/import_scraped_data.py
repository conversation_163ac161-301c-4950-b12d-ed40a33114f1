"""
Django management command for importing scraped ingredient data from CSV files.

This command loads CSV files generated by the scraper and imports them
into the Django models, handling duplicates and validation.
"""

import csv
import logging
from pathlib import Path

from django.core.management.base import BaseCommand
from django.db import transaction

from core.models import Fermentable, Hop, Yeast
from core.models.ingredients import FermentableType
from core.models.yeast import YeastForm, YeastType


class Command(BaseCommand):
    help = "Import scraped ingredient data from CSV files"

    def add_arguments(self, parser):
        parser.add_argument("csv_files", nargs="+", help="CSV files to import")

        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be imported without actually importing",
        )

        parser.add_argument(
            "--update-existing",
            action="store_true",
            help="Update existing ingredients instead of skipping them",
        )

        parser.add_argument(
            "--verbose", action="store_true", help="Enable verbose logging"
        )

    def handle(self, *args, **options):
        # Configure logging
        log_level = logging.DEBUG if options["verbose"] else logging.INFO
        logging.basicConfig(
            level=log_level,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        )

        logger = logging.getLogger(__name__)

        results = {
            "fermentables": {"imported": 0, "skipped": 0, "errors": 0},
            "yeasts": {"imported": 0, "skipped": 0, "errors": 0},
            "hops": {"imported": 0, "skipped": 0, "errors": 0},
        }

        for csv_file in options["csv_files"]:
            try:
                file_path = Path(csv_file)

                if not file_path.exists():
                    self.stdout.write(self.style.ERROR(f"File not found: {csv_file}"))
                    continue

                # Determine ingredient type from filename
                ingredient_type = self._detect_ingredient_type(file_path.name)

                if not ingredient_type:
                    self.stdout.write(
                        self.style.WARNING(
                            f"Could not determine ingredient type for: {csv_file}"
                        )
                    )
                    continue

                self.stdout.write(f"Importing {ingredient_type} from {csv_file}...")

                # Import the file
                file_results = self._import_csv_file(
                    file_path,
                    ingredient_type,
                    options["dry_run"],
                    options["update_existing"],
                )

                # Update results
                for key in results[ingredient_type]:
                    results[ingredient_type][key] += file_results[key]

                self.stdout.write(
                    self.style.SUCCESS(
                        f"Processed {file_results['imported']} {ingredient_type}, "
                        f"skipped {file_results['skipped']}, "
                        f"errors {file_results['errors']}"
                    )
                )

            except Exception as e:
                logger.error(f"Error processing {csv_file}: {e}")
                self.stdout.write(self.style.ERROR(f"Error processing {csv_file}: {e}"))

        # Print summary
        self._print_summary(results, options["dry_run"])

    def _detect_ingredient_type(self, filename: str) -> str:
        """Detect ingredient type from filename."""
        filename_lower = filename.lower()

        if (
            "fermentable" in filename_lower
            or "malt" in filename_lower
            or "grain" in filename_lower
        ):
            return "fermentables"
        elif "yeast" in filename_lower:
            return "yeasts"
        elif "hop" in filename_lower:
            return "hops"

        return ""

    def _import_csv_file(
        self,
        file_path: Path,
        ingredient_type: str,
        dry_run: bool,
        update_existing: bool,
    ) -> dict[str, int]:
        """Import a single CSV file."""
        results = {"imported": 0, "skipped": 0, "errors": 0}

        with open(file_path, encoding="utf-8") as csvfile:
            reader = csv.DictReader(csvfile)

            for row_num, row in enumerate(reader, start=2):  # Start at 2 for header
                try:
                    if ingredient_type == "fermentables":
                        success = self._import_fermentable(
                            row, dry_run, update_existing
                        )
                    elif ingredient_type == "yeasts":
                        success = self._import_yeast(row, dry_run, update_existing)
                    elif ingredient_type == "hops":
                        success = self._import_hop(row, dry_run, update_existing)
                    else:
                        raise ValueError(f"Unknown ingredient type: {ingredient_type}")

                    if success == "imported":
                        results["imported"] += 1
                    elif success == "skipped":
                        results["skipped"] += 1

                except Exception as e:
                    results["errors"] += 1
                    self.stdout.write(
                        self.style.ERROR(f"Error importing row {row_num}: {e}")
                    )

        return results

    def _import_fermentable(
        self, row: dict[str, str], dry_run: bool, update_existing: bool
    ) -> str:
        """Import a single fermentable."""
        name = row.get("name", "").strip()
        if not name:
            raise ValueError("Missing name")

        # Check if exists
        existing = Fermentable.objects.filter(name=name).first()

        if existing and not update_existing:
            return "skipped"

        # Prepare data
        data = {
            "name": name,
            "country_of_origin": row.get("country_of_origin", ""),
            "notes": row.get("notes", ""),
            "fermentable_type": row.get("fermentable_type", FermentableType.BASE_MALT),
            "extract_potential_ppg": float(row.get("extract_potential_ppg", 37.0)),
            "color_lovibond": float(row.get("color_lovibond", 2.0)),
            "requires_mashing": row.get("requires_mashing", "True").lower() == "true",
        }

        if dry_run:
            self.stdout.write(f"Would import fermentable: {name}")
            return "imported"

        with transaction.atomic():
            if existing:
                for key, value in data.items():
                    setattr(existing, key, value)
                existing.save()
            else:
                Fermentable.objects.create(**data)

        return "imported"

    def _import_yeast(
        self, row: dict[str, str], dry_run: bool, update_existing: bool
    ) -> str:
        """Import a single yeast."""
        name = row.get("name", "").strip()
        if not name:
            raise ValueError("Missing name")

        # Check if exists
        existing = Yeast.objects.filter(name=name).first()

        if existing and not update_existing:
            return "skipped"

        # Prepare data
        data = {
            "name": name,
            "laboratory": row.get("laboratory", ""),
            "product_id": row.get("product_id", ""),
            "yeast_type": row.get("yeast_type", YeastType.ALE),
            "yeast_form": row.get("yeast_form", YeastForm.DRY),
            "min_temperature_fahrenheit": float(
                row.get("min_temperature_fahrenheit", 65.0)
            ),
            "max_temperature_fahrenheit": float(
                row.get("max_temperature_fahrenheit", 75.0)
            ),
            "attenuation_percent": float(row.get("attenuation_percent", 75.0)),
            "flocculation": row.get("flocculation", "MEDIUM"),
            "alcohol_tolerance_percent": (
                float(row.get("alcohol_tolerance_percent", 10.0))
                if row.get("alcohol_tolerance_percent")
                else None
            ),
            "description": row.get("description", ""),
        }

        if dry_run:
            self.stdout.write(f"Would import yeast: {name}")
            return "imported"

        with transaction.atomic():
            if existing:
                for key, value in data.items():
                    setattr(existing, key, value)
                existing.save()
            else:
                Yeast.objects.create(**data)

        return "imported"

    def _import_hop(
        self, row: dict[str, str], dry_run: bool, update_existing: bool
    ) -> str:
        """Import a single hop."""
        name = row.get("name", "").strip()
        if not name:
            raise ValueError("Missing name")

        # Check if exists
        existing = Hop.objects.filter(name=name).first()

        if existing and not update_existing:
            return "skipped"

        # Prepare data
        data = {
            "name": name,
            "country_of_origin": row.get("country_of_origin", ""),
            "notes": row.get("notes", ""),
            "alpha_acid": float(row.get("alpha_acid", 0.0)),
            "beta_acid": float(row.get("beta_acid", 0.0)),
            "aroma": row.get("aroma", "False").lower() == "true",
            "bittering": row.get("bittering", "False").lower() == "true",
        }

        if dry_run:
            self.stdout.write(f"Would import hop: {name}")
            return "imported"

        with transaction.atomic():
            if existing:
                for key, value in data.items():
                    setattr(existing, key, value)
                existing.save()
            else:
                Hop.objects.create(**data)

        return "imported"

    def _print_summary(self, results: dict[str, dict[str, int]], dry_run: bool):
        """Print import summary."""
        action = "Would import" if dry_run else "Imported"

        self.stdout.write("\n" + "=" * 50)
        self.stdout.write(f"IMPORT SUMMARY {'(DRY RUN)' if dry_run else ''}")
        self.stdout.write("=" * 50)

        total_imported = 0
        total_skipped = 0
        total_errors = 0

        for ingredient_type, stats in results.items():
            if stats["imported"] > 0 or stats["skipped"] > 0 or stats["errors"] > 0:
                self.stdout.write(
                    f"{ingredient_type.capitalize()}: "
                    f"{stats['imported']} imported, "
                    f"{stats['skipped']} skipped, "
                    f"{stats['errors']} errors"
                )

                total_imported += stats["imported"]
                total_skipped += stats["skipped"]
                total_errors += stats["errors"]

        self.stdout.write(
            f"\nTotal: {total_imported} imported, {total_skipped} skipped, {total_errors} errors"
        )

        if total_imported > 0:
            self.stdout.write(
                self.style.SUCCESS(
                    f"\n{action} {total_imported} ingredients successfully!"
                )
            )
