"""
Django management command for scraping brewing ingredients.
"""

import logging
from datetime import datetime
from pathlib import Path

from django.core.management.base import BaseCommand

from core.agents.models import ModelConfiguration
from core.agents.scraper.schemas import IngredientType
from core.agents.scraper.simple_scraper import create_simple_scraper


class Command(BaseCommand):
    help = "Scrape brewing ingredients from websites"

    def add_arguments(self, parser):
        parser.add_argument("url", help="URL to scrape")
        parser.add_argument(
            "--type",
            choices=["fermentables", "yeasts", "hops"],
            required=True,
            help="Type of ingredients to scrape",
        )
        parser.add_argument(
            "--max-ingredients",
            type=int,
            default=20,
            help="Maximum ingredients to extract",
        )
        parser.add_argument(
            "--output-dir", default="scraped_data", help="Directory to save CSV files"
        )

    def handle(self, *args, **options):
        logging.basicConfig(level=logging.INFO)

        url = options["url"]
        ingredient_type = IngredientType(options["type"])
        max_ingredients = options["max_ingredients"]
        output_dir = Path(options["output_dir"])

        self.stdout.write(f"Scraping {ingredient_type.value} from {url}")

        try:
            # Create simple scraper
            scraper = create_simple_scraper(ModelConfiguration.from_settings())

            # Scrape ingredients
            result = scraper.scrape_ingredients(url, ingredient_type, max_ingredients)

            if result.success:
                self.stdout.write(
                    self.style.SUCCESS(
                        f"✓ Scraped {len(result.ingredients)} ingredients"
                    )
                )

                # Export to CSV
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = output_dir / f"{ingredient_type.value}_{timestamp}.csv"
                scraper.export_to_csv(result, str(filename))

                self.stdout.write(f"Exported to: {filename}")

            else:
                self.stdout.write(self.style.ERROR("✗ Scraping failed"))
                for error in result.errors:
                    self.stdout.write(self.style.ERROR(f"  - {error}"))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error: {e}"))
