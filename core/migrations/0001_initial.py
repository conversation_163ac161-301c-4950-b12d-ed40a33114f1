# Generated by Django 5.2.4 on 2025-07-20 16:50

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="Fermentable",
            fields=[
                (
                    "id",
                    models.CharField(
                        editable=False, max_length=50, primary_key=True, serialize=False
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "country_of_origin",
                    models.CharField(
                        default="", help_text="Country of origin", max_length=100
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        default="",
                        help_text="Notes for special instructions",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "fermentable_type",
                    models.CharField(
                        choices=[
                            ("BASE", "Base Malt"),
                            ("SPECIALTY", "Specialty Malt"),
                            ("CRYSTAL", "Crystal/Caramel Malt"),
                            ("ROASTED", "Roasted Malt"),
                            ("ADJUNCT", "Adjunct"),
                            ("EXTRACT", "Malt Extract"),
                        ],
                        default="BASE",
                        max_length=10,
                    ),
                ),
                (
                    "extract_potential_ppg",
                    models.FloatField(
                        default=37.0,
                        help_text="Extract potential in points per pound per gallon",
                    ),
                ),
                (
                    "color_lovibond",
                    models.FloatField(
                        default=2.0, help_text="Color in degrees Lovibond"
                    ),
                ),
                (
                    "requires_mashing",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this fermentable requires mashing",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Hop",
            fields=[
                (
                    "id",
                    models.CharField(
                        editable=False, max_length=50, primary_key=True, serialize=False
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("name", models.CharField(max_length=100)),
                (
                    "country_of_origin",
                    models.CharField(
                        default="", help_text="Country of origin", max_length=100
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        default="",
                        help_text="Notes for special instructions",
                    ),
                ),
                (
                    "alpha_acid",
                    models.FloatField(default=0.0, help_text="Alpha acid percentage"),
                ),
                (
                    "beta_acid",
                    models.FloatField(default=0.0, help_text="Beta acid percentage"),
                ),
                ("aroma", models.BooleanField(default=False)),
                ("bittering", models.BooleanField(default=False)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="TestModel",
            fields=[
                (
                    "id",
                    models.CharField(
                        editable=False, max_length=50, primary_key=True, serialize=False
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("name", models.CharField(max_length=100)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="WaterProfile",
            fields=[
                (
                    "id",
                    models.CharField(
                        editable=False, max_length=50, primary_key=True, serialize=False
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the water profile (e.g., 'Burton on Trent', 'Balanced')",
                        max_length=100,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        default="",
                        help_text="Description of the water profile and suitable beer styles",
                    ),
                ),
                (
                    "calcium_ppm",
                    models.FloatField(
                        default=0.0, help_text="Calcium (Ca²⁺) content in ppm"
                    ),
                ),
                (
                    "magnesium_ppm",
                    models.FloatField(
                        default=0.0, help_text="Magnesium (Mg²⁺) content in ppm"
                    ),
                ),
                (
                    "sodium_ppm",
                    models.FloatField(
                        default=0.0, help_text="Sodium (Na⁺) content in ppm"
                    ),
                ),
                (
                    "sulfate_ppm",
                    models.FloatField(
                        default=0.0, help_text="Sulfate (SO₄²⁻) content in ppm"
                    ),
                ),
                (
                    "chloride_ppm",
                    models.FloatField(
                        default=0.0, help_text="Chloride (Cl⁻) content in ppm"
                    ),
                ),
                (
                    "bicarbonate_ppm",
                    models.FloatField(
                        default=0.0, help_text="Bicarbonate (HCO₃⁻) content in ppm"
                    ),
                ),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Yeast",
            fields=[
                (
                    "id",
                    models.CharField(
                        editable=False, max_length=50, primary_key=True, serialize=False
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the yeast strain", max_length=100
                    ),
                ),
                (
                    "laboratory",
                    models.CharField(
                        blank=True,
                        default="",
                        help_text="Laboratory or manufacturer (e.g., Wyeast, White Labs, Lallemand)",
                        max_length=50,
                    ),
                ),
                (
                    "product_id",
                    models.CharField(
                        blank=True,
                        default="",
                        help_text="Product ID or strain number (e.g., WLP001, US-05)",
                        max_length=20,
                    ),
                ),
                (
                    "yeast_type",
                    models.CharField(
                        choices=[
                            ("ALE", "Ale Yeast"),
                            ("LAGER", "Lager Yeast"),
                            ("WILD", "Wild Yeast"),
                            ("BRETT", "Brettanomyces"),
                            ("BACTERIA", "Bacteria"),
                        ],
                        default="ALE",
                        help_text="Type of yeast",
                        max_length=10,
                    ),
                ),
                (
                    "yeast_form",
                    models.CharField(
                        choices=[
                            ("DRY", "Dry"),
                            ("LIQUID", "Liquid"),
                            ("SLANT", "Slant"),
                            ("CULTURE", "Culture"),
                        ],
                        default="DRY",
                        help_text="Form of yeast",
                        max_length=10,
                    ),
                ),
                (
                    "min_temperature_fahrenheit",
                    models.FloatField(
                        help_text="Minimum fermentation temperature in Fahrenheit"
                    ),
                ),
                (
                    "max_temperature_fahrenheit",
                    models.FloatField(
                        help_text="Maximum fermentation temperature in Fahrenheit"
                    ),
                ),
                (
                    "attenuation_percent",
                    models.FloatField(help_text="Expected attenuation percentage"),
                ),
                (
                    "flocculation",
                    models.CharField(
                        choices=[
                            ("LOW", "Low"),
                            ("MEDIUM", "Medium"),
                            ("HIGH", "High"),
                        ],
                        default="MEDIUM",
                        help_text="Flocculation characteristics",
                        max_length=20,
                    ),
                ),
                (
                    "alcohol_tolerance_percent",
                    models.FloatField(
                        blank=True, help_text="Alcohol tolerance percentage", null=True
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        default="",
                        help_text="Description of yeast characteristics and suitable beer styles",
                    ),
                ),
            ],
            options={
                "ordering": ["laboratory", "name"],
            },
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "id",
                    models.CharField(
                        editable=False, max_length=50, primary_key=True, serialize=False
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "email",
                    models.EmailField(
                        help_text="Email address for authentication",
                        max_length=254,
                        unique=True,
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, help_text="User's first name", max_length=150
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, help_text="User's last name", max_length=150
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active.",
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into the admin site.",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        help_text="Date when the user account was created",
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "User",
                "verbose_name_plural": "Users",
                "db_table": "core_user",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Recipe",
            fields=[
                (
                    "id",
                    models.CharField(
                        editable=False, max_length=50, primary_key=True, serialize=False
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(default="")),
                (
                    "batch_size_gallons",
                    models.FloatField(default=5.0, help_text="Batch size in gallons"),
                ),
                (
                    "mash_efficiency_percent",
                    models.FloatField(
                        default=75.0, help_text="Mash efficiency percentage"
                    ),
                ),
                (
                    "target_original_gravity",
                    models.FloatField(
                        default=1.05, help_text="Target original gravity (e.g., 1.050)"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who owns this recipe",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "water_profile",
                    models.ForeignKey(
                        blank=True,
                        help_text="Water profile used for this recipe",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="core.waterprofile",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="FlameoutHopInclusion",
            fields=[
                (
                    "id",
                    models.CharField(
                        editable=False, max_length=50, primary_key=True, serialize=False
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("quantity", models.FloatField()),
                (
                    "quantity_unit",
                    models.CharField(
                        choices=[
                            ("lb", "Pounds"),
                            ("oz", "Ounces"),
                            ("kg", "Kilograms"),
                            ("g", "Grams"),
                        ],
                        default="oz",
                        max_length=2,
                    ),
                ),
                ("notes", models.TextField(blank=True, default="")),
                (
                    "hop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.hop"
                    ),
                ),
                (
                    "recipe",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.recipe"
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="FirstWortHopInclusion",
            fields=[
                (
                    "id",
                    models.CharField(
                        editable=False, max_length=50, primary_key=True, serialize=False
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("quantity", models.FloatField()),
                (
                    "quantity_unit",
                    models.CharField(
                        choices=[
                            ("lb", "Pounds"),
                            ("oz", "Ounces"),
                            ("kg", "Kilograms"),
                            ("g", "Grams"),
                        ],
                        default="oz",
                        max_length=2,
                    ),
                ),
                ("notes", models.TextField(blank=True, default="")),
                (
                    "hop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.hop"
                    ),
                ),
                (
                    "recipe",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.recipe"
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="FermentableInclusion",
            fields=[
                (
                    "id",
                    models.CharField(
                        editable=False, max_length=50, primary_key=True, serialize=False
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "quantity",
                    models.FloatField(
                        help_text="Quantity of fermentable added to the recipe"
                    ),
                ),
                (
                    "quantity_unit",
                    models.CharField(
                        choices=[
                            ("lb", "Pounds"),
                            ("oz", "Ounces"),
                            ("kg", "Kilograms"),
                            ("g", "Grams"),
                        ],
                        default="lb",
                        help_text="Unit of measurement for the quantity of fermentable",
                        max_length=2,
                    ),
                ),
                (
                    "efficiency_percent",
                    models.FloatField(
                        blank=True,
                        help_text="Mash efficiency for this fermentable (leave blank to use recipe default)",
                        null=True,
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        default="",
                        help_text="Notes for special instructions",
                    ),
                ),
                (
                    "fermentable",
                    models.ForeignKey(
                        help_text="Fermentable added to the recipe",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.fermentable",
                    ),
                ),
                (
                    "recipe",
                    models.ForeignKey(
                        help_text="Recipe containing this fermentable addition",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.recipe",
                    ),
                ),
            ],
            options={
                "ordering": ["-quantity", "fermentable__name"],
            },
        ),
        migrations.CreateModel(
            name="DryHopInclusion",
            fields=[
                (
                    "id",
                    models.CharField(
                        editable=False, max_length=50, primary_key=True, serialize=False
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("quantity", models.FloatField()),
                (
                    "quantity_unit",
                    models.CharField(
                        choices=[
                            ("lb", "Pounds"),
                            ("oz", "Ounces"),
                            ("kg", "Kilograms"),
                            ("g", "Grams"),
                        ],
                        default="oz",
                        max_length=2,
                    ),
                ),
                ("notes", models.TextField(blank=True, default="")),
                (
                    "time_days",
                    models.PositiveIntegerField(
                        blank=True, help_text="Duration of dry hop in days", null=True
                    ),
                ),
                (
                    "hop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.hop"
                    ),
                ),
                (
                    "recipe",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.recipe"
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="BoilHopInclusion",
            fields=[
                (
                    "id",
                    models.CharField(
                        editable=False, max_length=50, primary_key=True, serialize=False
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("quantity", models.FloatField()),
                (
                    "quantity_unit",
                    models.CharField(
                        choices=[
                            ("lb", "Pounds"),
                            ("oz", "Ounces"),
                            ("kg", "Kilograms"),
                            ("g", "Grams"),
                        ],
                        default="oz",
                        max_length=2,
                    ),
                ),
                ("notes", models.TextField(blank=True, default="")),
                (
                    "time_minutes",
                    models.PositiveIntegerField(help_text="Minutes before end of boil"),
                ),
                (
                    "hop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.hop"
                    ),
                ),
                (
                    "recipe",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.recipe"
                    ),
                ),
            ],
            options={
                "ordering": ["-time_minutes"],
            },
        ),
        migrations.CreateModel(
            name="WhirlpoolHopInclusion",
            fields=[
                (
                    "id",
                    models.CharField(
                        editable=False, max_length=50, primary_key=True, serialize=False
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("quantity", models.FloatField()),
                (
                    "quantity_unit",
                    models.CharField(
                        choices=[
                            ("lb", "Pounds"),
                            ("oz", "Ounces"),
                            ("kg", "Kilograms"),
                            ("g", "Grams"),
                        ],
                        default="oz",
                        max_length=2,
                    ),
                ),
                ("notes", models.TextField(blank=True, default="")),
                (
                    "time_minutes",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Duration of whirlpool in minutes",
                        null=True,
                    ),
                ),
                (
                    "temperature_f",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Temperature in Fahrenheit for whirlpool",
                        null=True,
                    ),
                ),
                (
                    "hop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.hop"
                    ),
                ),
                (
                    "recipe",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.recipe"
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="YeastInclusion",
            fields=[
                (
                    "id",
                    models.CharField(
                        editable=False, max_length=50, primary_key=True, serialize=False
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "quantity",
                    models.FloatField(
                        help_text="Quantity of yeast (packets, vials, etc.)"
                    ),
                ),
                (
                    "quantity_unit",
                    models.CharField(
                        choices=[
                            ("PACKET", "Packet"),
                            ("VIAL", "Vial"),
                            ("POUCH", "Pouch"),
                            ("ML", "mL"),
                            ("GRAMS", "Grams"),
                        ],
                        default="PACKET",
                        help_text="Unit of measurement for yeast quantity",
                        max_length=20,
                    ),
                ),
                (
                    "starter_made",
                    models.BooleanField(
                        default=False, help_text="Whether a yeast starter was made"
                    ),
                ),
                (
                    "starter_size_ml",
                    models.FloatField(
                        blank=True, help_text="Size of yeast starter in mL", null=True
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        default="",
                        help_text="Notes about yeast handling or starter preparation",
                    ),
                ),
                (
                    "recipe",
                    models.ForeignKey(
                        help_text="Recipe containing this yeast addition",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.recipe",
                    ),
                ),
                (
                    "yeast",
                    models.ForeignKey(
                        help_text="Yeast strain added to the recipe",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.yeast",
                    ),
                ),
            ],
            options={
                "ordering": ["yeast__name"],
            },
        ),
        migrations.CreateModel(
            name="MashStep",
            fields=[
                (
                    "id",
                    models.CharField(
                        editable=False, max_length=50, primary_key=True, serialize=False
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the mash step (e.g., 'Protein Rest', 'Saccharification')",
                        max_length=100,
                    ),
                ),
                (
                    "step_type",
                    models.CharField(
                        choices=[
                            ("INFUSION", "Infusion"),
                            ("TEMPERATURE", "Temperature"),
                            ("DECOCTION", "Decoction"),
                        ],
                        default="INFUSION",
                        help_text="Type of mash step",
                        max_length=15,
                    ),
                ),
                (
                    "temperature_fahrenheit",
                    models.FloatField(
                        help_text="Target temperature for this step in Fahrenheit"
                    ),
                ),
                (
                    "duration_minutes",
                    models.IntegerField(help_text="Duration of this step in minutes"),
                ),
                (
                    "step_order",
                    models.PositiveIntegerField(
                        help_text="Order of this step in the mash schedule"
                    ),
                ),
                (
                    "infusion_amount_gallons",
                    models.FloatField(
                        blank=True,
                        help_text="Amount of water to add for infusion steps (gallons)",
                        null=True,
                    ),
                ),
                (
                    "infusion_temperature_fahrenheit",
                    models.FloatField(
                        blank=True,
                        help_text="Temperature of infusion water in Fahrenheit",
                        null=True,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        default="",
                        help_text="Description or notes about this mash step",
                    ),
                ),
                (
                    "recipe",
                    models.ForeignKey(
                        help_text="Recipe containing this mash step",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.recipe",
                    ),
                ),
            ],
            options={
                "ordering": ["recipe", "step_order"],
                "unique_together": {("recipe", "step_order")},
            },
        ),
        migrations.CreateModel(
            name="FermentationPhase",
            fields=[
                (
                    "id",
                    models.CharField(
                        editable=False, max_length=50, primary_key=True, serialize=False
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the fermentation phase", max_length=100
                    ),
                ),
                (
                    "phase_type",
                    models.CharField(
                        choices=[
                            ("PRIMARY", "Primary Fermentation"),
                            ("SECONDARY", "Secondary Fermentation"),
                            ("CONDITIONING", "Conditioning"),
                            ("DIACETYL_REST", "Diacetyl Rest"),
                            ("COLD_CRASH", "Cold Crash"),
                            ("LAGERING", "Lagering"),
                        ],
                        default="PRIMARY",
                        help_text="Type of fermentation phase",
                        max_length=15,
                    ),
                ),
                (
                    "temperature_fahrenheit",
                    models.FloatField(
                        help_text="Target temperature for this phase in Fahrenheit"
                    ),
                ),
                (
                    "duration_days",
                    models.IntegerField(help_text="Duration of this phase in days"),
                ),
                (
                    "phase_order",
                    models.PositiveIntegerField(
                        help_text="Order of this phase in the fermentation schedule"
                    ),
                ),
                (
                    "gravity_target",
                    models.FloatField(
                        blank=True,
                        help_text="Target specific gravity for this phase (optional)",
                        null=True,
                    ),
                ),
                (
                    "pressure_psi",
                    models.FloatField(
                        blank=True,
                        help_text="Pressure for this phase in PSI (for pressure fermentation)",
                        null=True,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        default="",
                        help_text="Description or notes about this fermentation phase",
                    ),
                ),
                (
                    "recipe",
                    models.ForeignKey(
                        help_text="Recipe containing this fermentation phase",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.recipe",
                    ),
                ),
            ],
            options={
                "ordering": ["recipe", "phase_order"],
                "unique_together": {("recipe", "phase_order")},
            },
        ),
    ]
