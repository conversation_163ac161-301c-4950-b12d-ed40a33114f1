# Generated by Django 5.2.4 on 2025-07-21 20:42

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="BeerStyle",
            fields=[
                (
                    "id",
                    models.CharField(
                        editable=False, max_length=50, primary_key=True, serialize=False
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the beer style (e.g., 'American IPA', 'Imperial Stout')",
                        max_length=100,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        default="",
                        help_text="Description of the beer style characteristics",
                    ),
                ),
                (
                    "srm_min",
                    models.FloatField(
                        blank=True,
                        help_text="Minimum SRM color value for this style",
                        null=True,
                    ),
                ),
                (
                    "srm_max",
                    models.FloatField(
                        blank=True,
                        help_text="Maximum SRM color value for this style",
                        null=True,
                    ),
                ),
                (
                    "ibu_min",
                    models.FloatField(
                        blank=True,
                        help_text="Minimum IBU (International Bitterness Units) for this style",
                        null=True,
                    ),
                ),
                (
                    "ibu_max",
                    models.FloatField(
                        blank=True,
                        help_text="Maximum IBU (International Bitterness Units) for this style",
                        null=True,
                    ),
                ),
                (
                    "og_min",
                    models.FloatField(
                        blank=True,
                        help_text="Minimum original gravity for this style (e.g., 1.040)",
                        null=True,
                    ),
                ),
                (
                    "og_max",
                    models.FloatField(
                        blank=True,
                        help_text="Maximum original gravity for this style (e.g., 1.060)",
                        null=True,
                    ),
                ),
                (
                    "fg_min",
                    models.FloatField(
                        blank=True,
                        help_text="Minimum final gravity for this style (e.g., 1.008)",
                        null=True,
                    ),
                ),
                (
                    "fg_max",
                    models.FloatField(
                        blank=True,
                        help_text="Maximum final gravity for this style (e.g., 1.016)",
                        null=True,
                    ),
                ),
                (
                    "abv_min",
                    models.FloatField(
                        blank=True,
                        help_text="Minimum alcohol by volume percentage for this style",
                        null=True,
                    ),
                ),
                (
                    "abv_max",
                    models.FloatField(
                        blank=True,
                        help_text="Maximum alcohol by volume percentage for this style",
                        null=True,
                    ),
                ),
            ],
            options={
                "verbose_name": "Beer Style",
                "verbose_name_plural": "Beer Styles",
                "ordering": ["name"],
            },
        ),
        migrations.AddField(
            model_name="recipe",
            name="beer_style",
            field=models.ForeignKey(
                blank=True,
                help_text="Beer style category for this recipe",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="core.beerstyle",
            ),
        ),
    ]
