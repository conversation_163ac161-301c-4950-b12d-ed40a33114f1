# Generated by Django 5.2.4 on 2025-07-26 15:16

import pgvector.django.indexes
import pgvector.django.vector
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0004_enable_pg_vector"),
    ]

    operations = [
        migrations.AddField(
            model_name="beerstyle",
            name="embedded_description",
            field=pgvector.django.vector.VectorField(
                dimensions=768,
                help_text="Embedded description vector for similarity search",
                null=True,
            ),
        ),
        migrations.AddIndex(
            model_name="beerstyle",
            index=pgvector.django.indexes.HnswIndex(
                ef_construction=64,
                fields=["embedded_description"],
                m=16,
                name="clip_l14_vectors_index",
                opclasses=["vector_cosine_ops"],
            ),
        ),
    ]
