"""
Models package for the hoplogic application.

This package contains all the Django models organized into logical modules.
"""

# Import all models to make them available when importing from core.models
from .base import BaseModel, TestModel
from .beer_style import BeerStyle
from .fermentable_inclusions import FermentableInclusion
from .fermentation_schedule import FermentationPhase
from .hop_inclusions import (
    BoilHopInclusion,
    DryHopInclusion,
    FirstWortHopInclusion,
    FlameoutHopInclusion,
    HopInclusion,
    WhirlpoolHopInclusion,
)
from .ingredients import Fermentable, FermentableType, Hop
from .mash_schedule import MashStep
from .recipe import Recipe
from .user import User
from .water_profile import WaterProfile
from .yeast import Yeast, YeastForm, YeastInclusion, YeastType

# Make all models available at package level
__all__ = [
    # Base models
    "BaseModel",
    "TestModel",
    # User models
    "User",
    # Beer style models
    "BeerStyle",
    # Ingredient models
    "Hop",
    "Fermentable",
    "FermentableType",
    # Hop inclusion models
    "HopInclusion",
    "BoilHopInclusion",
    "FirstWortHopInclusion",
    "FlameoutHopInclusion",
    "WhirlpoolHopInclusion",
    "DryHopInclusion",
    # Fermentable inclusion models
    "FermentableInclusion",
    # Water and yeast models
    "WaterProfile",
    "Yeast",
    "YeastInclusion",
    "YeastType",
    "YeastForm",
    # Process models
    "MashStep",
    "FermentationPhase",
    # Recipe models
    "Recipe",
]
