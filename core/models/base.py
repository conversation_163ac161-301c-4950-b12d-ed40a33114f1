"""
Base model classes for the hoplogic application.

This module contains the base model classes that provide common functionality
like soft deletes, timestamps, and unique IDs.
"""

import uuid
from datetime import datetime

from django.db import models
from django.utils import timezone


class SoftDeleteQuerySet(models.QuerySet):
    """Custom QuerySet for soft delete functionality."""

    def delete(self) -> None:
        """Soft delete all objects in the queryset."""
        self.update(deleted_at=timezone.now())

    def hard_delete(self) -> None:
        """Permanently delete all objects in the queryset."""
        super().delete()

    def alive(self):
        """Return only non-deleted objects."""
        return self.filter(deleted_at__isnull=True)

    def dead(self):
        """Return only deleted objects."""
        return self.filter(deleted_at__isnull=False)


class SoftDeleteManager(models.Manager):
    """Custom manager for soft delete functionality."""

    def get_queryset(self):
        """Return only non-deleted objects by default."""
        return SoftDeleteQuerySet(self.model, using=self._db).alive()

    def all_with_deleted(self):
        """Return all objects including deleted ones."""
        return SoftDeleteQuerySet(self.model, using=self._db)

    def deleted_only(self):
        """Return only deleted objects."""
        return SoftDeleteQuerySet(self.model, using=self._db).dead()

    def alive(self):
        """Return only non-deleted objects."""
        return SoftDeleteQuerySet(self.model, using=self._db).alive()

    def dead(self):
        """Return only deleted objects."""
        return SoftDeleteQuerySet(self.model, using=self._db).dead()


class BaseModel(models.Model):
    """
    Base model class that provides common functionality for all models.

    Features:
    - Unique ID with timestamp prefix (stripe-like)
    - Soft delete functionality
    - Automatic timestamps (created_at, updated_at)
    """

    id = models.CharField(max_length=50, primary_key=True, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True)

    objects = SoftDeleteManager()

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        """Override save to generate unique ID if not set."""
        if not self.id:
            self.id = self._generate_id()
        super().save(*args, **kwargs)

    def delete(self, using=None, keep_parents=False):
        """Soft delete the object."""
        self.deleted_at = timezone.now()
        self.save(using=using)

    def hard_delete(self, using=None, keep_parents=False):
        """Permanently delete the object."""
        super().delete(using=using, keep_parents=keep_parents)

    def restore(self):
        """Restore a soft-deleted object."""
        self.deleted_at = None
        self.save()

    @property
    def is_deleted(self) -> bool:
        """Check if the object is soft-deleted."""
        return self.deleted_at is not None

    def _generate_id(self) -> str:
        """Generate a unique ID with timestamp prefix."""
        # Get current timestamp in seconds
        timestamp = int(datetime.now().timestamp())

        # Generate a random UUID
        random_part = str(uuid.uuid4()).replace("-", "")[:16]

        # Combine timestamp and random part
        return f"{self._get_id_prefix()}_{timestamp}_{random_part}"

    def _get_id_prefix(self) -> str:
        """Get the ID prefix for this model type."""
        # Default prefix based on model name
        return self.__class__.__name__.lower()[:3]


class TestModel(BaseModel):
    """Test model for testing BaseModel functionality."""

    name = models.CharField(max_length=100)

    def _get_id_prefix(self) -> str:
        return "tes"

    def __str__(self) -> str:
        return str(self.name)
