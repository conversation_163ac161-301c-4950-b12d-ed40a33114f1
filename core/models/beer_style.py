"""
Beer style model for the hoplogic application.

This module contains the BeerStyle model that defines beer style categories
with their characteristic vital ranges (SRM, IBU, gravity, ABV).
"""

from django.db import models
from pgvector.django import HnswIndex, VectorField

from .base import BaseModel


class BeerStyle(BaseModel):
    """
    Beer style model defining style characteristics and vital ranges.

    This model stores beer style information including name, description,
    and the typical ranges for various brewing parameters like color,
    bitterness, gravity, and alcohol content.
    """

    name = models.CharField(
        max_length=100,
        help_text="Name of the beer style (e.g., 'American IPA', 'Imperial Stout')",
    )

    description = models.TextField(
        blank=True,
        default="",
        help_text="Description of the beer style characteristics",
    )

    embedded_description = VectorField(
        null=True,
        help_text="Embedded description vector for similarity search",
        dimensions=768,
    )

    # SRM Color Range
    srm_min = models.FloatField(
        null=True, blank=True, help_text="Minimum SRM color value for this style"
    )

    srm_max = models.FloatField(
        null=True, blank=True, help_text="Maximum SRM color value for this style"
    )

    # IBU Bitterness Range
    ibu_min = models.FloatField(
        null=True,
        blank=True,
        help_text="Minimum IBU (International Bitterness Units) for this style",
    )

    ibu_max = models.FloatField(
        null=True,
        blank=True,
        help_text="Maximum IBU (International Bitterness Units) for this style",
    )

    # Original Gravity Range
    og_min = models.FloatField(
        null=True,
        blank=True,
        help_text="Minimum original gravity for this style (e.g., 1.040)",
    )

    og_max = models.FloatField(
        null=True,
        blank=True,
        help_text="Maximum original gravity for this style (e.g., 1.060)",
    )

    # Final Gravity Range
    fg_min = models.FloatField(
        null=True,
        blank=True,
        help_text="Minimum final gravity for this style (e.g., 1.008)",
    )

    fg_max = models.FloatField(
        null=True,
        blank=True,
        help_text="Maximum final gravity for this style (e.g., 1.016)",
    )

    # ABV Range
    abv_min = models.FloatField(
        null=True,
        blank=True,
        help_text="Minimum alcohol by volume percentage for this style",
    )

    abv_max = models.FloatField(
        null=True,
        blank=True,
        help_text="Maximum alcohol by volume percentage for this style",
    )

    class Meta:
        ordering = ["name"]
        verbose_name = "Beer Style"
        verbose_name_plural = "Beer Styles"

        indexes = [
            HnswIndex(
                name="clip_l14_vectors_index",
                fields=["embedded_description"],
                m=16,
                ef_construction=64,
                opclasses=["vector_cosine_ops"],
            )
        ]

    def __str__(self) -> str:
        return str(self.name)

    def _get_id_prefix(self) -> str:
        """Get the ID prefix for BeerStyle model."""
        return "bst"

    @property
    def srm_range_display(self) -> str:
        """Return a formatted string showing the SRM color range."""
        if self.srm_min is not None and self.srm_max is not None:
            return f"{self.srm_min}-{self.srm_max} SRM"
        elif self.srm_min is not None:
            return f"{self.srm_min}+ SRM"
        elif self.srm_max is not None:
            return f"≤{self.srm_max} SRM"
        return "No SRM range specified"

    @property
    def ibu_range_display(self) -> str:
        """Return a formatted string showing the IBU bitterness range."""
        if self.ibu_min is not None and self.ibu_max is not None:
            return f"{self.ibu_min}-{self.ibu_max} IBU"
        elif self.ibu_min is not None:
            return f"{self.ibu_min}+ IBU"
        elif self.ibu_max is not None:
            return f"≤{self.ibu_max} IBU"
        return "No IBU range specified"

    @property
    def og_range_display(self) -> str:
        """Return a formatted string showing the original gravity range."""
        if self.og_min is not None and self.og_max is not None:
            return f"{self.og_min:.3f}-{self.og_max:.3f} OG"
        elif self.og_min is not None:
            return f"{self.og_min:.3f}+ OG"
        elif self.og_max is not None:
            return f"≤{self.og_max:.3f} OG"
        return "No OG range specified"

    @property
    def fg_range_display(self) -> str:
        """Return a formatted string showing the final gravity range."""
        if self.fg_min is not None and self.fg_max is not None:
            return f"{self.fg_min:.3f}-{self.fg_max:.3f} FG"
        elif self.fg_min is not None:
            return f"{self.fg_min:.3f}+ FG"
        elif self.fg_max is not None:
            return f"≤{self.fg_max:.3f} FG"
        return "No FG range specified"

    @property
    def abv_range_display(self) -> str:
        """Return a formatted string showing the ABV range."""
        if self.abv_min is not None and self.abv_max is not None:
            return f"{self.abv_min:.1f}-{self.abv_max:.1f}% ABV"
        elif self.abv_min is not None:
            return f"{self.abv_min:.1f}+ % ABV"
        elif self.abv_max is not None:
            return f"≤{self.abv_max:.1f}% ABV"
        return "No ABV range specified"
