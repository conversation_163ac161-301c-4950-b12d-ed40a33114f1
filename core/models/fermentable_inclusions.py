from django.db import models

from .base import BaseModel
from .ingredients import Fermentable
from .units import QuantityUnit


class FermentableInclusion(BaseModel):
    recipe = models.ForeignKey(
        "Recipe",
        on_delete=models.CASCADE,
        help_text="Recipe containing this fermentable addition",
    )

    fermentable = models.ForeignKey(
        Fermentable,
        on_delete=models.CASCADE,
        help_text="Fermentable added to the recipe",
    )

    quantity = models.FloatField(
        help_text="Quantity of fermentable added to the recipe",
    )

    quantity_unit = models.CharField(
        max_length=2,
        choices=QuantityUnit.choices,
        default=QuantityUnit.POUNDS,
        help_text="Unit of measurement for the quantity of fermentable",
    )

    efficiency_percent = models.FloatField(
        null=True,
        blank=True,
        help_text="Mash efficiency for this fermentable (leave blank to use recipe default)",
    )

    notes = models.TextField(
        blank=True,
        default="",
        help_text="Notes for special instructions",
    )

    class Meta:
        ordering = ["-quantity", "fermentable__name"]

    def __str__(self) -> str:
        return f"{self.id} - {self.fermentable.name} (id: {self.fermentable.id}) - {self.quantity} {self.quantity_unit} ({self.efficiency_percent}% efficiency)"

    @property
    def quantity_in_pounds(self) -> float:
        return QuantityUnit(self.quantity_unit).to_pounds(self.quantity)

    @property
    def gravity_points_contribution(self) -> float:
        efficiency = self.efficiency_percent or self.recipe.mash_efficiency_percent

        return (
            self.quantity_in_pounds
            * self.fermentable.extract_potential_ppg
            * (efficiency / 100.0)
        ) / self.recipe.batch_size_gallons

    @property
    def srm_contribution(self) -> float:
        return (
            self.quantity_in_pounds
            * self.fermentable.color_lovibond
            / self.recipe.batch_size_gallons
        )

    def _get_id_prefix(self) -> str:
        return "fri"
