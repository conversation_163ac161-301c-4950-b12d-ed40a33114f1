from django.db import models

from .base import BaseModel


class FermentationPhaseType(models.TextChoices):
    PRIMARY = "PRIMARY", "Primary Fermentation"
    SECONDARY = "SECONDARY", "Secondary Fermentation"
    CONDITIONING = "CONDITIONING", "Conditioning"
    DIACETYL_REST = "DIACETYL_REST", "Diacetyl Rest"
    COLD_CRASH = "COLD_CRASH", "Cold Crash"
    LAGERING = "LAGERING", "Lagering"


class FermentationPhase(BaseModel):
    """Individual phase in a fermentation schedule."""

    recipe = models.ForeignKey(
        "Recipe",
        on_delete=models.CASCADE,
        help_text="Recipe containing this fermentation phase",
    )

    name = models.Char<PERSON>ield(max_length=100, help_text="Name of the fermentation phase")

    phase_type = models.CharField(
        max_length=15,
        choices=FermentationPhaseType.choices,
        default=FermentationPhaseType.PRIMARY,
        help_text="Type of fermentation phase",
    )

    temperature_fahrenheit = models.FloatField(
        help_text="Target temperature for this phase in Fahrenheit"
    )

    duration_days = models.IntegerField(help_text="Duration of this phase in days")

    phase_order = models.PositiveIntegerField(
        help_text="Order of this phase in the fermentation schedule"
    )

    gravity_target = models.FloatField(
        null=True,
        blank=True,
        help_text="Target specific gravity for this phase (optional)",
    )

    pressure_psi = models.FloatField(
        null=True,
        blank=True,
        help_text="Pressure for this phase in PSI (for pressure fermentation)",
    )

    description = models.TextField(
        blank=True,
        default="",
        help_text="Description or notes about this fermentation phase",
    )

    @property
    def duration_display(self) -> str:
        """Display duration in a human-readable format."""
        if self.duration_days == 1:
            return "1 day"
        elif self.duration_days < 7:
            return f"{self.duration_days} days"
        else:
            weeks = self.duration_days // 7
            remaining_days = self.duration_days % 7
            if remaining_days == 0:
                return f"{weeks} week{'s' if weeks != 1 else ''}"
            else:
                return f"{weeks} week{'s' if weeks != 1 else ''} {remaining_days} day{'s' if remaining_days != 1 else ''}"

    @property
    def temperature_display(self) -> str:
        """Display temperature with unit."""
        return f"{self.temperature_fahrenheit:.0f}°F"

    class Meta:
        ordering = ["recipe", "phase_order"]
        unique_together = ["recipe", "phase_order"]

    def __str__(self) -> str:
        return f"{self.name} - {self.temperature_display} for {self.duration_display}"

    def _get_id_prefix(self) -> str:
        return "fph"
