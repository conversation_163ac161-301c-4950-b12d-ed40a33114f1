import math

from django.db import models

from .base import BaseModel
from .ingredients import Hop
from .units import QuantityUnit


class HopInclusion(BaseModel):
    recipe = models.ForeignKey(
        "Recipe",
        on_delete=models.CASCADE,
    )

    hop = models.ForeignKey(
        Hop,
        on_delete=models.CASCADE,
    )

    quantity = models.FloatField()
    quantity_unit = models.CharField(
        max_length=2,
        choices=QuantityUnit.choices,
        default=QuantityUnit.OUNCES,
    )

    notes = models.TextField(
        blank=True,
        default="",
    )

    @property
    def quantity_in_ounces(self) -> float:
        return QuantityUnit(self.quantity_unit).to_ounces(self.quantity)

    class Meta:
        abstract = True

    @property
    def is_bittering_addition(self) -> bool:
        return False

    @property
    def is_aroma_addition(self) -> bool:
        return False

    @property
    def ibu_contribution(self) -> float:
        return 0.0

    def _calculate_ibu_with_time(self, boil_time_minutes: float) -> float:
        # Calculate mg/L of alpha acids
        alpha_acid_mgl = (
            self.quantity_in_ounces * (self.hop.alpha_acid / 100.0) * 7490
        ) / self.recipe.batch_size_gallons

        # Calculate utilization using Tinseth formula
        # Gravity factor: f(gravity) = 1.65 × 0.000125^(gravity - 1)
        gravity_factor = 1.65 * (0.000125 ** (self.recipe.original_gravity - 1.0))

        # Time factor: f(time) = (1 - e^(-0.04 × time)) / 4.15
        time_factor = (1 - math.exp(-0.04 * boil_time_minutes)) / 4.15

        utilization = gravity_factor * time_factor

        return alpha_acid_mgl * utilization

    def _get_id_prefix(self) -> str:
        return "hop"


class BoilHopInclusion(HopInclusion):
    time_minutes = models.PositiveIntegerField(help_text="Minutes before end of boil")

    class Meta:
        ordering = ["-time_minutes"]

    def __str__(self) -> str:
        return f"{self.hop.name} - {self.time_minutes}min boil ({self.quantity} {self.quantity_unit})"

    @property
    def is_bittering_addition(self) -> bool:
        return self.time_minutes >= 30

    @property
    def is_aroma_addition(self) -> bool:
        return self.time_minutes < 15

    @property
    def ibu_contribution(self) -> float:
        return self._calculate_ibu_with_time(float(self.time_minutes))

    def _get_id_prefix(self) -> str:
        return "bhl"


class FirstWortHopInclusion(HopInclusion):
    def __str__(self) -> str:
        return (
            f"{self.hop.name} - First Wort Hop ({self.quantity} {self.quantity_unit})"
        )

    @property
    def is_bittering_addition(self) -> bool:
        return True

    @property
    def ibu_contribution(self) -> float:
        return self._calculate_ibu_with_time(70.0)

    def _get_id_prefix(self) -> str:
        return "fwh"


class FlameoutHopInclusion(HopInclusion):
    def __str__(self) -> str:
        return f"{self.hop.name} - Flameout ({self.quantity} {self.quantity_unit})"

    @property
    def is_aroma_addition(self) -> bool:
        return True

    def _get_id_prefix(self) -> str:
        return "flo"


class WhirlpoolHopInclusion(HopInclusion):
    time_minutes = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Duration of whirlpool in minutes",
    )

    temperature_f = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Temperature in Fahrenheit for whirlpool",
    )

    def __str__(self) -> str:
        temp_str = f" @ {self.temperature_f}°F" if self.temperature_f else ""
        time_str = f" for {self.time_minutes}min" if self.time_minutes else ""
        return f"{self.hop.name} - Whirlpool{temp_str}{time_str} ({self.quantity} {self.quantity_unit})"

    @property
    def is_aroma_addition(self) -> bool:
        return True

    @property
    def ibu_contribution(self) -> float:
        if self.temperature_f and self.temperature_f >= 180:
            effective_time = 10.0
        elif self.temperature_f and self.temperature_f >= 160:
            effective_time = 5.0
        else:
            effective_time = 2.0
        return self._calculate_ibu_with_time(effective_time)

    def _get_id_prefix(self) -> str:
        return "whl"


class DryHopInclusion(HopInclusion):
    time_days = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Duration of dry hop in days",
    )

    def __str__(self) -> str:
        days_str = f" for {self.time_days} days" if self.time_days else ""
        return f"{self.hop.name} - Dry Hop{days_str} ({self.quantity} {self.quantity_unit})"

    def is_aroma_addition(self) -> bool:
        return True

    def _get_id_prefix(self) -> str:
        return "dry"
