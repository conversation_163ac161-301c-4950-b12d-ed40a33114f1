from django.db import models

from .base import BaseModel


class MashStepType(models.TextChoices):
    INFUSION = "INFUSION", "Infusion"
    TEMPERATURE = "TEMPERATURE", "Temperature"
    DECOCTION = "DECOCTION", "Decoction"


class MashStep(BaseModel):
    """Individual step in a mash schedule."""

    recipe = models.ForeignKey(
        "Recipe", on_delete=models.CASCADE, help_text="Recipe containing this mash step"
    )

    name = models.CharField(
        max_length=100,
        help_text="Name of the mash step (e.g., 'Protein Rest', 'Saccharification')",
    )

    step_type = models.CharField(
        max_length=15,
        choices=MashStepType.choices,
        default=MashStepType.INFUSION,
        help_text="Type of mash step",
    )

    temperature_fahrenheit = models.FloatField(
        help_text="Target temperature for this step in Fahrenheit"
    )

    duration_minutes = models.IntegerField(help_text="Duration of this step in minutes")

    step_order = models.PositiveIntegerField(
        help_text="Order of this step in the mash schedule"
    )

    infusion_amount_gallons = models.FloatField(
        null=True,
        blank=True,
        help_text="Amount of water to add for infusion steps (gallons)",
    )

    infusion_temperature_fahrenheit = models.FloatField(
        null=True, blank=True, help_text="Temperature of infusion water in Fahrenheit"
    )

    description = models.TextField(
        blank=True, default="", help_text="Description or notes about this mash step"
    )

    @property
    def duration_display(self) -> str:
        """Display duration in a human-readable format."""
        if self.duration_minutes >= 60:
            hours = self.duration_minutes // 60
            minutes = self.duration_minutes % 60
            if minutes == 0:
                return f"{hours} hr{'s' if hours != 1 else ''}"
            else:
                return f"{hours} hr{'s' if hours != 1 else ''} {minutes} min"
        else:
            return f"{self.duration_minutes} min"

    class Meta:
        ordering = ["recipe", "step_order"]
        unique_together = ["recipe", "step_order"]

    def __str__(self) -> str:
        return (
            f"{self.name} - {self.temperature_fahrenheit}°F for {self.duration_display}"
        )

    def _get_id_prefix(self) -> str:
        return "msh"
