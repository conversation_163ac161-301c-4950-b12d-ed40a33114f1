from django.conf import settings
from django.db import models

from .base import BaseModel
from .hop_inclusions import (
    BoilHopInclusion,
    FirstWortHopInclusion,
    WhirlpoolHopInclusion,
)


class Recipe(BaseModel):
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="User who owns this recipe",
    )

    name = models.CharField(max_length=100)
    description = models.TextField(default="")

    batch_size_gallons = models.FloatField(
        default=5.0,
        help_text="Batch size in gallons",
    )

    mash_efficiency_percent = models.FloatField(
        default=75.0,
        help_text="Mash efficiency percentage",
    )

    # Relationships to new models
    water_profile = models.ForeignKey(
        "WaterProfile",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Water profile used for this recipe",
    )

    beer_style = models.ForeignKey(
        "BeerStyle",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Beer style category for this recipe",
    )

    target_original_gravity = models.FloatField(
        default=1.050,
        help_text="Target original gravity (e.g., 1.050)",
    )

    def __str__(self) -> str:
        return str(self.name)

    def _get_id_prefix(self) -> str:
        """Get the ID prefix for Recipe model."""
        return "rec"

    @property
    def calculated_original_gravity(self) -> float:
        total_points = 0.0
        for fermentable_inclusion in self.fermentableinclusion_set.all():
            total_points += fermentable_inclusion.gravity_points_contribution

        # Convert points to gravity (1.000 + points/1000)
        return 1.000 + (total_points / 1000.0)

    @property
    def original_gravity(self) -> float:
        if self.fermentableinclusion_set.exists():
            return self.calculated_original_gravity
        return self.target_original_gravity

    @property
    def calculated_srm(self) -> float:
        total_mcu = 0.0
        for fermentable_inclusion in self.fermentableinclusion_set.all():
            total_mcu += fermentable_inclusion.srm_contribution

        if total_mcu <= 0:
            return 0.0

        srm = 1.4922 * (total_mcu**0.6859)
        return round(srm, 1)

    @property
    def total_ibus(self) -> float:
        additions = []
        additions.extend(BoilHopInclusion.objects.filter(recipe=self))
        additions.extend(FirstWortHopInclusion.objects.filter(recipe=self))
        additions.extend(WhirlpoolHopInclusion.objects.filter(recipe=self))
        return round(sum(addition.ibu_contribution for addition in additions), 1)

    @property
    def estimated_final_gravity(self) -> float:
        """
        Estimate final gravity based on original gravity and typical attenuation.
        Uses 75% attenuation as a reasonable default for most ale yeasts.
        """
        og = self.original_gravity
        # Typical attenuation for ale yeasts is 70-80%, using 75% as default
        attenuation = 0.75

        # Calculate final gravity: FG = OG - (OG - 1.000) * attenuation
        fg = og - ((og - 1.000) * attenuation)
        return round(fg, 3)

    @property
    def estimated_abv(self) -> float:
        """
        Calculate estimated ABV using the simple formula:
        ABV = (OG - FG) * 131.25
        """
        og = self.original_gravity
        fg = self.estimated_final_gravity
        abv = (og - fg) * 131.25
        return round(abv, 1)

    def _get_id_prefix(self) -> str:
        return "rec"
