from django.db import models


class QuantityUnit(models.TextChoices):
    POUNDS = "lb", "Pounds"
    OUNCES = "oz", "Ounces"
    KILOGRAMS = "kg", "Kilograms"
    GRAMS = "g", "Grams"

    def to_pounds(self, quantity: float) -> float:
        if self == self.POUNDS:
            return quantity
        elif self == self.OUNCES:
            return quantity / 16.0
        elif self == self.KILOGRAMS:
            return quantity * 2.20462
        elif self == self.GRAMS:
            return quantity / 453.592
        return quantity

    def to_ounces(self, quantity: float) -> float:
        if self == self.POUNDS:
            return quantity * 16.0
        elif self == self.OUNCES:
            return quantity
        elif self == self.KILOGRAMS:
            return quantity * 35.274
        elif self == self.GRAMS:
            return quantity / 28.35
        return quantity
