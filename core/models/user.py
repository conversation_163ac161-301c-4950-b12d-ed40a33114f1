"""
User model for the hoplogic application.

This module contains the custom User model that inherits from BaseModel
to provide stripe-like IDs, soft deletes, and timestamps.
"""

from django.contrib.auth.base_user import BaseUserManager
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.db import models
from django.utils import timezone

from .base import BaseModel


class UserManager(BaseUserManager):
    """Custom manager for the User model."""

    def create_user(self, email: str, password: str = None, **extra_fields):
        """Create and return a regular user with an email and password."""
        if not email:
            raise ValueError("The Email field must be set")

        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email: str, password: str = None, **extra_fields):
        """Create and return a superuser with an email and password."""
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)

        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser must have is_staff=True.")
        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser must have is_superuser=True.")

        return self.create_user(email, password, **extra_fields)


class User(BaseModel, AbstractBaseUser, PermissionsMixin):
    """
    Custom User model that inherits from BaseModel.

    Features:
    - Email-based authentication instead of username
    - Stripe-like ID generation from BaseModel
    - Soft delete functionality from BaseModel
    - Automatic timestamps from BaseModel
    """

    email = models.EmailField(unique=True, help_text="Email address for authentication")

    first_name = models.CharField(
        max_length=150, blank=True, help_text="User's first name"
    )

    last_name = models.CharField(
        max_length=150, blank=True, help_text="User's last name"
    )

    is_active = models.BooleanField(
        default=True,
        help_text="Designates whether this user should be treated as active.",
    )

    is_staff = models.BooleanField(
        default=False,
        help_text="Designates whether the user can log into the admin site.",
    )

    date_joined = models.DateTimeField(
        default=timezone.now, help_text="Date when the user account was created"
    )

    objects = UserManager()

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = []

    class Meta:
        verbose_name = "User"
        verbose_name_plural = "Users"
        ordering = ["-created_at"]
        db_table = "core_user"

    def __str__(self) -> str:
        """Return string representation of the user."""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        else:
            return self.email

    @property
    def full_name(self) -> str:
        """Return the user's full name."""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        else:
            return self.email

    @property
    def short_name(self) -> str:
        """Return the user's short name."""
        return self.first_name or self.email

    def _get_id_prefix(self) -> str:
        """Get the ID prefix for User model."""
        return "usr"
