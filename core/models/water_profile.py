from django.db import models

from .base import BaseModel


class WaterProfile(BaseModel):
    """Water chemistry profile for brewing."""

    name = models.CharField(
        max_length=100,
        help_text="Name of the water profile (e.g., 'Burton on Trent', 'Balanced')",
    )

    description = models.TextField(
        blank=True,
        default="",
        help_text="Description of the water profile and suitable beer styles",
    )

    # Major ions in ppm (parts per million)
    calcium_ppm = models.FloatField(
        default=0.0, help_text="Calcium (Ca²⁺) content in ppm"
    )

    magnesium_ppm = models.FloatField(
        default=0.0, help_text="Magnesium (Mg²⁺) content in ppm"
    )

    sodium_ppm = models.FloatField(default=0.0, help_text="Sodium (Na⁺) content in ppm")

    sulfate_ppm = models.FloatField(
        default=0.0, help_text="Sulfate (SO₄²⁻) content in ppm"
    )

    chloride_ppm = models.FloatField(
        default=0.0, help_text="Chloride (Cl⁻) content in ppm"
    )

    bicarbonate_ppm = models.FloatField(
        default=0.0, help_text="Bicarbonate (HCO₃⁻) content in ppm"
    )

    # Calculated properties
    @property
    def sulfate_to_chloride_ratio(self) -> float:
        """Calculate the sulfate to chloride ratio."""
        if self.chloride_ppm == 0:
            return float("inf") if self.sulfate_ppm > 0 else 0.0
        return self.sulfate_ppm / self.chloride_ppm

    @property
    def total_hardness_ppm(self) -> float:
        """Calculate total hardness as CaCO₃ equivalent."""
        # Ca hardness = Ca * 2.497, Mg hardness = Mg * 4.118
        return (self.calcium_ppm * 2.497) + (self.magnesium_ppm * 4.118)

    class Meta:
        ordering = ["name"]

    def __str__(self) -> str:
        return f"{self.name} - Ca: {self.calcium_ppm}, SO₄: {self.sulfate_ppm}, Cl: {self.chloride_ppm}"

    def _get_id_prefix(self) -> str:
        return "wtr"
