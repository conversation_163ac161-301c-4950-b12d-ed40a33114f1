from django.db import models

from .base import BaseModel


class YeastType(models.TextChoices):
    ALE = "ALE", "Ale Yeast"
    LAGER = "LAGER", "Lager Yeast"
    WILD = "WILD", "Wild Yeast"
    BRETT = "BRETT", "Brettanomyces"
    BACTERIA = "BACTERIA", "Bacteria"


class YeastForm(models.TextChoices):
    DRY = "DRY", "Dry"
    LIQUID = "LIQUID", "Liquid"
    SLANT = "SLANT", "Slant"
    CULTURE = "CULTURE", "Culture"


class Yeast(BaseModel):
    """Yeast strain for brewing."""

    name = models.CharField(max_length=100, help_text="Name of the yeast strain")

    laboratory = models.CharField(
        max_length=50,
        blank=True,
        default="",
        help_text="Laboratory or manufacturer (e.g., Wyeast, White Labs, Lallemand)",
    )

    product_id = models.CharField(
        max_length=20,
        blank=True,
        default="",
        help_text="Product ID or strain number (e.g., WLP001, US-05)",
    )

    yeast_type = models.CharField(
        max_length=10,
        choices=YeastType.choices,
        default=YeastType.ALE,
        help_text="Type of yeast",
    )

    yeast_form = models.CharField(
        max_length=10,
        choices=YeastForm.choices,
        default=YeastForm.DRY,
        help_text="Form of yeast",
    )

    min_temperature_fahrenheit = models.FloatField(
        help_text="Minimum fermentation temperature in Fahrenheit"
    )

    max_temperature_fahrenheit = models.FloatField(
        help_text="Maximum fermentation temperature in Fahrenheit"
    )

    attenuation_percent = models.FloatField(help_text="Expected attenuation percentage")

    flocculation = models.CharField(
        max_length=20,
        choices=[
            ("LOW", "Low"),
            ("MEDIUM", "Medium"),
            ("HIGH", "High"),
        ],
        default="MEDIUM",
        help_text="Flocculation characteristics",
    )

    alcohol_tolerance_percent = models.FloatField(
        null=True, blank=True, help_text="Alcohol tolerance percentage"
    )

    description = models.TextField(
        blank=True,
        default="",
        help_text="Description of yeast characteristics and suitable beer styles",
    )

    @property
    def temperature_range_display(self) -> str:
        """Display temperature range as a string."""
        return f"{self.min_temperature_fahrenheit:.0f}-{self.max_temperature_fahrenheit:.0f}°F"

    class Meta:
        ordering = ["laboratory", "name"]

    def __str__(self) -> str:
        lab_prefix = f"{self.laboratory} " if self.laboratory else ""
        product_suffix = f" ({self.product_id})" if self.product_id else ""
        return f"{lab_prefix}{self.name}{product_suffix}"

    def _get_id_prefix(self) -> str:
        return "yst"


class YeastInclusion(BaseModel):
    """Yeast addition to a recipe."""

    recipe = models.ForeignKey(
        "Recipe",
        on_delete=models.CASCADE,
        help_text="Recipe containing this yeast addition",
    )

    yeast = models.ForeignKey(
        Yeast, on_delete=models.CASCADE, help_text="Yeast strain added to the recipe"
    )

    quantity = models.FloatField(help_text="Quantity of yeast (packets, vials, etc.)")

    quantity_unit = models.CharField(
        max_length=20,
        choices=[
            ("PACKET", "Packet"),
            ("VIAL", "Vial"),
            ("POUCH", "Pouch"),
            ("ML", "mL"),
            ("GRAMS", "Grams"),
        ],
        default="PACKET",
        help_text="Unit of measurement for yeast quantity",
    )

    starter_made = models.BooleanField(
        default=False, help_text="Whether a yeast starter was made"
    )

    starter_size_ml = models.FloatField(
        null=True, blank=True, help_text="Size of yeast starter in mL"
    )

    notes = models.TextField(
        blank=True,
        default="",
        help_text="Notes about yeast handling or starter preparation",
    )

    class Meta:
        ordering = ["yeast__name"]

    def __str__(self) -> str:
        return f"{self.yeast.name} - {self.quantity} {self.quantity_unit}"

    def _get_id_prefix(self) -> str:
        return "ysi"
