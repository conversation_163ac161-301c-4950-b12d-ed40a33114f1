"""
Base repository class for the hoplogic application.

This module contains the base repository class that provides common
database operations for all model repositories.
"""

from typing import Generic, Type, TypeVar

from django.core.exceptions import ObjectDoesNotExist
from django.db import models

ModelType = TypeVar("ModelType", bound=models.Model)


class DjangoDatabaseModelRepositoryMixin(Generic[ModelType]):
    """
    Base repository class that provides common database operations.

    This class encapsulates basic CRUD operations and can be extended
    by specific model repositories.
    """

    model_class: Type[ModelType]

    def get_by_id(self, id: str) -> ModelType | None:
        """Get a model instance by ID."""
        try:
            return self.get_by_id_or_raise(id)
        except ObjectDoesNotExist:
            return None

    def get_by_id_or_raise(self, id: str) -> ModelType:
        """Get a model instance by ID or raise exception if not found."""
        return self.model_class.objects.get(id=id)

    def get_all(self) -> list[ModelType]:
        """Get all model instances."""
        return list(self.model_class.objects.all())

    def filter(self, **kwargs) -> list[ModelType]:
        """Filter model instances by given criteria."""
        return list(self.model_class.objects.filter(**kwargs))

    def create(self, **kwargs) -> ModelType:
        """Create a new model instance."""
        return self.model_class.objects.create(**kwargs)

    def update(self, instance: ModelType, **kwargs) -> ModelType:
        """Update an existing model instance."""
        for key, value in kwargs.items():
            setattr(instance, key, value)
        instance.save()
        return instance

    def delete(self, instance: ModelType) -> None:
        """Delete a model instance (soft delete if supported)."""
        instance.delete()

    def exists(self, **kwargs) -> bool:
        """Check if a model instance exists with given criteria.

        Args:
            **kwargs: Filtering criteria where key is field name and value is match value.

        returns:
            bool: True if instance exists, False otherwise.
        """
        return self.model_class.objects.filter(**kwargs).exists()

    def count(self, **kwargs) -> int:
        """Count model instances with optional filtering criteria.

        Args:
            **kwargs: Optional filtering criteria where key is field name and value is match value.

        Returns:
            int: Number of model instances matching the criteria.
        """
        objects = self.model_class.objects
        if kwargs:
            objects = objects.filter(**kwargs)
        return objects.count()
