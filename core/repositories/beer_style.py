from abc import ABC, abstractmethod

from ..di import injectable
from ..models import BeerStyle
from .base import DjangoDatabaseModelRepositoryMixin


class BeerStyleRepository(ABC):
    """Repository for BeerStyle model operations.

    This repository provides methods for searching and retrieving beer styles.
    """

    @abstractmethod
    def get_by_id(self, style_id: str) -> BeerStyle | None:
        """Get a beer style by ID.

        Args:
            style_id (str): The ID of the beer style to get.

        Returns:
            BeerStyle | None: The beer style if found, None otherwise.
        """
        ...

    @abstractmethod
    def search_freetext(self, query: str) -> list[BeerStyle]:
        """Search beer styles via a free-text search.

        Args:
            query (str): The search query.

        Returns:
            list[BeerStyle]: A list of beer styles that match the query.
        """
        ...

    @abstractmethod
    def get_default_style(self) -> BeerStyle:
        """Get the default all-inclusive beer style.

        Returns:
            BeerStyle: The default beer style.
        """
        ...


@injectable(interface=BeerStyleRepository)
class DjangoDatabaseBeerStyleRepository(
    BeerStyleRepository,
    DjangoDatabaseModelRepositoryMixin[BeerStyle],
):
    model_class = BeerStyle

    def get_by_id(self, style_id: str) -> BeerStyle | None:
        try:
            return self.model_class.objects.get(id=style_id)
        except self.model_class.DoesNotExist:
            return None

    def search_freetext(self, name_query: str) -> list[BeerStyle]:
        # TODO: Do this as a vector embeddings search instead of a text search
        return self.filter(name__icontains=name_query)

    def get_default_style(self) -> BeerStyle:
        return self.filter(name="Custom Style").pop()
