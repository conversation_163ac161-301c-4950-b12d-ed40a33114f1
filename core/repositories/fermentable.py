"""
Fermentable repository for the hoplogic application.

This module contains the repository class for Fermentable model operations.
"""

from abc import ABC, abstractmethod

from ..di import injectable
from ..models import Fermentable, FermentableType
from .base import DjangoDatabaseModelRepositoryMixin


class FermentableRepository(ABC):
    @abstractmethod
    def get_by_id(self, fermentable_id: str) -> Fermentable | None: ...

    @abstractmethod
    def get_by_type(self, fermentable_type: FermentableType) -> list[Fermentable]: ...

    @abstractmethod
    def get_specialty_malts(self) -> list[Fermentable]: ...

    @abstractmethod
    def search_by_name(self, name_query: str) -> list[Fermentable]: ...

    @abstractmethod
    def get_by_country(self, country: str) -> list[Fermentable]: ...

    @abstractmethod
    def get_extracts(self) -> list[Fermentable]: ...


@injectable(interface=FermentableRepository)
class DjangoDatabaseFermentableRepository(
    FermentableRepository,
    DjangoDatabaseModelRepositoryMixin[Fermentable],
):
    """Repository for Fermentable model operations."""

    model_class = Fermentable

    def get_by_id(self, fermentable_id: str) -> Fermentable | None:
        try:
            return self.model_class.objects.get(id=fermentable_id)
        except self.model_class.DoesNotExist:
            return None

    def get_by_type(self, fermentable_type: FermentableType) -> list[Fermentable]:
        return self.filter(fermentable_type=fermentable_type)

    def search_by_name(self, name_query: str) -> list[Fermentable]:
        return self.filter(name__icontains=name_query)

    def get_by_country(self, country: str) -> list[Fermentable]:
        return self.filter(country_of_origin__iexact=country)

    def get_extracts(self) -> list[Fermentable]:
        return self.filter(requires_mashing=False)
