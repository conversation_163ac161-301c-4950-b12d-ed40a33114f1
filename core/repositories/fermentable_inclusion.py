"""
Fermentable inclusion repository for the hoplogic application.

This module contains the repository class for FermentableInclusion model operations.
"""

from abc import ABC, abstractmethod

from ..di import injectable
from ..models import Fermentable, FermentableInclusion, Recipe
from .base import DjangoDatabaseModelRepositoryMixin


class FermentableInclusionRepository(ABC):
    @abstractmethod
    def get_by_recipe(self, recipe_id: str) -> list[FermentableInclusion]:
        """Get all fermentable inclusions for a recipe.

        Args:
            recipe_id (str): The ID of the recipe to get the fermentable inclusions for.

        Returns:
            list[FermentableInclusion]: The fermentable inclusions for the recipe.

        Notes:
            This will include the fermentable data for each inclusion. Use this
            to get the fermentable and quantity for each fermentable. Note it may
            return an empty list if there are no fermentables in the recipe.
        """
        ...

    @abstractmethod
    def get_by_recipe_and_fermentable(
        self, recipe_id: str, fermentable_id: str
    ) -> FermentableInclusion | None:
        """Get a specific fermentable inclusion by recipe and fermentable.

        Args:
            recipe_id (str): The ID of the recipe to get the fermentable inclusion for.
            fermentable_id (str): The ID of the fermentable to get the inclusion for.

        Returns:
            FermentableInclusion | None: The fermentable inclusion if found, None otherwise.
        """
        ...

    @abstractmethod
    def create(
        self,
        recipe: Recipe,
        fermentable: Fermentable,
        quantity: float,
        quantity_unit: str,
        efficiency_percent: float | None = None,
        notes: str = "",
    ) -> FermentableInclusion:
        """Create a new fermentable inclusion.

        Args:
            recipe (Recipe): The recipe to add the fermentable to.
            fermentable (Fermentable): The fermentable to add to the recipe.
            quantity (float): The quantity of the fermentable to add.
            quantity_unit (str): The unit of measurement for the quantity.
            efficiency_percent (float, optional): The mash efficiency for this fermentable. Defaults to None.

        Returns:
            FermentableInclusion: The newly created fermentable inclusion.

        Notes:
            If efficiency_percent is not provided, the recipe's default efficiency will be used.
        """
        ...

    @abstractmethod
    def delete(self, inclusion_id: str) -> bool:
        """Delete a fermentable inclusion by ID.

        Args:
            inclusion_id (str): The ID of the fermentable inclusion to delete.

        Returns:
            bool: True if the inclusion was deleted, False otherwise.
        """
        ...


@injectable(interface=FermentableInclusionRepository)
class DjangoDatabaseFermentableInclusionRepository(
    FermentableInclusionRepository,
    DjangoDatabaseModelRepositoryMixin[FermentableInclusion],
):
    model_class = FermentableInclusion

    def get_by_recipe(self, recipe_id: str) -> list[FermentableInclusion]:
        return list(
            self.model_class.objects.select_related("fermentable").filter(
                recipe_id=recipe_id
            )
        )

    def get_by_recipe_and_fermentable(
        self, recipe_id: str, fermentable_id: str
    ) -> FermentableInclusion | None:
        try:
            return self.model_class.objects.get(
                recipe_id=recipe_id, fermentable_id=fermentable_id
            )
        except self.model_class.DoesNotExist:
            return None

    def create(
        self,
        recipe: Recipe,
        fermentable: Fermentable,
        quantity: float,
        quantity_unit: str,
        efficiency_percent: float | None = None,
        notes: str = "",
    ) -> FermentableInclusion:
        return self.model_class.objects.create(
            recipe=recipe,
            fermentable=fermentable,
            quantity=quantity,
            quantity_unit=quantity_unit,
            efficiency_percent=efficiency_percent,
            notes=notes,
        )

    def delete(self, inclusion_id: str) -> bool:
        try:
            self.model_class.objects.get(id=inclusion_id).delete()
            return True
        except self.model_class.DoesNotExist:
            return False
