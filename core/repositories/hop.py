"""
Hop repository for the hoplogic application.

This module contains the repository class for Hop model operations.
"""

from abc import ABC, abstractmethod

from ..di import injectable
from ..models import Hop
from .base import DjangoDatabaseModelRepositoryMixin


class HopRepository(ABC):
    @abstractmethod
    def get_by_id(self, hop_id: str) -> Hop | None: ...

    @abstractmethod
    def get_aroma_hops(self) -> list[Hop]: ...

    @abstractmethod
    def get_bittering_hops(self) -> list[Hop]: ...

    @abstractmethod
    def get_dual_purpose_hops(self) -> list[Hop]: ...

    @abstractmethod
    def search(self, name_query: str) -> list[Hop]: ...

    @abstractmethod
    def get_by_country(self, country: str) -> list[Hop]: ...

    @abstractmethod
    def get_by_alpha_acid_range(
        self, min_alpha: float, max_alpha: float
    ) -> list[Hop]: ...


@injectable(interface=HopRepository)
class DjangoDatabaseHopRepository(
    HopRepository,
    DjangoDatabaseModelRepositoryMixin[Hop],
):
    """Repository for Hop model operations."""

    model_class = Hop

    def get_by_id(self, hop_id: str) -> Hop | None:
        try:
            return self.model_class.objects.get(id=hop_id)
        except self.model_class.DoesNotExist:
            return None

    def get_aroma_hops(self) -> list[Hop]:
        return self.filter(aroma=True)

    def get_bittering_hops(self) -> list[Hop]:
        return self.filter(bittering=True)

    def get_dual_purpose_hops(self) -> list[Hop]:
        return self.filter(aroma=True, bittering=True)

    def search(self, name_query: str) -> list[Hop]:
        return self.filter(name__icontains=name_query)

    def get_by_country(self, country: str) -> list[Hop]:
        return self.filter(country_of_origin__iexact=country)

    def get_by_alpha_acid_range(self, min_alpha: float, max_alpha: float) -> list[Hop]:
        return self.filter(alpha_acid__gte=min_alpha, alpha_acid__lte=max_alpha)
