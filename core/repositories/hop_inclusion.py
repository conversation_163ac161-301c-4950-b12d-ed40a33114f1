from abc import ABC, abstractmethod

from ..di import injectable
from ..models import (
    BoilHopInclusion,
    DryHopInclusion,
    FirstWortHopInclusion,
    FlameoutHopInclusion,
    Hop,
    Recipe,
    WhirlpoolHopInclusion,
)
from .base import DjangoDatabaseModelRepositoryMixin


class FlameoutHopInclusionRepository(ABC):
    """A repository for flameout hop inclusions."""

    @abstractmethod
    def get_by_recipe(self, recipe_id: str) -> list[FlameoutHopInclusion]:
        """Get all flameout hop inclusions for a recipe."""
        ...

    @abstractmethod
    def delete(self, inclusion_id: str) -> bool:
        """Delete a flameout hop inclusion."""
        ...

    @abstractmethod
    def create(
        self,
        hop: Hop,
        recipe: Recipe,
        quantity: float,
        quantity_unit: str,
        notes: str = "",
    ) -> FlameoutHopInclusion:
        """Create a new flameout hop inclusion."""
        ...


class DryHopInclusionRepository(ABC):
    """A repository for dry hop inclusions."""

    @abstractmethod
    def get_by_recipe(self, recipe_id: str) -> list[DryHopInclusion]:
        """Get all dry hop inclusions for a recipe."""
        ...

    @abstractmethod
    def delete(self, inclusion_id: str) -> bool:
        """Delete a dry hop inclusion."""
        ...

    @abstractmethod
    def create(
        self,
        hop: Hop,
        recipe: Recipe,
        quantity: float,
        quantity_unit: str,
        time_days: int | None = None,
        notes: str = "",
    ) -> DryHopInclusion:
        """Create a new dry hop inclusion."""
        ...


class BoilHopInclusionRepository(ABC):
    """A repository for boil hop inclusions."""

    @abstractmethod
    def get_by_recipe(self, recipe_id: str) -> list[BoilHopInclusion]:
        """Get all boil hop inclusions for a recipe."""
        ...

    @abstractmethod
    def delete(self, inclusion_id: str) -> bool:
        """Delete a boil hop inclusion."""
        ...

    @abstractmethod
    def create(
        self,
        hop: Hop,
        recipe: Recipe,
        quantity: float,
        quantity_unit: str,
        time_minutes: int | None = None,
        notes: str = "",
    ) -> BoilHopInclusion:
        """Create a new boil hop inclusion."""
        ...


class FirstWortHopInclusionRepository(ABC):
    """A repository for first wort hop inclusions."""

    @abstractmethod
    def get_by_recipe(self, recipe_id: str) -> list[FirstWortHopInclusion]:
        """Get all first wort hop inclusions for a recipe."""
        ...

    @abstractmethod
    def delete(self, inclusion_id: str) -> bool:
        """Delete a first wort hop inclusion."""
        ...

    @abstractmethod
    def create(
        self,
        hop: Hop,
        recipe: Recipe,
        quantity: float,
        quantity_unit: str,
        notes: str = "",
    ) -> FirstWortHopInclusion:
        """Create a new first wort hop inclusion."""
        ...


class WhirlpoolHopInclusionRepository(ABC):
    """A repository for whirlpool hop inclusions."""

    @abstractmethod
    def get_by_recipe(self, recipe_id: str) -> list[WhirlpoolHopInclusion]:
        """Get all whirlpool hop inclusions for a recipe."""
        ...

    @abstractmethod
    def delete(self, inclusion_id: str) -> bool:
        """Delete a whirlpool hop inclusion."""
        ...

    @abstractmethod
    def create(
        self,
        hop: Hop,
        recipe: Recipe,
        quantity: float,
        quantity_unit: str,
        time_minutes: int | None = None,
        temperature_f: int | None = None,
        notes: str = "",
    ) -> WhirlpoolHopInclusion:
        """Create a new whirlpool hop inclusion."""
        ...


@injectable(interface=BoilHopInclusionRepository)
class DjangoDatabaseBoilHopInclusionRepository(
    BoilHopInclusionRepository,
    DjangoDatabaseModelRepositoryMixin[BoilHopInclusion],
):
    model_class = BoilHopInclusion

    def get_by_recipe(self, recipe_id: str) -> list[BoilHopInclusion]:
        return list(
            self.model_class.objects.select_related("hop")
            .filter(recipe_id=recipe_id)
            .order_by("-time_minutes")
        )

    def delete(self, inclusion_id: str) -> bool:
        try:
            self.model_class.objects.get(id=inclusion_id).delete()
            return True
        except self.model_class.DoesNotExist:
            return False

    def create(
        self,
        hop: Hop,
        recipe: Recipe,
        quantity: float,
        quantity_unit: str,
        time_minutes: int | None = None,
        notes: str = "",
    ) -> BoilHopInclusion:
        return self.model_class.objects.create(
            hop=hop,
            recipe=recipe,
            quantity=quantity,
            quantity_unit=quantity_unit,
            time_minutes=time_minutes,
            notes=notes,
        )


@injectable(interface=FirstWortHopInclusionRepository)
class DjangoDatabaseFirstWortHopInclusionRepository(
    FirstWortHopInclusionRepository,
    DjangoDatabaseModelRepositoryMixin[FirstWortHopInclusion],
):
    model_class = FirstWortHopInclusion

    def get_by_recipe(self, recipe_id: str) -> list[FirstWortHopInclusion]:
        return list(
            self.model_class.objects.select_related("hop").filter(recipe_id=recipe_id)
        )

    def delete(self, inclusion_id: str) -> bool:
        try:
            self.model_class.objects.get(id=inclusion_id).delete()
            return True
        except self.model_class.DoesNotExist:
            return False

    def create(
        self,
        hop: Hop,
        recipe: Recipe,
        quantity: float,
        quantity_unit: str,
        notes: str = "",
    ) -> FirstWortHopInclusion:
        return self.model_class.objects.create(
            hop=hop,
            recipe=recipe,
            quantity=quantity,
            quantity_unit=quantity_unit,
            notes=notes,
        )


@injectable(interface=FlameoutHopInclusionRepository)
class DjangoDatabaseFlameoutHopInclusionRepository(
    FlameoutHopInclusionRepository,
    DjangoDatabaseModelRepositoryMixin[FlameoutHopInclusion],
):
    model_class = FlameoutHopInclusion

    def get_by_recipe(self, recipe_id: str) -> list[FlameoutHopInclusion]:
        return list(
            self.model_class.objects.select_related("hop").filter(recipe_id=recipe_id)
        )

    def delete(self, inclusion_id: str) -> bool:
        try:
            self.model_class.objects.get(id=inclusion_id).delete()
            return True
        except self.model_class.DoesNotExist:
            return False

    def create(
        self,
        hop: Hop,
        recipe: Recipe,
        quantity: float,
        quantity_unit: str,
        notes: str = "",
    ) -> FlameoutHopInclusion:
        return self.model_class.objects.create(
            hop=hop,
            recipe=recipe,
            quantity=quantity,
            quantity_unit=quantity_unit,
            notes=notes,
        )


@injectable(interface=WhirlpoolHopInclusionRepository)
class DjangoDatabaseWhirlpoolHopInclusionRepository(
    WhirlpoolHopInclusionRepository,
    DjangoDatabaseModelRepositoryMixin[WhirlpoolHopInclusion],
):
    model_class = WhirlpoolHopInclusion

    def get_by_recipe(self, recipe_id: str) -> list[WhirlpoolHopInclusion]:
        return list(
            self.model_class.objects.select_related("hop").filter(recipe_id=recipe_id)
        )

    def delete(self, inclusion_id: str) -> bool:
        try:
            self.model_class.objects.get(id=inclusion_id).delete()
            return True
        except self.model_class.DoesNotExist:
            return False

    def create(
        self,
        hop: Hop,
        recipe: Recipe,
        quantity: float,
        quantity_unit: str,
        time_minutes: int | None = None,
        temperature_f: int | None = None,
        notes: str = "",
    ) -> WhirlpoolHopInclusion:
        return self.model_class.objects.create(
            hop=hop,
            recipe=recipe,
            quantity=quantity,
            quantity_unit=quantity_unit,
            time_minutes=time_minutes,
            temperature_f=temperature_f,
            notes=notes,
        )


@injectable(interface=DryHopInclusionRepository)
class DjangoDatabaseDryHopInclusionRepository(
    DryHopInclusionRepository,
    DjangoDatabaseModelRepositoryMixin[DryHopInclusion],
):
    model_class = DryHopInclusion

    def get_by_recipe(self, recipe_id: str) -> list[DryHopInclusion]:
        return list(
            self.model_class.objects.select_related("hop").filter(recipe_id=recipe_id)
        )

    def delete(self, inclusion_id: str) -> bool:
        try:
            self.model_class.objects.get(id=inclusion_id).delete()
            return True
        except self.model_class.DoesNotExist:
            return False

    def create(
        self,
        hop: Hop,
        recipe: Recipe,
        quantity: float,
        quantity_unit: str,
        time_days: int | None = None,
        notes: str = "",
    ) -> DryHopInclusion:
        return self.model_class.objects.create(
            hop=hop,
            recipe=recipe,
            quantity=quantity,
            quantity_unit=quantity_unit,
            time_days=time_days,
            notes=notes,
        )
