"""
Mash step repository for the hoplogic application.

This module contains the repository class for MashStep model operations.
"""

from abc import ABC, abstractmethod

from ..di import injectable
from ..models import MashStep
from .base import DjangoDatabaseModelRepositoryMixin


class MashStepRepository(ABC):
    @abstractmethod
    def get_by_recipe(self, recipe_id: str) -> list[MashStep]: ...


@injectable(interface=MashStepRepository)
class DjangoDatabaseMashStepRepository(
    MashStepRepository,
    DjangoDatabaseModelRepositoryMixin[MashStep],
):
    """Repository for MashStep model operations."""

    model_class = MashStep

    def get_by_recipe(self, recipe_id: str) -> list[MashStep]:
        """Get all mash steps for a recipe, ordered by step_order."""
        return list(
            self.model_class.objects.filter(recipe_id=recipe_id).order_by("step_order")
        )
