"""
Recipe repository for the hoplogic application.

This module contains the repository class for Recipe model operations.
"""

from abc import ABC, abstractmethod

from ..di import injectable
from ..models import Recipe
from .base import DjangoDatabaseModelRepositoryMixin


class RecipeRepository(ABC):
    @abstractmethod
    def get_by_user(self, user_id: str) -> list[Recipe]: ...

    @abstractmethod
    def get_by_id(self, recipe_id: str) -> Recipe | None: ...

    @abstractmethod
    def delete(self, recipe: Recipe) -> None: ...


@injectable(interface=RecipeRepository)
class DjangoDatabaseRecipeRepository(
    RecipeRepository,
    DjangoDatabaseModelRepositoryMixin[Recipe],
):
    """Repository for Recipe model operations."""

    model_class = Recipe

    # get_by_id and delete are implemented by DjangoDatabaseModelRepositoryMixin

    def get_by_user(self, user_id: str) -> list[Recipe]:
        return list(self.filter(user_id=user_id))
