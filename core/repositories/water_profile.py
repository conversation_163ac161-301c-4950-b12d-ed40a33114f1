"""
Water profile repository for the hoplogic application.

This module contains the repository class for WaterProfile model operations.
"""

from abc import ABC, abstractmethod

from ..di import injectable
from ..models import WaterProfile
from .base import DjangoDatabaseModelRepositoryMixin


class WaterProfileRepository(ABC):
    @abstractmethod
    def search_by_name(self, name_query: str) -> list[WaterProfile]: ...

    @abstractmethod
    def get_by_name(self, name: str) -> WaterProfile | None: ...


@injectable(interface=WaterProfileRepository)
class DjangoDatabaseWaterProfileRepository(
    WaterProfileRepository,
    DjangoDatabaseModelRepositoryMixin[WaterProfile],
):
    """Repository for WaterProfile model operations."""

    model_class = WaterProfile

    def search_by_name(self, name_query: str) -> list[WaterProfile]:
        """Search water profiles by name (case-insensitive partial match)."""
        return self.filter(name__icontains=name_query)

    def get_by_name(self, name: str) -> WaterProfile | None:
        """Get a water profile by exact name."""
        try:
            return self.model_class.objects.get(name=name)
        except self.model_class.DoesNotExist:
            return None
