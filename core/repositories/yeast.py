"""
Yeast repository for the hoplogic application.

This module contains the repository class for Yeast model operations.
"""

from abc import ABC, abstractmethod

from ..di import injectable
from ..models import Recipe, Yeast, YeastForm, YeastInclusion, YeastType
from .base import DjangoDatabaseModelRepositoryMixin


class YeastRepository(ABC):
    @abstractmethod
    def get_by_id(self, yeast_id: str) -> Yeast | None: ...

    @abstractmethod
    def get_by_type(self, yeast_type: YeastType) -> list[Yeast]: ...

    @abstractmethod
    def get_by_form(self, yeast_form: YeastForm) -> list[Yeast]: ...

    @abstractmethod
    def get_by_laboratory(self, laboratory: str) -> list[Yeast]: ...

    @abstractmethod
    def search_by_name(self, name_query: str) -> list[Yeast]: ...

    @abstractmethod
    def get_by_temperature_range(
        self, min_temp: float, max_temp: float
    ) -> list[Yeast]: ...

    @abstractmethod
    def get_by_attenuation_range(
        self, min_attenuation: float, max_attenuation: float
    ) -> list[Yeast]: ...

    @abstractmethod
    def get_high_attenuation_yeasts(
        self, min_attenuation: float = 80.0
    ) -> list[Yeast]: ...



@injectable(interface=YeastRepository)
class DjangoDatabaseYeastRepository(
    YeastRepository,
    DjangoDatabaseModelRepositoryMixin[Yeast],
):
    """Repository for Yeast model operations."""

    model_class = Yeast

    def get_by_id(self, yeast_id: str) -> Yeast | None:
        try:
            return self.model_class.objects.get(id=yeast_id)
        except self.model_class.DoesNotExist:
            return None

    def get_by_type(self, yeast_type: YeastType) -> list[Yeast]:
        """Get all yeasts of a specific type."""
        return self.filter(yeast_type=yeast_type)

    def get_ale_yeasts(self) -> list[Yeast]:
        """Get all ale yeasts."""
        return self.get_by_type(YeastType.ALE)

    def get_lager_yeasts(self) -> list[Yeast]:
        """Get all lager yeasts."""
        return self.get_by_type(YeastType.LAGER)

    def get_by_form(self, yeast_form: YeastForm) -> list[Yeast]:
        """Get yeasts by form (dry, liquid, etc.)."""
        return self.filter(yeast_form=yeast_form)

    def get_by_laboratory(self, laboratory: str) -> list[Yeast]:
        """Get yeasts from a specific laboratory."""
        return self.filter(laboratory__iexact=laboratory)

    def search_by_name(self, name_query: str) -> list[Yeast]:
        """Search yeasts by name (case-insensitive partial match)."""
        return self.filter(name__icontains=name_query)

    def get_by_temperature_range(self, min_temp: float, max_temp: float) -> list[Yeast]:
        """Get yeasts suitable for a temperature range."""
        return self.filter(
            min_temperature_fahrenheit__lte=max_temp,
            max_temperature_fahrenheit__gte=min_temp,
        )

    def get_by_attenuation_range(
        self, min_attenuation: float, max_attenuation: float
    ) -> list[Yeast]:
        """Get yeasts within an attenuation range."""
        return self.filter(
            attenuation_percent__gte=min_attenuation,
            attenuation_percent__lte=max_attenuation,
        )

    def get_high_attenuation_yeasts(self, min_attenuation: float = 80.0) -> list[Yeast]:
        """Get high attenuation yeasts (default: >= 80%)."""
        return self.filter(attenuation_percent__gte=min_attenuation)


class YeastInclusionRepository(ABC):
    @abstractmethod
    def get_by_recipe(self, recipe_id: str) -> list[YeastInclusion]: ...

    @abstractmethod
    def create(
        self,
        yeast: Yeast,
        recipe: Recipe,
        quantity: float,
        quantity_unit: str,
        starter_made: bool = False,
        starter_size_ml: float | None = None,
        notes: str = "",
    ) -> YeastInclusion: ...

    @abstractmethod
    def delete(self, inclusion_id: str) -> bool: ...


@injectable(interface=YeastInclusionRepository)
class DjangoDatabaseYeastInclusionRepository(
    YeastInclusionRepository,
    DjangoDatabaseModelRepositoryMixin[YeastInclusion],
):
    """Repository for YeastInclusion model operations."""

    model_class = YeastInclusion

    def get_by_recipe(self, recipe_id: str) -> list[YeastInclusion]:
        return list(
            self.model_class.objects.select_related("yeast").filter(recipe_id=recipe_id)
        )

    def create(
        self,
        yeast: Yeast,
        recipe: Recipe,
        quantity: float,
        quantity_unit: str,
        starter_made: bool = False,
        starter_size_ml: float | None = None,
        notes: str = "",
    ) -> YeastInclusion:
        return self.model_class.objects.create(
            yeast=yeast,
            recipe=recipe,
            quantity=quantity,
            quantity_unit=quantity_unit,
            starter_made=starter_made,
            starter_size_ml=starter_size_ml,
            notes=notes,
        )

    def delete(self, inclusion_id: str) -> bool:
        try:
            self.model_class.objects.get(id=inclusion_id).delete()
            return True
        except self.model_class.DoesNotExist:
            return False
