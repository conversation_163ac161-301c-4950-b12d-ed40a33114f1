"""
Serializers package for the hoplogic core application.

This package contains Django Rest Framework serializers organized by functionality.
"""

from .beer_style import BeerStyleSerializer

# Import all serializers to maintain backward compatibility
from .chat import ChatMessageSerializer
from .inclusions import (
    BoilHopInclusionSerializer,
    DryHopInclusionSerializer,
    FermentableInclusionSerializer,
    FirstWortHopInclusionSerializer,
    FlameoutHopInclusionSerializer,
    WhirlpoolHopInclusionSerializer,
    YeastInclusionSerializer,
)
from .ingredients import (
    FermentableSerializer,
    HopSerializer,
    WaterProfileSerializer,
    YeastSerializer,
)
from .processes import (
    FermentationPhaseSerializer,
    MashStepSerializer,
)
from .recipe import RecipeSerializer

# Make all serializers available at package level for backward compatibility
__all__ = [
    # Ingredient serializers
    "FermentableSerializer",
    "HopSerializer",
    "YeastSerializer",
    "WaterProfileSerializer",
    # Inclusion serializers
    "FermentableInclusionSerializer",
    "YeastInclusionSerializer",
    "BoilHopInclusionSerializer",
    "FirstWortHopInclusionSerializer",
    "FlameoutHopInclusionSerializer",
    "WhirlpoolHopInclusionSerializer",
    "DryHopInclusionSerializer",
    # Process serializers
    "MashStepSerializer",
    "FermentationPhaseSerializer",
    # Recipe serializer
    "RecipeSerializer",
    # Chat serializer
    "ChatMessageSerializer",
    # Beer style serializer
    "BeerStyleSerializer",
]
