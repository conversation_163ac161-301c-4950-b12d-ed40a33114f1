"""
BeerStyle serializer for the hoplogic core application.

This module contains the BeerStyle serializer for API responses.
"""

from rest_framework import serializers

from ..models import BeerStyle


class BeerStyleSerializer(serializers.ModelSerializer):
    """Serializer for BeerStyle model with all fields and display properties."""

    # Include the display properties as read-only fields
    srm_range_display = serializers.ReadOnlyField()
    ibu_range_display = serializers.ReadOnlyField()
    og_range_display = serializers.ReadOnlyField()
    fg_range_display = serializers.ReadOnlyField()
    abv_range_display = serializers.ReadOnlyField()

    class Meta:
        model = BeerStyle
        fields = [
            "id",
            "name",
            "description",
            "srm_min",
            "srm_max",
            "ibu_min",
            "ibu_max",
            "og_min",
            "og_max",
            "fg_min",
            "fg_max",
            "abv_min",
            "abv_max",
            "srm_range_display",
            "ibu_range_display",
            "og_range_display",
            "fg_range_display",
            "abv_range_display",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]
