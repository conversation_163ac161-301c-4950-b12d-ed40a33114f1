"""
Chat serializers for the hoplogic core application.

This module contains serializers for chat-related functionality.
"""

from rest_framework import serializers


class ChatMessageSerializer(serializers.Serializer):
    """Serializer for chat messages sent to the agent."""

    message = serializers.Char<PERSON>ield(
        max_length=2000, help_text="The message to send to the agent"
    )

    def validate_message(self, value):
        """Validate that the message is not empty after stripping whitespace."""
        if not value.strip():
            raise serializers.ValidationError("Message cannot be empty.")
        return value.strip()
