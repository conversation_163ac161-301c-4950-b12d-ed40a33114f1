"""
Inclusion serializers for the hoplogic core application.

This module contains serializers for ingredient inclusion models like hop inclusions,
fermentable inclusions, and yeast inclusions.
"""

from rest_framework import serializers

from ..models import (
    BoilHopInclusion,
    DryHopInclusion,
    FermentableInclusion,
    FirstWortHopInclusion,
    FlameoutHopInclusion,
    WhirlpoolHopInclusion,
    YeastInclusion,
)
from .ingredients import FermentableSerializer, HopSerializer, YeastSerializer


class FermentableInclusionSerializer(serializers.ModelSerializer):
    """Serializer for FermentableInclusion model with nested fermentable data."""

    fermentable = FermentableSerializer(read_only=True)
    gravity_points_contribution = serializers.ReadOnlyField()
    srm_contribution = serializers.ReadOnlyField()

    class Meta:
        model = FermentableInclusion
        fields = [
            "id",
            "fermentable",
            "quantity",
            "quantity_unit",
            "efficiency_percent",
            "gravity_points_contribution",
            "srm_contribution",
        ]


class YeastInclusionSerializer(serializers.ModelSerializer):
    """Serializer for YeastInclusion model with nested yeast data."""

    yeast = YeastSerializer(read_only=True)

    class Meta:
        model = YeastInclusion
        fields = [
            "id",
            "yeast",
            "quantity",
            "quantity_unit",
            "starter_made",
            "starter_size_ml",
        ]


class BoilHopInclusionSerializer(serializers.ModelSerializer):
    """Serializer for BoilHopInclusion model with nested hop data."""

    hop = HopSerializer(read_only=True)
    ibu_contribution = serializers.ReadOnlyField()
    is_bittering_addition = serializers.ReadOnlyField()
    is_aroma_addition = serializers.ReadOnlyField()

    class Meta:
        model = BoilHopInclusion
        fields = [
            "id",
            "hop",
            "quantity",
            "quantity_unit",
            "time_minutes",
            "notes",
            "ibu_contribution",
            "is_bittering_addition",
            "is_aroma_addition",
        ]


class FirstWortHopInclusionSerializer(serializers.ModelSerializer):
    """Serializer for FirstWortHopInclusion model with nested hop data."""

    hop = HopSerializer(read_only=True)
    ibu_contribution = serializers.ReadOnlyField()
    is_bittering_addition = serializers.ReadOnlyField()

    class Meta:
        model = FirstWortHopInclusion
        fields = [
            "id",
            "hop",
            "quantity",
            "quantity_unit",
            "notes",
            "ibu_contribution",
            "is_bittering_addition",
        ]


class FlameoutHopInclusionSerializer(serializers.ModelSerializer):
    """Serializer for FlameoutHopInclusion model with nested hop data."""

    hop = HopSerializer(read_only=True)
    is_aroma_addition = serializers.ReadOnlyField()

    class Meta:
        model = FlameoutHopInclusion
        fields = [
            "id",
            "hop",
            "quantity",
            "quantity_unit",
            "notes",
            "is_aroma_addition",
        ]


class WhirlpoolHopInclusionSerializer(serializers.ModelSerializer):
    """Serializer for WhirlpoolHopInclusion model with nested hop data."""

    hop = HopSerializer(read_only=True)
    ibu_contribution = serializers.ReadOnlyField()
    is_aroma_addition = serializers.ReadOnlyField()

    class Meta:
        model = WhirlpoolHopInclusion
        fields = [
            "id",
            "hop",
            "quantity",
            "quantity_unit",
            "time_minutes",
            "temperature_f",
            "notes",
            "ibu_contribution",
            "is_aroma_addition",
        ]


class DryHopInclusionSerializer(serializers.ModelSerializer):
    """Serializer for DryHopInclusion model with nested hop data."""

    hop = HopSerializer(read_only=True)
    is_aroma_addition = serializers.ReadOnlyField()

    class Meta:
        model = DryHopInclusion
        fields = [
            "id",
            "hop",
            "quantity",
            "quantity_unit",
            "time_days",
            "notes",
            "is_aroma_addition",
        ]
