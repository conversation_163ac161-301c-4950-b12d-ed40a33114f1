"""
Ingredient serializers for the hoplogic core application.

This module contains serializers for ingredient models like Fermentable, Hop, Yeast, and WaterProfile.
"""

from rest_framework import serializers

from ..models import Fermentable, Hop, WaterProfile, Yeast


class FermentableSerializer(serializers.ModelSerializer):
    """Serializer for Fermentable model."""

    class Meta:
        model = Fermentable
        fields = [
            "id",
            "name",
            "extract_potential_ppg",
            "color_lovibond",
            "requires_mashing",
        ]


class HopSerializer(serializers.ModelSerializer):
    """Serializer for Hop model."""

    class Meta:
        model = Hop
        fields = [
            "id",
            "name",
            "country_of_origin",
            "notes",
            "alpha_acid",
            "beta_acid",
            "aroma",
            "bittering",
        ]


class YeastSerializer(serializers.ModelSerializer):
    """Serializer for Yeast model."""

    temperature_range_display = serializers.ReadOnlyField()

    class Meta:
        model = Yeast
        fields = [
            "id",
            "name",
            "laboratory",
            "product_id",
            "yeast_type",
            "yeast_form",
            "min_temperature_fahrenheit",
            "max_temperature_fahrenheit",
            "attenuation_percent",
            "temperature_range_display",
        ]


class WaterProfileSerializer(serializers.ModelSerializer):
    """Serializer for WaterProfile model."""

    class Meta:
        model = WaterProfile
        fields = [
            "id",
            "name",
            "description",
            "calcium_ppm",
            "magnesium_ppm",
            "sodium_ppm",
            "sulfate_ppm",
            "chloride_ppm",
            "bicarbonate_ppm",
        ]
