"""
Process serializers for the hoplogic core application.

This module contains serializers for brewing process models like MashStep and FermentationPhase.
"""

from rest_framework import serializers

from ..models import FermentationPhase, MashStep


class MashStepSerializer(serializers.ModelSerializer):
    """Serializer for MashStep model."""

    duration_display = serializers.ReadOnlyField()

    class Meta:
        model = MashStep
        fields = [
            "id",
            "name",
            "step_type",
            "temperature_fahrenheit",
            "duration_minutes",
            "step_order",
            "duration_display",
        ]


class FermentationPhaseSerializer(serializers.ModelSerializer):
    """Serializer for FermentationPhase model."""

    duration_display = serializers.ReadOnlyField()
    temperature_display = serializers.ReadOnlyField()

    class Meta:
        model = FermentationPhase
        fields = [
            "id",
            "name",
            "phase_type",
            "temperature_fahrenheit",
            "duration_days",
            "phase_order",
            "duration_display",
            "temperature_display",
        ]
