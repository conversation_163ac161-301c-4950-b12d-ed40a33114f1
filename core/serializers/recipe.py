"""
Recipe serializer for the hoplogic core application.

This module contains the main Recipe serializer that combines all other serializers.
"""

from rest_framework import serializers

from ..models import Recipe
from .beer_style import BeerStyleSerializer
from .inclusions import (
    BoilHopInclusionSerializer,
    DryHopInclusionSerializer,
    FermentableInclusionSerializer,
    FirstWortHopInclusionSerializer,
    FlameoutHopInclusionSerializer,
    WhirlpoolHopInclusionSerializer,
    YeastInclusionSerializer,
)
from .ingredients import WaterProfileSerializer
from .processes import FermentationPhaseSerializer, MashStepSerializer


class RecipeSerializer(serializers.ModelSerializer):
    """Serializer for Recipe model with calculated properties and related data."""

    # Calculated properties
    calculated_original_gravity = serializers.ReadOnlyField()
    original_gravity = serializers.ReadOnlyField()
    calculated_srm = serializers.ReadOnlyField()
    total_ibus = serializers.ReadOnlyField()
    estimated_final_gravity = serializers.ReadOnlyField()
    estimated_abv = serializers.ReadOnly<PERSON>ield()

    # Related data
    beer_style = BeerStyleSerializer(read_only=True)
    fermentable_inclusions = FermentableInclusionSerializer(
        source="fermentableinclusion_set", many=True, read_only=True
    )
    boil_hop_inclusions = BoilHopInclusionSerializer(
        source="boilhopinclusion_set", many=True, read_only=True
    )
    first_wort_hop_inclusions = FirstWortHopInclusionSerializer(
        source="firstworthopinclusion_set", many=True, read_only=True
    )
    flameout_hop_inclusions = FlameoutHopInclusionSerializer(
        source="flameouthopinclusion_set", many=True, read_only=True
    )
    whirlpool_hop_inclusions = WhirlpoolHopInclusionSerializer(
        source="whirlpoolhopinclusion_set", many=True, read_only=True
    )
    dry_hop_inclusions = DryHopInclusionSerializer(
        source="dryhopinclusion_set", many=True, read_only=True
    )
    water_profile = WaterProfileSerializer(read_only=True)
    yeast_inclusions = YeastInclusionSerializer(
        source="yeastinclusion_set", many=True, read_only=True
    )
    mash_steps = MashStepSerializer(source="mashstep_set", many=True, read_only=True)
    fermentation_phases = FermentationPhaseSerializer(
        source="fermentationphase_set", many=True, read_only=True
    )

    class Meta:
        model = Recipe
        fields = [
            "id",
            "name",
            "description",
            "batch_size_gallons",
            "mash_efficiency_percent",
            "target_original_gravity",
            "calculated_original_gravity",
            "original_gravity",
            "calculated_srm",
            "total_ibus",
            "estimated_final_gravity",
            "estimated_abv",
            "beer_style",
            "fermentable_inclusions",
            "boil_hop_inclusions",
            "first_wort_hop_inclusions",
            "flameout_hop_inclusions",
            "whirlpool_hop_inclusions",
            "dry_hop_inclusions",
            "water_profile",
            "yeast_inclusions",
            "mash_steps",
            "fermentation_phases",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]
