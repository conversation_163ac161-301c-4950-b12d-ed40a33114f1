from abc import ABC, abstractmethod
from enum import Enum
from typing import Generic, TypeVar, Callable

from django.contrib.auth.models import User

from ..models import BaseModel


M = TypeVar("M", bound=BaseModel)


class Action(str, Enum):
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"


class AuthorizationError(Exception):
    @classmethod
    def from_action(cls, action: Action) -> "AuthorizationError":
        return cls(f"Access denied: Cannot perform the action {action}")

    @classmethod
    def from_resource(cls, resource: M, action: Action) -> "AuthorizationError":
        return cls(
            f"Access denied: Cannot access the resource {resource.id} with action {action}"
        )


class AuthorizationPolicy(ABC, Generic[M]):
    @abstractmethod
    def authorize_action(self, user: User, action: Action) -> bool: ...

    @abstractmethod
    def auhorize_resource(self, user: User, resource: M, action: Action) -> bool: ...


class AuthorizationMixin:
    def authorize_delete_resource(
        self, user: User, getter: Callable[[], M], policy: AuthorizationPolicy[M]
    ) -> M | None:
        """Authorizes a user to delete a resource.

        This function first checks if the user is authorized to perform the action at all. If not,
        it raises an AuthorizationError. If so, it then checks if the user is authorized to access
        the resource. If not, it raises an AuthorizationError. If so, it returns the resource. If
        the resource does not exist, it returns None.

        Args:
            user (User): The user to check access for.
            getter (Callable[[], M]): A function that returns the resource to check access for.
            policy (AuthorizationPolicy[M]): The policy to check against.

        Returns:
            M | None: The resource if authorized, None if not found.

        Raises:
            AuthorizationError: If the user is not authorized to perform the action or access the resource.
        """
        if not policy.authorize_action(user, Action.DELETE):
            raise AuthorizationError.from_action(Action.DELETE)

        if (resource := getter()) is None:
            return None

        if not policy.auhorize_resource(user, resource, Action.DELETE):
            raise AuthorizationError.from_resource(resource, Action.DELETE)

        return resource

    def authorize_update_resource(
        self, user: User, getter: Callable[[], M], policy: AuthorizationPolicy[M]
    ) -> M | None:
        """Authorizes a user to update a resource.

        This function first checks if the user is authorized to perform the action at all. If not,
        it raises an AuthorizationError. If so, it then checks if the user is authorized to access
        the resource. If not, it raises an AuthorizationError. If so, it returns the resource. If
        the resource does not exist, it returns None.

        Args:
            user (User): The user to check access for.
            getter (Callable[[], M]): A function that returns the resource to check access for.
            policy (AuthorizationPolicy[M]): The policy to check against.

        Returns:
            M | None: The resource if authorized, None if not found.

        Raises:
            AuthorizationError: If the user is not authorized to perform the action or access the resource.
        """
        if not policy.authorize_action(user, Action.UPDATE):
            raise AuthorizationError.from_action(Action.UPDATE)

        if (resource := getter()) is None:
            return None

        if not policy.auhorize_resource(user, resource, Action.UPDATE):
            raise AuthorizationError.from_resource(resource, Action.UPDATE)

        return resource

    def authorize_read_resource(
        self, user: User, getter: Callable[[], M], policy: AuthorizationPolicy[M]
    ) -> M | None:
        """Authorizes a user to read a resource.

        This function first checks if the user is authorized to perform the action at all. If not,
        it raises an AuthorizationError. If so, it then checks if the user is authorized to access
        the resource. If not, it raises an AuthorizationError. If so, it returns the resource. If
        the resource does not exist, it returns None.

        Args:
            user (User): The user to check access for.
            getter (Callable[[], M]): A function that returns the resource to check access for.
            policy (AuthorizationPolicy[M]): The policy to check against.

        Returns:
            M | None: The resource if authorized, None if not found.

        Raises:
            AuthorizationError: If the user is not authorized to perform the action or access the resource.
        """
        if not policy.authorize_action(user, Action.READ):
            raise AuthorizationError.from_action(Action.READ)

        if (resource := getter()) is None:
            return None

        if not policy.auhorize_resource(user, resource, Action.READ):
            raise AuthorizationError.from_resource(resource, Action.READ)

        return resource
