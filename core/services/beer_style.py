from typing import Optional

from ..agents.tools.factory import agent_accessible
from ..di import injectable
from ..models import BeerStyle, Recipe
from ..repositories.beer_style import BeerStyleRepository
from ..repositories.recipe import RecipeRepository
from ..serializers.beer_style import BeerStyleSerializer


@injectable
class BeerStyleService:
    """Service class for BeerStyle business logic operations."""

    def __init__(
        self,
        repository: BeerStyleRepository,
        recipe_repository: RecipeRepository,
    ):
        self.repository = repository
        self.recipe_repository = recipe_repository

    @agent_accessible(serializer=BeerStyleSerializer)
    def get_all_styles(self) -> list[BeerStyle]:
        """Get all beer styles ordered by name."""
        return self.repository.get_all()

    @agent_accessible(serializer=BeerStyleSerializer)
    def get_style_by_id(self, style_id: str) -> Optional[BeerStyle]:
        """Get a beer style by its ID."""
        return self.repository.get_by_id(style_id)

    @agent_accessible(serializer=BeerStyleSerializer)
    def search_styles(self, query: str) -> list[BeerStyle]:
        """Search beer styles by name."""
        return self.repository.search_by_name(query)

    def get_default_style(self) -> Optional[BeerStyle]:
        """Get the default 'Custom Style' beer style."""
        return self.repository.get_default_style()

    @agent_accessible(serializer=BeerStyleSerializer)
    def find_matching_styles_for_recipe(self, recipe_id: str) -> list[BeerStyle]:
        """
        Find beer styles that match the given recipe's vital statistics.

        Args:
            recipe_id: ID of the recipe to find matching styles for

        Returns:
            List of BeerStyle objects that match the recipe's statistics
        """
        recipe = self.recipe_repository.get_by_id(recipe_id)
        if not recipe:
            return []

        return self.repository.get_styles_for_recipe_stats(
            srm=recipe.calculated_srm,
            ibu=recipe.total_ibus,
            og=recipe.original_gravity,
            fg=recipe.estimated_final_gravity,
            abv=recipe.estimated_abv,
        )
