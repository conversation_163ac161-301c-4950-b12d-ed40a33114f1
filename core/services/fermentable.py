"""
Fermentable service for the hoplogic application.

This module contains the service class for fermentable-related business logic.
"""

import logging

from ..agents.tools.factory import agent_accessible
from ..di import injectable
from ..models import Fermentable
from ..repositories import FermentableRepository
from ..serializers import FermentableSerializer

logger = logging.getLogger(__name__)


@injectable
class FermentableService:
    def __init__(self, repository: FermentableRepository):
        self.repository = repository

    @agent_accessible(serializer=FermentableSerializer)
    def get_fermentable_by_id(self, fermentable_id: str) -> Fermentable | None:
        """Get a fermentable by ID."""
        return self.repository.get_by_id(fermentable_id)

    @agent_accessible(serializer=FermentableSerializer)
    def get_all_fermentables(self) -> list[Fermentable]:
        """Get all fermentables."""
        return self.repository.get_all()
