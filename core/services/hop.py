"""
Hop service for the hoplogic application.

This module contains the service class for hop-related business logic.
"""

from django.db import transaction

from ..agents.tools.factory import agent_accessible
from ..di import injectable
from ..models import (
    BoilHopInclusion,
    DryHopInclusion,
    FirstWortHopInclusion,
    FlameoutHopInclusion,
    Hop,
    WhirlpoolHopInclusion,
)
from ..repositories import (
    BoilHopInclusionRepository,
    DryHopInclusionRepository,
    FirstWortHopInclusionRepository,
    FlameoutHopInclusionRepository,
    HopRepository,
    RecipeRepository,
    WhirlpoolHopInclusionRepository,
)
from ..serializers import (
    BoilHopInclusionSerializer,
    DryHopInclusionSerializer,
    FirstWortHopInclusionSerializer,
    FlameoutHopInclusionSerializer,
    HopSerializer,
    WhirlpoolHopInclusionSerializer,
)


@injectable
class HopService:
    """Service for managing hops and hop inclusions in recipes."""

    def __init__(
        self,
        recipe_repository: RecipeRepository,
        hop_repository: HopRepository,
        boil_hop_repository: BoilHopInclusionRepository,
        first_wort_repository: FirstWortHopInclusionRepository,
        flameout_repository: FlameoutHopInclusionRepository,
        whirlpool_repository: WhirlpoolHopInclusionRepository,
        dry_hop_repository: DryHopInclusionRepository,
    ):
        self.hop_repository = hop_repository
        self.recipe_repository = recipe_repository
        self.boil_hop_repository = boil_hop_repository
        self.first_wort_repository = first_wort_repository
        self.flameout_repository = flameout_repository
        self.whirlpool_repository = whirlpool_repository
        self.dry_hop_repository = dry_hop_repository

    @agent_accessible(serializer=HopSerializer)
    def get_hop_by_id(self, hop_id: str) -> Hop | None:
        """Get a hop by ID."""
        return self.hop_repository.get_by_id(hop_id)

    @agent_accessible(serializer=HopSerializer)
    def get_all_hops(self) -> list[Hop]:
        """Get all hops."""
        return self.hop_repository.get_all()

    @agent_accessible(serializer=HopSerializer)
    def search_hops(self, name_query: str) -> list[Hop]:
        """Search for hops by name. The input is a partial name to search for."""
        return self.hop_repository.search_by_name(name_query)

    @agent_accessible(serializer=HopSerializer)
    def get_aroma_hops(self) -> list[Hop]:
        """Get hops suitable for aroma additions."""
        return self.hop_repository.get_aroma_hops()

    @agent_accessible(serializer=HopSerializer)
    def get_bittering_hops(self) -> list[Hop]:
        """Get hops suitable for bittering additions."""
        return self.hop_repository.get_bittering_hops()

    @agent_accessible(serializer=HopSerializer)
    def get_hops_by_country(self, country: str) -> list[Hop]:
        """Get hops from a specific country. The input is the country name."""
        return self.hop_repository.get_by_country(country)

    @agent_accessible(serializer=HopSerializer)
    def get_high_alpha_hops(self, min_alpha: float = 10.0) -> list[Hop]:
        """Get high alpha acid hops (>= 10% alpha acid by default). Optionally specify minimum alpha acid percentage."""
        return self.hop_repository.get_high_alpha_hops(min_alpha)

    @agent_accessible(serializer=HopSerializer)
    def get_low_alpha_hops(self, max_alpha: float = 6.0) -> list[Hop]:
        """Get low alpha acid hops (<= 6% alpha acid by default). Optionally specify maximum alpha acid percentage."""
        return self.hop_repository.get_low_alpha_hops(max_alpha)

    @agent_accessible(serializer=BoilHopInclusionSerializer)
    @transaction.atomic
    def add_boil_hop_to_recipe(
        self,
        recipe_id: str,
        hop_id: str,
        quantity: float,
        quantity_unit: str,
        time_minutes: int,
        notes: str = "",
    ) -> BoilHopInclusion:
        """Add a boil hop to the recipe. Inputs are recipe ID, hop ID, quantity, quantity unit, and boil time in minutes."""
        recipe = self.recipe_repository.get_by_id_or_raise(recipe_id)
        hop = self.hop_repository.get_by_id_or_raise(hop_id)
        return self.boil_hop_repository.create(
            recipe=recipe,
            hop=hop,
            quantity=quantity,
            quantity_unit=quantity_unit,
            time_minutes=time_minutes,
            notes=notes,
        )

    @agent_accessible(serializer=FirstWortHopInclusionSerializer)
    @transaction.atomic
    def add_first_wort_hop_to_recipe(
        self,
        recipe_id: str,
        hop_id: str,
        quantity: float,
        quantity_unit: str,
        notes: str = "",
    ) -> FirstWortHopInclusion:
        """Add a first wort hop to the recipe. Inputs are recipe ID, hop ID, quantity, and quantity unit."""
        recipe = self.recipe_repository.get_by_id_or_raise(recipe_id)
        hop = self.hop_repository.get_by_id_or_raise(hop_id)
        return self.first_wort_repository.create(
            recipe=recipe,
            hop=hop,
            quantity=quantity,
            quantity_unit=quantity_unit,
            notes=notes,
        )

    @agent_accessible(serializer=FlameoutHopInclusionSerializer)
    @transaction.atomic
    def add_flameout_hop_to_recipe(
        self,
        recipe_id: str,
        hop_id: str,
        quantity: float,
        quantity_unit: str,
        notes: str = "",
    ) -> FlameoutHopInclusion:
        """Add a flameout hop to the recipe. Inputs are recipe ID, hop ID, quantity, and quantity unit."""
        recipe = self.recipe_repository.get_by_id_or_raise(recipe_id)
        hop = self.hop_repository.get_by_id_or_raise(hop_id)
        return self.flameout_repository.create(
            recipe=recipe,
            hop=hop,
            quantity=quantity,
            quantity_unit=quantity_unit,
            notes=notes,
        )

    @agent_accessible(serializer=WhirlpoolHopInclusionSerializer)
    @transaction.atomic
    def add_whirlpool_hop_to_recipe(
        self,
        recipe_id: str,
        hop_id: str,
        quantity: float,
        quantity_unit: str,
        time_minutes: int | None = None,
        temperature_f: int | None = None,
        notes: str = "",
    ) -> WhirlpoolHopInclusion:
        """Add a whirlpool hop to the recipe. Inputs are recipe ID, hop ID, quantity, quantity unit, and optionally time and temperature."""
        recipe = self.recipe_repository.get_by_id_or_raise(recipe_id)
        hop = self.hop_repository.get_by_id_or_raise(hop_id)
        return self.whirlpool_repository.create(
            recipe=recipe,
            hop=hop,
            quantity=quantity,
            quantity_unit=quantity_unit,
            time_minutes=time_minutes,
            temperature_f=temperature_f,
            notes=notes,
        )

    @agent_accessible(serializer=DryHopInclusionSerializer)
    @transaction.atomic
    def add_dry_hop_to_recipe(
        self,
        recipe_id: str,
        hop_id: str,
        quantity: float,
        quantity_unit: str,
        time_days: int | None = None,
        notes: str = "",
    ) -> DryHopInclusion:
        """Add a dry hop to the recipe. Inputs are recipe ID, hop ID, quantity, quantity unit, and optionally time in days."""
        recipe = self.recipe_repository.get_by_id_or_raise(recipe_id)
        hop = self.hop_repository.get_by_id_or_raise(hop_id)
        return self.dry_hop_repository.create(
            recipe=recipe,
            hop=hop,
            quantity=quantity,
            quantity_unit=quantity_unit,
            time_days=time_days,
            notes=notes,
        )

    @agent_accessible(serializer=BoilHopInclusionSerializer)
    @transaction.atomic
    def update_boil_hop_inclusion(
        self,
        inclusion_id: str,
        hop_id: str | None = None,
        quantity: float | None = None,
        quantity_unit: str | None = None,
        time_minutes: int | None = None,
        notes: str | None = None,
    ) -> BoilHopInclusion:
        """Update a boil hop inclusion. Inputs are inclusion ID and any fields to update: hop_id, quantity, quantity_unit, time_minutes, notes."""
        inclusion = self.boil_hop_repository.get_by_id_or_raise(inclusion_id)
        update_data = {}

        if hop_id is not None:
            hop = self.hop_repository.get_by_id_or_raise(hop_id)
            update_data["hop"] = hop
        if quantity is not None:
            update_data["quantity"] = quantity
        if quantity_unit is not None:
            update_data["quantity_unit"] = quantity_unit
        if time_minutes is not None:
            update_data["time_minutes"] = time_minutes
        if notes is not None:
            update_data["notes"] = notes

        return self.boil_hop_repository.update(inclusion, **update_data)

    @agent_accessible(serializer=FirstWortHopInclusionSerializer)
    @transaction.atomic
    def update_first_wort_hop_inclusion(
        self,
        inclusion_id: str,
        hop_id: str | None = None,
        quantity: float | None = None,
        quantity_unit: str | None = None,
        notes: str | None = None,
    ) -> FirstWortHopInclusion:
        """Update a first wort hop inclusion. Inputs are inclusion ID and any fields to update: hop_id, quantity, quantity_unit, notes."""
        inclusion = self.first_wort_repository.get_by_id_or_raise(inclusion_id)
        update_data = {}

        if hop_id is not None:
            hop = self.hop_repository.get_by_id_or_raise(hop_id)
            update_data["hop"] = hop
        if quantity is not None:
            update_data["quantity"] = quantity
        if quantity_unit is not None:
            update_data["quantity_unit"] = quantity_unit
        if notes is not None:
            update_data["notes"] = notes

        return self.first_wort_repository.update(inclusion, **update_data)

    @agent_accessible(serializer=FlameoutHopInclusionSerializer)
    @transaction.atomic
    def update_flameout_hop_inclusion(
        self,
        inclusion_id: str,
        hop_id: str | None = None,
        quantity: float | None = None,
        quantity_unit: str | None = None,
        notes: str | None = None,
    ) -> FlameoutHopInclusion:
        """Update a flameout hop inclusion. Inputs are inclusion ID and any fields to update: hop_id, quantity, quantity_unit, notes."""
        inclusion = self.flameout_repository.get_by_id_or_raise(inclusion_id)
        update_data = {}

        if hop_id is not None:
            hop = self.hop_repository.get_by_id_or_raise(hop_id)
            update_data["hop"] = hop
        if quantity is not None:
            update_data["quantity"] = quantity
        if quantity_unit is not None:
            update_data["quantity_unit"] = quantity_unit
        if notes is not None:
            update_data["notes"] = notes

        return self.flameout_repository.update(inclusion, **update_data)

    @agent_accessible(serializer=WhirlpoolHopInclusionSerializer)
    @transaction.atomic
    def update_whirlpool_hop_inclusion(
        self,
        inclusion_id: str,
        hop_id: str | None = None,
        quantity: float | None = None,
        quantity_unit: str | None = None,
        time_minutes: int | None = None,
        temperature_f: int | None = None,
        notes: str | None = None,
    ) -> WhirlpoolHopInclusion:
        """Update a whirlpool hop inclusion. Inputs are inclusion ID and any fields to update: hop_id, quantity, quantity_unit, time_minutes, temperature_f, notes."""
        inclusion = self.whirlpool_repository.get_by_id_or_raise(inclusion_id)
        update_data = {}

        if hop_id is not None:
            hop = self.hop_repository.get_by_id_or_raise(hop_id)
            update_data["hop"] = hop
        if quantity is not None:
            update_data["quantity"] = quantity
        if quantity_unit is not None:
            update_data["quantity_unit"] = quantity_unit
        if time_minutes is not None:
            update_data["time_minutes"] = time_minutes
        if temperature_f is not None:
            update_data["temperature_f"] = temperature_f
        if notes is not None:
            update_data["notes"] = notes

        return self.whirlpool_repository.update(inclusion, **update_data)

    @agent_accessible(serializer=DryHopInclusionSerializer)
    @transaction.atomic
    def update_dry_hop_inclusion(
        self,
        inclusion_id: str,
        hop_id: str | None = None,
        quantity: float | None = None,
        quantity_unit: str | None = None,
        time_days: int | None = None,
        notes: str | None = None,
    ) -> DryHopInclusion:
        """Update a dry hop inclusion. Inputs are inclusion ID and any fields to update: hop_id, quantity, quantity_unit, time_days, notes."""
        inclusion = self.dry_hop_repository.get_by_id_or_raise(inclusion_id)
        update_data = {}

        if hop_id is not None:
            hop = self.hop_repository.get_by_id_or_raise(hop_id)
            update_data["hop"] = hop
        if quantity is not None:
            update_data["quantity"] = quantity
        if quantity_unit is not None:
            update_data["quantity_unit"] = quantity_unit
        if time_days is not None:
            update_data["time_days"] = time_days
        if notes is not None:
            update_data["notes"] = notes

        return self.dry_hop_repository.update(inclusion, **update_data)

    # Delete methods for hop inclusions

    @agent_accessible()
    @transaction.atomic
    def delete_boil_hop_inclusion(self, inclusion_id: str) -> bool:
        """Delete a boil hop inclusion by ID. This will permanently remove the hop from the recipe."""
        inclusion = self.boil_hop_repository.get_by_id_or_raise(inclusion_id)
        self.boil_hop_repository.delete(inclusion)
        return True

    @agent_accessible()
    @transaction.atomic
    def delete_first_wort_hop_inclusion(self, inclusion_id: str) -> bool:
        """Delete a first wort hop inclusion by ID. This will permanently remove the hop from the recipe."""
        inclusion = self.first_wort_repository.get_by_id_or_raise(inclusion_id)
        self.first_wort_repository.delete(inclusion)
        return True

    @agent_accessible()
    @transaction.atomic
    def delete_flameout_hop_inclusion(self, inclusion_id: str) -> bool:
        """Delete a flameout hop inclusion by ID. This will permanently remove the hop from the recipe."""
        inclusion = self.flameout_repository.get_by_id_or_raise(inclusion_id)
        self.flameout_repository.delete(inclusion)
        return True

    @agent_accessible()
    @transaction.atomic
    def delete_whirlpool_hop_inclusion(self, inclusion_id: str) -> bool:
        """Delete a whirlpool hop inclusion by ID. This will permanently remove the hop from the recipe."""
        inclusion = self.whirlpool_repository.get_by_id_or_raise(inclusion_id)
        self.whirlpool_repository.delete(inclusion)
        return True

    @agent_accessible()
    @transaction.atomic
    def delete_dry_hop_inclusion(self, inclusion_id: str) -> bool:
        """Delete a dry hop inclusion by ID. This will permanently remove the hop from the recipe."""
        inclusion = self.dry_hop_repository.get_by_id_or_raise(inclusion_id)
        self.dry_hop_repository.delete(inclusion)
        return True

    # Get methods for individual hop inclusions

    @agent_accessible(serializer=BoilHopInclusionSerializer)
    def get_boil_hop_inclusion_by_id(
        self, inclusion_id: str
    ) -> BoilHopInclusion | None:
        """Get a boil hop inclusion by ID."""
        return self.boil_hop_repository.get_by_id(inclusion_id)

    @agent_accessible(serializer=FirstWortHopInclusionSerializer)
    def get_first_wort_hop_inclusion_by_id(
        self, inclusion_id: str
    ) -> FirstWortHopInclusion | None:
        """Get a first wort hop inclusion by ID."""
        return self.first_wort_repository.get_by_id(inclusion_id)

    @agent_accessible(serializer=FlameoutHopInclusionSerializer)
    def get_flameout_hop_inclusion_by_id(
        self, inclusion_id: str
    ) -> FlameoutHopInclusion | None:
        """Get a flameout hop inclusion by ID."""
        return self.flameout_repository.get_by_id(inclusion_id)

    @agent_accessible(serializer=WhirlpoolHopInclusionSerializer)
    def get_whirlpool_hop_inclusion_by_id(
        self, inclusion_id: str
    ) -> WhirlpoolHopInclusion | None:
        """Get a whirlpool hop inclusion by ID."""
        return self.whirlpool_repository.get_by_id(inclusion_id)

    @agent_accessible(serializer=DryHopInclusionSerializer)
    def get_dry_hop_inclusion_by_id(self, inclusion_id: str) -> DryHopInclusion | None:
        """Get a dry hop inclusion by ID."""
        return self.dry_hop_repository.get_by_id(inclusion_id)
