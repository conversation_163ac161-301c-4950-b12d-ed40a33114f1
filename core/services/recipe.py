from django.contrib.auth.models import User

from .auth import AuthorizationMixin, AuthorizationPolicy, Action, AuthorizationError
from ..agents.tools.factory import agent_accessible
from ..di import injectable
from ..models import (
    BoilHopInclusion,
    DryHopInclusion,
    FermentableInclusion,
    FirstWortHopInclusion,
    FlameoutHopInclusion,
    Recipe,
    WhirlpoolHopInclusion,
    YeastInclusion,
)
from ..repositories import (
    BeerStyleRepository,
    BoilHopInclusionRepository,
    DryHopInclusionRepository,
    FermentableInclusionRepository,
    FermentableRepository,
    FirstWortHopInclusionRepository,
    FlameoutHopInclusionRepository,
    HopRepository,
    RecipeRepository,
    WhirlpoolHopInclusionRepository,
    YeastInclusionRepository,
    YeastRepository,
)
from ..serializers import (
    BoilHopInclusionSerializer,
    DryHopInclusionSerializer,
    FermentableInclusionSerializer,
    FirstWortHopInclusionSerializer,
    FlameoutHopInclusionSerializer,
    RecipeSerializer,
    WhirlpoolHopInclusionSerializer,
    YeastInclusionSerializer,
)


class RecipeAuthorizationPolicy(AuthorizationPolicy[Recipe]):
    def authorize_action(self, user: User, action: Action) -> bool:
        return True

    def auhorize_resource(self, user: User, resource: Recipe, _: Action) -> bool:
        return user == resource.user


@injectable
class RecipeService(AuthorizationMixin):
    """Manage recipes, the techniques, and the ingredients.

    This service provides methods for creating, updating, and deleting recipes as
    well as adding and removing ingredients and techniques. Adding ingredients like
    fermentables, hops, and yeast are all managed through this service. Techniques
    like mash steps and fermentation phases are also managed through this service.

    As a result, this service relies on many repositories to perform its operations
    and therefore is relatively large. Fortunately, most of the methods are not
    particularly complicated because the repositories handle the database operations
    and other more lower level details.
    """

    def __init__(
        self,
        recipe_repository: RecipeRepository,
        fermentable_repository: FermentableRepository,
        fermentable_inclusion_repository: FermentableInclusionRepository,
        boil_hop_repository: BoilHopInclusionRepository,
        first_wort_repository: FirstWortHopInclusionRepository,
        flameout_repository: FlameoutHopInclusionRepository,
        whirlpool_repository: WhirlpoolHopInclusionRepository,
        dry_hop_repository: DryHopInclusionRepository,
        yeast_inclusion_repository: YeastInclusionRepository,
        beer_style_repository: BeerStyleRepository,
        hop_repository: HopRepository,
        yeast_repository: YeastRepository,
    ):
        self.recipe_repository = recipe_repository
        self.fermentable_repository = fermentable_repository
        self.fermentable_inclusion_repository = fermentable_inclusion_repository
        self.boil_hop_repository = boil_hop_repository
        self.first_wort_repository = first_wort_repository
        self.flameout_repository = flameout_repository
        self.whirlpool_repository = whirlpool_repository
        self.dry_hop_repository = dry_hop_repository
        self.yeast_inclusion_repository = yeast_inclusion_repository
        self.beer_style_repository = beer_style_repository
        self.hop_repository = hop_repository
        self.yeast_repository = yeast_repository

        self.recipe_authorization_policy = RecipeAuthorizationPolicy()

    def _authorize_recipe_read(self, user: User, recipe_id: str) -> Recipe | None:
        """Helper to authorize recipe read access."""
        return self.authorize_read_resource(
            user=user,
            getter=lambda: self.recipe_repository.get_by_id(recipe_id),
            policy=self.recipe_authorization_policy,
        )

    def _authorize_recipe_update(self, user: User, recipe_id: str) -> Recipe | None:
        """Helper to authorize recipe update access."""
        return self.authorize_update_resource(
            user=user,
            getter=lambda: self.recipe_repository.get_by_id(recipe_id),
            policy=self.recipe_authorization_policy,
        )

    def _authorize_inclusion_delete(self, user: User, inclusion_id: str, repository) -> bool:
        """Helper to authorize inclusion deletion by checking recipe ownership."""
        # Get the inclusion first
        inclusion = repository.get_by_id(inclusion_id)
        if not inclusion:
            return False

        # Check if user owns the recipe that contains this inclusion
        recipe = self.authorize_update_resource(
            user=user,
            getter=lambda: inclusion.recipe,  # inclusion.recipe is already loaded
            policy=self.recipe_authorization_policy,
        )

        if recipe is None:
            return False

        # If authorized, delete the inclusion
        return repository.delete(inclusion_id)

    @agent_accessible(serializer=RecipeSerializer)
    def get_recipe_by_id(self, recipe_id: str, user: User) -> Recipe | None:
        """Get a recipe by ID.

        Args:
            recipe_id (str): The ID of the recipe to get.
            user (User): The user to check access for.

        Returns:
            Recipe | None: The recipe if found, None otherwise.

        Raises:
            AuthorizationError: If the user does not have access to the recipe.
        """
        return self.authorize_read_resource(
            user=user,
            getter=lambda: self.recipe_repository.get_by_id(recipe_id),
            policy=self.recipe_authorization_policy,
        )

    @agent_accessible
    def delete_recipe(self, recipe_id: str, user: User):
        """Delete a recipe by ID.

        Args:
            recipe_id (str): The ID of the recipe to delete.
            user (User): The user to check access for.
        """
        recipe = self.authorize_delete_resource(
            user=user,
            getter=lambda: self.recipe_repository.get_by_id(recipe_id),
            policy=self.recipe_authorization_policy,
        )
        if recipe is not None:
            self.recipe_repository.delete(recipe)

    @agent_accessible(serializer=FermentableInclusionSerializer)
    def get_included_fermentables_by_id(
        self, recipe_id: str, user: User
    ) -> list[FermentableInclusion]:
        """Get the fermentables included in the recipe and their quantities.

        Args:
            recipe_id (str): The ID of the recipe to get the fermentables for.
            user (User): The user to check access for.

        Returns:
            list[FermentableInclusion]: The fermentables included in the recipe.
        """
        # Authorize recipe access first
        recipe = self._authorize_recipe_read(user, recipe_id)
        if recipe is None:
            return []

        return self.fermentable_inclusion_repository.get_by_recipe(recipe_id)

    @agent_accessible(serializer=YeastInclusionSerializer)
    def get_yeast_inclusions(self, recipe_id: str, user: User) -> list[YeastInclusion]:
        """Get the yeast inclusions for the recipe.

        This will include the yeast data for each inclusion. Use this
        to get the yeast and pitch amount for each yeast.

        Args:
            recipe_id (str): The ID of the recipe to get the yeast inclusions for.
            user (User): The user to check access for.

        Returns:
            list[YeastInclusion]: The yeast inclusions for the recipe.
        """
        # Authorize recipe access first
        recipe = self._authorize_recipe_read(user, recipe_id)
        if recipe is None:
            return []

        return self.yeast_inclusion_repository.get_by_recipe(recipe_id)

    @agent_accessible(serializer=DryHopInclusionSerializer)
    def get_dry_hop_inclusions(self, recipe_id: str, user: User) -> list[DryHopInclusion]:
        """Get the dry hop inclusions for the recipe.

        This will include the hop data for each inclusion. Use this
        to get the hop and addition details for each dry hop.

        Args:
            recipe_id (str): The ID of the recipe to get the dry hop inclusions for.
            user (User): The user to check access for.

        Returns:
            list[DryHopInclusion]: The dry hop inclusions for the recipe.
        """
        # Authorize recipe access first
        recipe = self._authorize_recipe_read(user, recipe_id)
        if recipe is None:
            return []

        return self.dry_hop_repository.get_by_recipe(recipe_id)

    @agent_accessible(serializer=FirstWortHopInclusionSerializer)
    def get_first_wort_hop_inclusions(
        self, recipe_id: str, user: User
    ) -> list[FirstWortHopInclusion]:
        """Get the first wort hop inclusions for the recipe.

        This will include the hop data for each inclusion. Use this
        to get the hop and addition details for each first wort hop.

        Args:
            recipe_id (str): The ID of the recipe to get the first wort hop inclusions for.
            user (User): The user to check access for.

        Returns:
            list[FirstWortHopInclusion]: The first wort hop inclusions for the recipe.
        """
        # Authorize recipe access first
        recipe = self._authorize_recipe_read(user, recipe_id)
        if recipe is None:
            return []

        return self.first_wort_repository.get_by_recipe(recipe_id)

    @agent_accessible(serializer=FlameoutHopInclusionSerializer)
    def get_flameout_hop_inclusions(self, recipe_id: str, user: User) -> list[FlameoutHopInclusion]:
        """Get the flameout hop inclusions for the recipe.

        This will include the hop data for each inclusion. Use this
        to get the hop and addition details for each flameout hop.

        Args:
            recipe_id (str): The ID of the recipe to get the flameout hop inclusions for.
            user (User): The user to check access for.

        Returns:
            list[FlameoutHopInclusion]: The flameout hop inclusions for the recipe.
        """
        # Authorize recipe access first
        recipe = self._authorize_recipe_read(user, recipe_id)
        if recipe is None:
            return []

        return self.flameout_repository.get_by_recipe(recipe_id)

    @agent_accessible(serializer=WhirlpoolHopInclusionSerializer)
    def get_whirlpool_hop_inclusions(
        self, recipe_id: str, user: User
    ) -> list[WhirlpoolHopInclusion]:
        """Get the whirlpool hop inclusions for the recipe.

        This will include the hop data for each inclusion. Use this
        to get the hop and addition details for each whirlpool hop.

        Args:
            recipe_id (str): The ID of the recipe to get the whirlpool hop inclusions for.
            user (User): The user to check access for.

        Returns:
            list[WhirlpoolHopInclusion]: The whirlpool hop inclusions for the recipe.
        """
        # Authorize recipe access first
        recipe = self._authorize_recipe_read(user, recipe_id)
        if recipe is None:
            return []

        return self.whirlpool_repository.get_by_recipe(recipe_id)

    @agent_accessible(serializer=BoilHopInclusionSerializer)
    def get_boil_hop_inclusions(self, recipe_id: str, user: User) -> list[BoilHopInclusion]:
        """Get the boil hop inclusions for the recipe.

        This will include the hop data for each inclusion. Use this
        to get the hop and addition details for each boil hop.

        Args:
            recipe_id (str): The ID of the recipe to get the boil hop inclusions for.
            user (User): The user to check access for.

        Returns:
            list[BoilHopInclusion]: The boil hop inclusions for the recipe.
        """
        # Authorize recipe access first
        recipe = self._authorize_recipe_read(user, recipe_id)
        if recipe is None:
            return []

        return self.boil_hop_repository.get_by_recipe(recipe_id)

    @agent_accessible(serializer=RecipeSerializer)
    def get_recipes_by_user(self, user_id: str, user: User) -> list[Recipe]:
        """Get all recipes for a user.

        Args:
            user_id (str): The ID of the user to get the recipes for.
            user (User): The user to check access for.

        Returns:
            list[Recipe]: The recipes for the user.
        """
        # Only allow users to get their own recipes (unless superuser)
        if user.id != user_id and not user.is_superuser:
            raise AuthorizationError.from_action(Action.READ)
        return self.recipe_repository.get_by_user(user_id)

    @agent_accessible(serializer=FermentableInclusionSerializer)
    def add_fermentable_to_recipe(
        self,
        recipe_id: str,
        fermentable_id: str,
        quantity: float,
        quantity_unit: str,
        user: User,
        efficiency_percent: float | None = None,
        notes: str = "",
    ) -> FermentableInclusion:
        """Add a fermentable to the recipe.

        Args:
            recipe_id (str): The ID of the recipe to add the fermentable to.
            fermentable_id (str): The ID of the fermentable to add to the recipe.
            quantity (float): The quantity of the fermentable to add.
            quantity_unit (str): The unit of measurement for the quantity.
            user (User): The user to check access for.
            efficiency_percent (float, optional): The mash efficiency for this fermentable. Defaults to None.
            notes (str, optional): Any notes for the fermentable inclusion. Defaults to "".

        Returns:
            FermentableInclusion: The newly created fermentable inclusion.
        """
        # Authorize recipe update first
        recipe = self._authorize_recipe_update(user, recipe_id)
        if recipe is None:
            raise ValueError(f"Recipe with ID {recipe_id} not found or access denied.")

        if (
            fermentable := self.fermentable_repository.get_by_id(fermentable_id)
        ) is None:
            raise ValueError(f"Fermentable with ID {fermentable_id} not found.")

        return self.fermentable_inclusion_repository.create(
            recipe=recipe,
            fermentable=fermentable,
            quantity=quantity,
            quantity_unit=quantity_unit,
            efficiency_percent=efficiency_percent,
            notes=notes,
        )

    @agent_accessible(serializer=BoilHopInclusionSerializer)
    def add_boil_hop_to_recipe(
        self,
        recipe_id: str,
        hop_id: str,
        quantity: float,
        quantity_unit: str,
        time_minutes: int,
        user: User,
        notes: str = "",
    ) -> BoilHopInclusion:
        """Add a boil hop to the recipe.

        Args:
            recipe_id (str): The ID of the recipe to add the hop to.
            hop_id (str): The ID of the hop to add to the recipe.
            quantity (float): The quantity of the hop to add.
            quantity_unit (str): The unit of measurement for the quantity.
            time_minutes (int): The time in minutes to boil the hop.
            user (User): The user to check access for.
            notes (str, optional): Any notes for the hop inclusion. Defaults to "".

        Returns:
            BoilHopInclusion: The newly created boil hop inclusion.
        """
        if (hop := self.hop_repository.get_by_id(hop_id)) is None:
            raise ValueError(f"Hop with ID {hop_id} not found.")

        # Authorize recipe update first
        recipe = self._authorize_recipe_update(user, recipe_id)
        if recipe is None:
            raise ValueError(f"Recipe with ID {recipe_id} not found or access denied.")

        return self.boil_hop_repository.create(
            hop=hop,
            recipe=recipe,
            quantity=quantity,
            quantity_unit=quantity_unit,
            time_minutes=time_minutes,
            notes=notes,
        )

    @agent_accessible(serializer=FirstWortHopInclusionSerializer)
    def add_first_wort_hop_to_recipe(
        self,
        recipe_id: str,
        hop_id: str,
        quantity: float,
        quantity_unit: str,
        user: User,
        notes: str = "",
    ) -> FirstWortHopInclusion:
        """Add a first wort hop to the recipe.

        Args:
            recipe_id (str): The ID of the recipe to add the hop to.
            hop_id (str): The ID of the hop to add to the recipe.
            quantity (float): The quantity of the hop to add.
            quantity_unit (str): The unit of measurement for the quantity.
            user (User): The user to check access for.
            notes (str, optional): Any notes for the hop inclusion. Defaults to "".

        Returns:
            FirstWortHopInclusion: The newly created first wort hop inclusion.
        """
        if (hop := self.hop_repository.get_by_id(hop_id)) is None:
            raise ValueError(f"Hop with ID {hop_id} not found.")

        # Authorize recipe update first
        recipe = self._authorize_recipe_update(user, recipe_id)
        if recipe is None:
            raise ValueError(f"Recipe with ID {recipe_id} not found or access denied.")

        return self.first_wort_repository.create(
            hop=hop,
            recipe=recipe,
            quantity=quantity,
            quantity_unit=quantity_unit,
            notes=notes,
        )

    @agent_accessible(serializer=FlameoutHopInclusionSerializer)
    def add_flameout_hop_to_recipe(
        self,
        recipe_id: str,
        hop_id: str,
        quantity: float,
        quantity_unit: str,
        user: User,
        notes: str = "",
    ) -> FlameoutHopInclusion:
        """Add a flameout hop to the recipe.

        Args:
            recipe_id (str): The ID of the recipe to add the hop to.
            hop_id (str): The ID of the hop to add to the recipe.
            quantity (float): The quantity of the hop to add.
            quantity_unit (str): The unit of measurement for the quantity.
            user (User): The user to check access for.
            notes (str, optional): Any notes for the hop inclusion. Defaults to "".

        Returns:
            FlameoutHopInclusion: The newly created flameout hop inclusion.
        """
        if (hop := self.hop_repository.get_by_id(hop_id)) is None:
            raise ValueError(f"Hop with ID {hop_id} not found.")

        # Authorize recipe update first
        recipe = self._authorize_recipe_update(user, recipe_id)
        if recipe is None:
            raise ValueError(f"Recipe with ID {recipe_id} not found or access denied.")

        return self.flameout_repository.create(
            hop=hop,
            recipe=recipe,
            quantity=quantity,
            quantity_unit=quantity_unit,
            notes=notes,
        )

    @agent_accessible(serializer=WhirlpoolHopInclusionSerializer)
    def add_whirlpool_hop_to_recipe(
        self,
        recipe_id: str,
        hop_id: str,
        quantity: float,
        quantity_unit: str,
        user: User,
        time_minutes: int | None = None,
        temperature_f: int | None = None,
        notes: str = "",
    ) -> WhirlpoolHopInclusion:
        """Add a whirlpool hop to the recipe.

        Args:
            recipe_id (str): The ID of the recipe to add the hop to.
            hop_id (str): The ID of the hop to add to the recipe.
            quantity (float): The quantity of the hop to add.
            quantity_unit (str): The unit of measurement for the quantity.
            user (User): The user to check access for.
            time_minutes (int, optional): The time in minutes to whirlpool the hop. Defaults to None.
            temperature_f (int, optional): The temperature in Fahrenheit to whirlpool the hop. Defaults to None.
            notes (str, optional): Any notes for the hop inclusion. Defaults to "".

        Returns:
            WhirlpoolHopInclusion: The newly created whirlpool hop inclusion.
        """
        if (hop := self.hop_repository.get_by_id(hop_id)) is None:
            raise ValueError(f"Hop with ID {hop_id} not found.")

        # Authorize recipe update first
        recipe = self._authorize_recipe_update(user, recipe_id)
        if recipe is None:
            raise ValueError(f"Recipe with ID {recipe_id} not found or access denied.")

        return self.whirlpool_repository.create(
            hop=hop,
            recipe=recipe,
            quantity=quantity,
            quantity_unit=quantity_unit,
            time_minutes=time_minutes,
            temperature_f=temperature_f,
            notes=notes,
        )

    @agent_accessible(serializer=DryHopInclusionSerializer)
    def add_dry_hop_to_recipe(
        self,
        recipe_id: str,
        hop_id: str,
        quantity: float,
        quantity_unit: str,
        user: User,
        time_days: int | None = None,
        notes: str = "",
    ) -> DryHopInclusion:
        """Add a dry hop to the recipe.

        Args:
            recipe_id (str): The ID of the recipe to add the hop to.
            hop_id (str): The ID of the hop to add to the recipe.
            quantity (float): The quantity of the hop to add.
            quantity_unit (str): The unit of measurement for the quantity.
            user (User): The user to check access for.
            time_days (int, optional): The time in days to dry hop. Defaults to None.
            notes (str, optional): Any notes for the hop inclusion. Defaults to "".

        Returns:
            DryHopInclusion: The newly created dry hop inclusion.
        """
        if (hop := self.hop_repository.get_by_id(hop_id)) is None:
            raise ValueError(f"Hop with ID {hop_id} not found.")

        # Authorize recipe update first
        recipe = self._authorize_recipe_update(user, recipe_id)
        if recipe is None:
            raise ValueError(f"Recipe with ID {recipe_id} not found or access denied.")

        return self.dry_hop_repository.create(
            hop=hop,
            recipe=recipe,
            quantity=quantity,
            quantity_unit=quantity_unit,
            time_days=time_days,
            notes=notes,
        )

    @agent_accessible(serializer=YeastInclusionSerializer)
    def add_yeast_to_recipe(
        self,
        user: User,
        recipe_id: str,
        yeast_id: str,
        quantity: float,
        quantity_unit: str,
        starter_made: bool = False,
        starter_size_ml: float | None = None,
        notes: str = "",
    ) -> YeastInclusion:
        """Add yeast to the recipe.

        Args:
            user (User): The user to check access for.
            recipe_id (str): The ID of the recipe to add the yeast to.
            yeast_id (str): The ID of the yeast to add to the recipe.
            quantity (float): The quantity of the yeast to add.
            quantity_unit (str): The unit of measurement for the quantity.
            starter_made (bool, optional): Whether a yeast starter was made. Defaults to False.
            starter_size_ml (float, optional): The size of the yeast starter in mL. Defaults to None.
            notes (str, optional): Any notes for the yeast inclusion. Defaults to "".

        Returns:
            YeastInclusion: The newly created yeast inclusion.
        """
        if (yeast := self.yeast_repository.get_by_id(yeast_id)) is None:
            raise ValueError(f"Yeast with ID {yeast_id} not found.")

        if (
            recipe := self.authorize_update_resource(
                user=user,
                getter=lambda: self.recipe_repository.get_by_id(recipe_id),
                policy=self.recipe_authorization_policy,
            )
        ) is None:
            raise ValueError(f"Recipe with ID {recipe_id} not found.")

        return self.yeast_inclusion_repository.create(
            yeast=yeast,
            recipe=recipe,
            quantity=quantity,
            quantity_unit=quantity_unit,
            starter_made=starter_made,
            starter_size_ml=starter_size_ml,
            notes=notes,
        )

    @agent_accessible
    def delete_fermentable_inclusion(self, inclusion_id: str, user: User) -> bool:
        """Delete an included fermentable from the inclusion id.

        Args:
            inclusion_id (str): The ID of the fermentable inclusion to delete.
            user (User): The user to check access for.

        Returns:
            bool: True if the inclusion was deleted, False otherwise.
        """
        return self._authorize_inclusion_delete(user, inclusion_id, self.fermentable_inclusion_repository)

    @agent_accessible
    def delete_boil_hop_inclusion(self, inclusion_id: str, user: User) -> bool:
        """Delete a boil hop inclusion from the inclusion id.

        Args:
            inclusion_id (str): The ID of the boil hop inclusion to delete.
            user (User): The user to check access for.

        Returns:
            bool: True if the inclusion was deleted, False otherwise.
        """
        return self._authorize_inclusion_delete(user, inclusion_id, self.boil_hop_repository)

    @agent_accessible
    def delete_first_wort_hop_inclusion(self, inclusion_id: str, user: User) -> bool:
        """Delete a first wort hop inclusion from the inclusion id.

        Args:
            inclusion_id (str): The ID of the first wort hop inclusion to delete.
            user (User): The user to check access for.

        Returns:
            bool: True if the inclusion was deleted, False otherwise.
        """
        return self._authorize_inclusion_delete(user, inclusion_id, self.first_wort_repository)

    @agent_accessible
    def delete_flameout_hop_inclusion(self, inclusion_id: str, user: User) -> bool:
        """Delete a flameout hop inclusion from the inclusion id.

        Args:
            inclusion_id (str): The ID of the flameout hop inclusion to delete.
            user (User): The user to check access for.

        Returns:
            bool: True if the inclusion was deleted, False otherwise.
        """
        return self._authorize_inclusion_delete(user, inclusion_id, self.flameout_repository)

    @agent_accessible
    def delete_whirlpool_hop_inclusion(self, inclusion_id: str, user: User) -> bool:
        """Delete a whirlpool hop inclusion from the inclusion id.

        Args:
            inclusion_id (str): The ID of the whirlpool hop inclusion to delete.
            user (User): The user to check access for.

        Returns:
            bool: True if the inclusion was deleted, False otherwise.
        """
        return self._authorize_inclusion_delete(user, inclusion_id, self.whirlpool_repository)

    @agent_accessible
    def delete_dry_hop_inclusion(self, inclusion_id: str, user: User) -> bool:
        """Delete a dry hop inclusion from the inclusion id.

        Args:
            inclusion_id (str): The ID of the dry hop inclusion to delete.
            user (User): The user to check access for.

        Returns:
            bool: True if the inclusion was deleted, False otherwise.
        """
        return self._authorize_inclusion_delete(user, inclusion_id, self.dry_hop_repository)

    @agent_accessible
    def delete_yeast_inclusion(self, inclusion_id: str, user: User) -> bool:
        """Delete a yeast inclusion from the inclusion id.

        Args:
            inclusion_id (str): The ID of the yeast inclusion to delete.
            user (User): The user to check access for.

        Returns:
            bool: True if the inclusion was deleted, False otherwise.
        """
        return self._authorize_inclusion_delete(user, inclusion_id, self.yeast_inclusion_repository)
