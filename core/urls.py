"""
URL configuration for the hoplogic core application.

This module contains all URL patterns for the application, including
authentication and recipe-related views.
"""

from django.urls import path

from .views import (
    ChatStreamAPIView,
    CreateRecipeView,
    CustomLoginView,
    CustomLogoutView,
    HomeView,
    RecipeCollaborationView,
    RecipeDetailAPIView,
    RecipeRefreshAPIView,
    RegisterView,
)

# Authentication URL patterns
auth_urlpatterns = [
    path("auth/login/", CustomLoginView.as_view(), name="login"),
    path("auth/logout/", CustomLogoutView.as_view(), name="logout"),
    path("auth/register/", RegisterView.as_view(), name="register"),
]

# Recipe URL patterns
recipe_urlpatterns = [
    path("", HomeView.as_view(), name="home"),
    path("recipe/create/", CreateRecipeView.as_view(), name="create_recipe"),
    path(
        "recipe/<str:pk>/",
        RecipeCollaborationView.as_view(),
        name="recipe_collaboration",
    ),
]

# API URL patterns
api_urlpatterns = [
    path(
        "api/recipe/<str:pk>/", RecipeDetailAPIView.as_view(), name="api_recipe_detail"
    ),
    path(
        "api/recipe/<str:recipe_pk>/chat/",
        ChatStreamAPIView.as_view(),
        name="api_chat_stream",
    ),
    path(
        "api/recipe/<str:pk>/refresh/",
        RecipeRefreshAPIView.as_view(),
        name="api_recipe_refresh",
    ),
]

# Combined URL patterns
urlpatterns = auth_urlpatterns + recipe_urlpatterns + api_urlpatterns
