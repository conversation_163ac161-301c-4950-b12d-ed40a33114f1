"""
Authentication views for the hoplogic application.

This module contains views for user authentication including login, logout,
and registration functionality.
"""

from django.contrib import messages
from django.contrib.auth import login
from django.contrib.auth.views import LoginView, LogoutView
from django.shortcuts import redirect
from django.urls import reverse_lazy
from django.views.generic import CreateView

from ..models import User


class CustomLoginView(LoginView):
    """Custom login view using email instead of username."""

    template_name = "auth/login.html"
    redirect_authenticated_user = True

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Login"
        return context

    def form_valid(self, form):
        """Add success message on login."""
        messages.success(self.request, f"Welcome back, {form.get_user().short_name}!")
        return super().form_valid(form)


class CustomLogoutView(LogoutView):
    """Custom logout view with success message."""

    def dispatch(self, request, *args, **kwargs):
        """Add success message on logout."""
        if request.user.is_authenticated:
            messages.success(request, "You have been successfully logged out.")
        return super().dispatch(request, *args, **kwargs)


class RegisterView(CreateView):
    """User registration view."""

    model = User
    template_name = "auth/register.html"
    fields = ["email", "first_name", "last_name", "password"]
    success_url = reverse_lazy("home")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Register"
        return context

    def form_valid(self, form):
        """Create user and log them in."""
        user = form.save(commit=False)
        user.set_password(form.cleaned_data["password"])
        user.save()

        # Log the user in
        login(self.request, user)
        messages.success(self.request, f"Welcome to Hoplogic, {user.short_name}!")

        return redirect(self.success_url)
