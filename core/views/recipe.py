"""
Recipe-related views for the hoplogic application.

This module contains views for recipe management and collaboration.
"""

from django.contrib.auth.mixins import LoginRequiredMixin
from django.shortcuts import get_object_or_404, redirect
from django.views.generic import DetailView, ListView, View

from ..models import Recipe


class HomeView(LoginRequiredMixin, ListView):
    """Home page view displaying user's recipes with options to create new ones."""

    model = Recipe
    template_name = "home.html"
    context_object_name = "recipes"
    ordering = ["-created_at"]

    def get_queryset(self):
        """Return only recipes owned by the current user."""
        return Recipe.objects.filter(user=self.request.user)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Recipe Collection"
        return context


class RecipeCollaborationView(LoginRequiredMixin, DetailView):
    """Recipe collaboration page with agent chat interface."""

    model = Recipe
    template_name = "recipe_collaboration.html"
    context_object_name = "recipe"

    def get_object(self, queryset=None):
        """Return recipe only if owned by the current user."""
        recipe = get_object_or_404(Recipe, pk=self.kwargs["pk"], user=self.request.user)
        return recipe

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = f"Collaborating on {self.object.name}"
        return context


class CreateRecipeView(LoginRequiredMixin, View):
    """View for creating a new blank recipe and redirecting to collaboration page."""

    def post(self, request, *args, **kwargs):
        """Create a new blank recipe and redirect to collaboration page."""
        recipe = Recipe.objects.create(
            user=request.user,
            name="New Recipe",
            description="",
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050,
        )
        return redirect("recipe_collaboration", pk=recipe.pk)
