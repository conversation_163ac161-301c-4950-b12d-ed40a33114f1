# Comprehensive Authorization System Design for Hoplogic Django Application

## Executive Summary

This document outlines a comprehensive authorization system for the hoplogic Django application that handles permissions for both traditional API endpoints and AI agent operations. The system provides unified authorization across REST API calls, agent tool operations, and direct model access while maintaining performance and following object-oriented design principles.

## Current State Analysis

### Existing Authorization Mechanisms

The codebase currently implements basic authorization through:

1. **Recipe Ownership Model**: Recipes have a `user` foreign key establishing ownership
2. **View-Level Protection**: `LoginRequiredMixin` on all recipe views
3. **API Permissions**: DRF `IsAuthenticated` permission class
4. **Manual Ownership Checks**: Direct filtering by `user=self.request.user` in views and API endpoints

### Current Challenges

1. **Inconsistent Authorization**: Manual checks scattered across views
2. **Agent Tool Gap**: No authorization for agent tool calls
3. **Service Layer Bypass**: Services can be called without permission checks
4. **Repetitive Code**: Same ownership logic duplicated across endpoints
5. **Testing Complexity**: Authorization logic mixed with business logic

## Proposed Authorization Approaches

### 1. Service Layer Authorization (Recommended)

**Architecture**: Inject authorization context into services through the existing DI system.

**Pros**:
- Leverages existing DI container
- Consistent with current architecture patterns
- Works seamlessly with agent tools
- Single point of authorization logic
- Testable in isolation

**Cons**:
- Requires refactoring existing services
- Additional complexity in DI configuration

### 2. Decorator-Based Authorization

**Architecture**: Use decorators on service methods to enforce permissions.

**Pros**:
- Minimal code changes
- Clear permission declarations
- Easy to understand and maintain

**Cons**:
- Scattered authorization logic
- Harder to test comprehensively
- Less flexible for complex scenarios

### 3. Django Middleware-Based Authorization

**Architecture**: Custom middleware to inject authorization context into requests.

**Pros**:
- Automatic context injection
- Works with existing Django patterns
- Centralized request processing

**Cons**:
- Only works for HTTP requests
- Doesn't help with agent operations
- Additional middleware complexity

### 4. Custom DRF Permission Classes

**Architecture**: Extend DRF permissions for API endpoints.

**Pros**:
- Native DRF integration
- Consistent with Django patterns
- Good for API-only authorization

**Cons**:
- Limited to DRF views
- Doesn't cover service layer
- Separate from agent authorization

## Recommended Implementation Strategy

**Hybrid Approach**: Combining Service Layer Authorization with Custom DRF Permissions for comprehensive coverage.

### Core Components

1. **Authorization Context**: Immutable context object containing user information
2. **Authorization Service**: Business logic for permission checks
3. **Service Integration**: Authorization injection into existing services
4. **Agent Integration**: Authorization context passing through agent tools
5. **DRF Permissions**: Custom permission classes for API endpoints
6. **Performance Layer**: Caching and optimization strategies

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Web Request   │    │   Agent Tool     │    │   Direct Call   │
│                 │    │   Call           │    │                 │
└─────────┬───────┘    └─────────┬────────┘    └─────────┬───────┘
          │                      │                       │
          ▼                      ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                Authorization Middleware                         │
│              (Context Creation & Injection)                     │
└─────────────────────────┬───────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Service Layer                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ RecipeService   │  │ HopService      │  │ FermentableServ │ │
│  │ + auth_context  │  │ + auth_context  │  │ + auth_context  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────┬───────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                Authorization Service                            │
│              (Permission Logic & Caching)                       │
└─────────────────────────┬───────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                   Repository Layer                              │
│                  (Data Access)                                  │
└─────────────────────────────────────────────────────────────────┘
```

## Implementation Phases

### Phase 1: Core Authorization Infrastructure

**Duration**: 1 week

**Deliverables**:
- Authorization context and service classes
- Middleware for context injection
- Exception handling
- Basic unit tests

### Phase 2: Service Layer Integration

**Duration**: 1 week

**Deliverables**:
- Updated RecipeService with authorization
- Authorization decorators
- Service method modifications
- Service layer tests

### Phase 3: Agent Authorization Integration

**Duration**: 1 week

**Deliverables**:
- Modified ServiceToolFactory
- Agent context passing
- Tool authorization injection
- Agent integration tests

### Phase 4: API Layer Updates

**Duration**: 1 week

**Deliverables**:
- Custom DRF permission classes
- Updated API views
- Removed manual ownership checks
- API authorization tests

### Phase 5: Performance & Polish

**Duration**: 1 week

**Deliverables**:
- Caching implementation
- Batch permission checks
- Performance optimization
- Documentation and migration guide

## Key Benefits

1. **Unified Authorization**: Single system for web, API, and agent operations
2. **Performance Optimized**: Caching and batch operations for efficiency
3. **Testable**: Clear separation of authorization logic
4. **Extensible**: Easy to add new permissions and resources
5. **Consistent**: Same authorization patterns across all access methods
6. **Object-Oriented**: Clean class hierarchies and dependency injection
7. **Agent-Aware**: Seamless integration with AI agent operations

## Risk Mitigation

1. **Backward Compatibility**: Gradual migration with feature flags
2. **Performance Impact**: Comprehensive caching and optimization
3. **Testing Coverage**: Extensive unit and integration tests
4. **Documentation**: Clear migration guides and examples
5. **Rollback Plan**: Ability to revert to current system if needed

## Success Metrics

1. **Security**: Zero authorization bypass incidents
2. **Performance**: <50ms additional latency for permission checks
3. **Code Quality**: 90%+ test coverage for authorization logic
4. **Developer Experience**: Reduced authorization-related bugs
5. **Maintainability**: Single source of truth for permission logic

## Next Steps

1. **Review and Approval**: Stakeholder review of this design document
2. **Technical Spike**: Proof of concept implementation
3. **Resource Allocation**: Assign development team and timeline
4. **Implementation**: Execute phases according to timeline
5. **Testing and Validation**: Comprehensive testing before production deployment

## Detailed Technical Specifications

### Authorization Context

```python
# core/authorization/context.py
from dataclasses import dataclass
from typing import Optional
from django.contrib.auth import get_user_model

User = get_user_model()

@dataclass(frozen=True)
class AuthorizationContext:
    """Context object containing authorization information."""
    user: Optional[User] = None
    is_authenticated: bool = False
    is_superuser: bool = False

    @classmethod
    def from_user(cls, user: Optional[User]) -> "AuthorizationContext":
        if user and user.is_authenticated:
            return cls(
                user=user,
                is_authenticated=True,
                is_superuser=user.is_superuser
            )
        return cls()

    @classmethod
    def system_context(cls) -> "AuthorizationContext":
        """Create system context for internal operations."""
        return cls(is_authenticated=True, is_superuser=True)
```

### Authorization Service

```python
# core/authorization/service.py
from abc import ABC, abstractmethod
from ..models import Recipe
from .context import AuthorizationContext

class AuthorizationService(ABC):
    """Abstract authorization service."""

    @abstractmethod
    def can_view_recipe(self, context: AuthorizationContext, recipe: Recipe) -> bool: ...

    @abstractmethod
    def can_edit_recipe(self, context: AuthorizationContext, recipe: Recipe) -> bool: ...

    @abstractmethod
    def can_delete_recipe(self, context: AuthorizationContext, recipe: Recipe) -> bool: ...

@injectable(interface=AuthorizationService)
class RecipeAuthorizationService(AuthorizationService):
    """Recipe-specific authorization logic."""

    def can_view_recipe(self, context: AuthorizationContext, recipe: Recipe) -> bool:
        if not context.is_authenticated:
            return False
        if context.is_superuser:
            return True
        return recipe.user_id == context.user.id if context.user else False

    def can_edit_recipe(self, context: AuthorizationContext, recipe: Recipe) -> bool:
        return self.can_view_recipe(context, recipe)

    def can_delete_recipe(self, context: AuthorizationContext, recipe: Recipe) -> bool:
        return self.can_view_recipe(context, recipe)
```

### Agent Integration

```python
# core/agents/tools/factory.py (modifications)
def _get_authorization_context_from_agent():
    """Get authorization context from agent configuration."""
    config = var_child_runnable_config.get()
    user_id = config.get("configurable", {}).get("user_id")

    if user_id:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        try:
            user = User.objects.get(id=user_id)
            return AuthorizationContext.from_user(user)
        except User.DoesNotExist:
            pass

    return AuthorizationContext()

class ToolMetadata:
    def _create_tool_function(self, method: Callable, orig_sig: inspect.Signature):
        requires_recipe_id = RECIPE_ID_PARAM in orig_sig.parameters
        requires_auth = hasattr(method, '_requires_authorization')

        def tool_function(**kwargs) -> str:
            service_kwargs = dict(kwargs)

            if requires_recipe_id:
                recipe_id = _get_recipe_id_from_context()
                service_kwargs[RECIPE_ID_PARAM] = recipe_id

            if requires_auth:
                auth_context = _get_authorization_context_from_agent()
                service_kwargs['auth_context'] = auth_context

            try:
                result = method(**service_kwargs)
                return self._format_result(result)
            except AuthorizationError as e:
                return f"Authorization Error: {str(e)}"

        return tool_function
```

### Service Layer Integration

```python
# core/services/recipe.py (modifications)
from ..authorization import AuthorizationContext, require_authorization
from ..authorization.exceptions import AuthorizationError

@injectable
class RecipeService:
    def __init__(
        self,
        recipe_repository: RecipeRepository,
        auth_service: AuthorizationService,
        # ... other dependencies
    ):
        self.recipe_repository = recipe_repository
        self.auth_service = auth_service

    @agent_accessible()
    @require_authorization("can_view_recipe")
    def get_included_fermentables_by_id(
        self,
        recipe_id: str,
        auth_context: AuthorizationContext = None
    ) -> list[FermentableInclusion]:
        """Get the fermentables included in the recipe and their quantities."""
        recipe = self.recipe_repository.get_by_id(recipe_id)
        if not recipe:
            raise ValueError(f"Recipe {recipe_id} not found")

        if auth_context and not self.auth_service.can_view_recipe(auth_context, recipe):
            raise AuthorizationError("Access denied: Cannot view this recipe")

        return self.fermentable_inclusion_repository.get_by_recipe_with_fermentables(recipe_id)
```

### DRF Permission Classes

```python
# core/permissions.py
from rest_framework import permissions
from .models import Recipe
from .authorization import AuthorizationContext, AuthorizationService
from .di import get_container

class RecipePermission(permissions.BasePermission):
    """Advanced recipe permission using authorization service."""

    def has_object_permission(self, request, view, obj):
        if not isinstance(obj, Recipe):
            return False

        container = get_container()
        auth_service = container.resolve(AuthorizationService)
        auth_context = AuthorizationContext.from_user(request.user)

        if request.method in permissions.SAFE_METHODS:
            return auth_service.can_view_recipe(auth_context, obj)
        elif request.method in ['PUT', 'PATCH', 'POST']:
            return auth_service.can_edit_recipe(auth_context, obj)
        elif request.method == 'DELETE':
            return auth_service.can_delete_recipe(auth_context, obj)

        return False
```

### Performance Optimization

```python
# core/authorization/cache.py
from django.core.cache import cache
from typing import Optional

class AuthorizationCache:
    """Cache for authorization decisions."""

    CACHE_TTL = 300  # 5 minutes

    @classmethod
    def get_recipe_permission(
        cls,
        user_id: str,
        recipe_id: str,
        permission: str
    ) -> Optional[bool]:
        """Get cached permission result."""
        cache_key = f"auth:{user_id}:{recipe_id}:{permission}"
        return cache.get(cache_key)

    @classmethod
    def set_recipe_permission(
        cls,
        user_id: str,
        recipe_id: str,
        permission: str,
        result: bool
    ):
        """Cache permission result."""
        cache_key = f"auth:{user_id}:{recipe_id}:{permission}"
        cache.set(cache_key, result, cls.CACHE_TTL)
```

## Implementation Examples

### Recipe Creation with Authorization

```python
# Before (current implementation)
class CreateRecipeView(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        recipe = Recipe.objects.create(
            user=request.user,  # Manual user assignment
            name="New Recipe",
        )
        return redirect("recipe_collaboration", pk=recipe.pk)

# After (with authorization system)
class CreateRecipeView(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        container = get_container()
        recipe_service = container.resolve(RecipeService)

        recipe = recipe_service.create_recipe(
            name="New Recipe",
            auth_context=AuthorizationContext.from_user(request.user)
        )
        return redirect("recipe_collaboration", pk=recipe.pk)
```

### Agent Tool Authorization

```python
# Agent conversation flow
user_message = "Add 2 pounds of Pale 2-Row malt to my recipe"

# Agent calls tool (automatically authorized)
agent_tool_call = {
    "tool": "add_fermentable_to_recipe",
    "arguments": {
        "fermentable_id": "fer_123456789_abcdef",
        "quantity": 2.0,
        "quantity_unit": "pounds"
        # recipe_id and auth_context injected automatically
    }
}

# Tool execution with authorization
def add_fermentable_to_recipe_tool(**kwargs):
    # ServiceToolFactory automatically injects:
    # - recipe_id from agent context
    # - auth_context from user_id in agent config

    service = container.resolve(RecipeService)
    result = service.add_fermentable_to_recipe(**kwargs)
    # If user doesn't own recipe, AuthorizationError is caught and returned
    return result
```

## Testing Strategy

```python
# tests/authorization/test_service.py
class TestRecipeAuthorizationService:
    def test_can_view_recipe_owner(self):
        user = UserFactory()
        recipe = RecipeFactory(user=user)
        context = AuthorizationContext.from_user(user)

        auth_service = RecipeAuthorizationService()
        assert auth_service.can_view_recipe(context, recipe) is True

    def test_can_view_recipe_non_owner(self):
        owner = UserFactory()
        other_user = UserFactory()
        recipe = RecipeFactory(user=owner)
        context = AuthorizationContext.from_user(other_user)

        auth_service = RecipeAuthorizationService()
        assert auth_service.can_view_recipe(context, recipe) is False
```

---

*This document serves as the foundation for implementing a robust, scalable authorization system that meets the unique requirements of the hoplogic AI-powered brewing application.*
