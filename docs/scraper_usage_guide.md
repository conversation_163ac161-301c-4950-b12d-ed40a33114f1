# Ultra-Simple LLM-Driven Ingredient Scraper

The HopLogic application includes an ultra-simple ingredient scraper that lets the LLM do ALL the work - no manual filtering, no complex logic, just pure AI intelligence.

## Architecture Overview

The scraper system consists of just:

1. **SimpleScraper** - Single class that handles everything
2. **LLM Agent** - Does all the intelligent work (finding ingredients, extracting data)
3. **Pydantic Schemas** - Type-safe data validation

The system is designed to be:
- **LLM-driven** - AI makes all the intelligent decisions
- **Ultra-simple** - Only 180 lines of code total
- **Highly effective** - Better results with less complexity

## Usage

### Basic Scraping

Scrape ingredients from a website:

```bash
# Scrape hops from Beer Maverick
python manage.py scrape_ingredients https://beermaverick.com/hops/ --type hops

# Scrape fermentables from Briess
python manage.py scrape_ingredients https://www.brewingwithbriess.com/products/ --type fermentables

# Scrape yeasts from White Labs
python manage.py scrape_ingredients https://www.whitelabs.com/yeast-bank --type yeasts
```

### Configuration Options

```bash
python manage.py scrape_ingredients https://example.com \
    --type hops \
    --max-ingredients 50 \
    --output-dir custom_scraped_data
```

### Testing the Scraper

Test with a single URL:

```bash
python manage.py test_scraper https://beermaverick.com/hops/ --type hops
```

## Data Import

After scraping, import the CSV files into Django models:

```bash
# Import all CSV files from scraped_data directory
python manage.py import_scraped_data scraped_data/*.csv

# Dry run to see what would be imported
python manage.py import_scraped_data scraped_data/*.csv --dry-run

# Update existing ingredients instead of skipping
python manage.py import_scraped_data scraped_data/*.csv --update-existing
```

## Data Schemas

### Fermentables

```python
{
    "name": "Pale Ale Malt",
    "country_of_origin": "USA",
    "notes": "Rich malty flavor",
    "fermentable_type": "BASE",
    "extract_potential_ppg": 37.0,
    "color_lovibond": 3.5,
    "requires_mashing": True
}
```

### Yeasts

```python
{
    "name": "California Ale Yeast",
    "laboratory": "White Labs",
    "product_id": "WLP001",
    "yeast_type": "ALE",
    "yeast_form": "LIQUID",
    "min_temperature_fahrenheit": 68.0,
    "max_temperature_fahrenheit": 73.0,
    "attenuation_percent": 76.0,
    "flocculation": "MEDIUM",
    "alcohol_tolerance_percent": 11.0,
    "description": "Clean fermenting strain"
}
```

### Hops

```python
{
    "name": "Cascade",
    "country_of_origin": "USA",
    "notes": "Classic American hop with citrus notes",
    "alpha_acid": 5.5,
    "beta_acid": 6.0,
    "aroma": True,
    "bittering": True
}
```

## Features

### Intelligent Extraction
- Uses LLM to parse HTML and extract structured brewing data
- Handles various website layouts and formats
- Validates data against Pydantic schemas

### Respectful Scraping
- Built-in rate limiting (configurable delays between requests)
- Retry logic with exponential backoff
- Proper User-Agent headers
- Error handling and logging

### Data Quality
- Pydantic schema validation
- Automatic data cleaning and normalization
- Duplicate detection and handling
- Comprehensive error reporting

### Multi-Agent Coordination
- LangGraph-based workflow orchestration
- Parallel processing capabilities
- Error recovery and fallback strategies
- Detailed progress tracking

## Configuration

### Environment Variables

Set your OpenAI API key for LLM-powered extraction:

```bash
export OPENAI_API_KEY="your-api-key-here"
```

### Rate Limiting

The scraper includes built-in rate limiting to be respectful to target websites:

- Default delay: 1-2 seconds between requests
- Configurable retry logic
- Exponential backoff on failures

### Output

CSV files are saved with timestamps and source information:

```
scraped_data/
├── fermentables_brewingwithbriess_com_20240120_143022.csv
├── yeasts_whitelabs_com_20240120_143045.csv
└── hops_beermaverick_com_20240120_143108.csv
```

## Extending the Scraper

### Adding New Websites

1. Create site-specific discovery methods in `url_discovery.py`
2. Add URL patterns to `URLValidator.is_ingredient_url()`
3. Update default URLs in management commands

### Custom Data Fields

1. Extend Pydantic schemas in `schemas.py`
2. Update CSV export methods in `data_export.py`
3. Modify Django model import logic

### New Ingredient Types

1. Add new schema class to `schemas.py`
2. Create corresponding Django model
3. Update coordinator and export agents
4. Add management command options

## Troubleshooting

### Common Issues

1. **Rate Limiting Errors**: Increase delay between requests
2. **LLM Extraction Failures**: Check API key and model availability
3. **Schema Validation Errors**: Review extracted data format
4. **Import Failures**: Verify CSV format matches Django models

### Debugging

Enable verbose logging:

```bash
python manage.py scrape_ingredients --verbose
```

Check scraped data before import:

```bash
python manage.py import_scraped_data scraped_data/*.csv --dry-run --verbose
```

## Performance Considerations

- The scraper processes URLs sequentially to respect rate limits
- Large sites may take significant time to scrape completely
- Consider using `--max-urls` to limit scope for testing
- Monitor API usage if using OpenAI models

## Legal and Ethical Considerations

- Always respect robots.txt files
- Use reasonable rate limits
- Only scrape publicly available data
- Consider reaching out to website owners for permission
- Comply with website terms of service
