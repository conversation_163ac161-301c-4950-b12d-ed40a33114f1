# Hoplogic Testing Guide

## Overview

This document outlines the testing patterns, conventions, and best practices for the Hoplogic Django application. Our testing framework follows DRY (Don't Repeat Yourself) principles, mirrors the package structure, and focuses on true unit testing with minimal dependencies.

## Testing Principles

### 1. DRY (Don't Repeat Yourself)
- Use factories for test data creation instead of manual object creation
- Create reusable fixtures and base test classes
- Share common test utilities across test modules
- Avoid duplicating setup code

### 2. Mirror Package Structure
- Test files should follow the exact same directory structure as source code
- Example: `core/models/recipe.py` → `tests/models/test_recipe.py`
- This makes tests easy to find and maintain

### 3. True Unit Testing
- Each test should focus on a single unit of functionality
- Mock external dependencies (databases, APIs, file systems)
- Tests should be fast and isolated
- Use dependency injection to make code testable

### 4. Terse and Readable
- Tests should be concise but clear
- Use descriptive test names that explain what is being tested
- Minimal boilerplate code
- Clear arrange-act-assert structure

## Directory Structure

```
tests/
├── conftest.py              # Pytest fixtures and configuration
├── factories.py             # Model factories for test data
├── base.py                  # Base test classes
├── models/                  # Mirror core/models/
├── repositories/            # Mirror core/repositories/
├── services/                # Mirror core/services/
├── serializers/             # Mirror core/serializers/
├── views/                   # Mirror core/views/
├── agents/                  # Mirror core/agents/
└── integration/             # Integration tests
```

## Testing Patterns

### Model Testing
Focus on business logic, calculations, and model methods:

```python
# Good: Testing business logic
def test_recipe_calculates_total_ibus_correctly(self, recipe_with_hops):
    assert recipe_with_hops.total_ibus == 45.2

# Bad: Testing Django ORM functionality
def test_recipe_can_be_saved():
    recipe = Recipe(name="Test")
    recipe.save()
    assert Recipe.objects.count() == 1
```

### Repository Testing
Test data access patterns with database mocking:

```python
# Good: Testing repository logic with mocked database
@patch('core.models.Recipe.objects')
def test_recipe_repository_get_by_user(self, mock_objects):
    mock_objects.filter.return_value = [recipe_factory()]
    recipes = self.repository.get_by_user("user123")
    mock_objects.filter.assert_called_with(user_id="user123")
```

### Service Testing
Test business logic coordination with mocked repositories:

```python
# Good: Testing service logic with mocked dependencies
def test_hop_service_adds_boil_hop_to_recipe(self):
    with patch.object(self.service, 'recipe_repository') as mock_repo:
        mock_repo.get_by_id_or_raise.return_value = recipe_factory()
        result = self.service.add_boil_hop_to_recipe(...)
        assert result.time_minutes == 60
```

### Agent Testing
Test agent logic with mocked LLM calls:

```python
# Good: Testing agent behavior with mocked LLM
@patch('core.agents.master_brewer.ChatOpenAI')
def test_master_brewer_processes_recipe_request(self, mock_llm):
    mock_llm.return_value.invoke.return_value = "Recipe updated"
    result = self.agent.run("Add cascade hops", recipe_factory())
    assert "cascade" in str(result).lower()
```

## Factories and Fixtures

### Model Factories
Use factories for consistent test data creation:

```python
# factories.py
class RecipeFactory:
    @staticmethod
    def create(**kwargs):
        defaults = {
            'name': 'Test Recipe',
            'batch_size_gallons': 5.0,
            'mash_efficiency_percent': 75.0,
            'target_original_gravity': 1.050,
        }
        defaults.update(kwargs)
        return Recipe(**defaults)

class HopFactory:
    @staticmethod
    def create(**kwargs):
        defaults = {
            'name': 'Cascade',
            'alpha_acid': 5.5,
            'hop_type': 'aroma',
        }
        defaults.update(kwargs)
        return Hop(**defaults)
```

### Pytest Fixtures
Create reusable fixtures in conftest.py:

```python
# conftest.py
@pytest.fixture
def recipe():
    return RecipeFactory.create()

@pytest.fixture
def hop():
    return HopFactory.create()

@pytest.fixture
def recipe_with_hops(recipe, hop):
    BoilHopInclusionFactory.create(recipe=recipe, hop=hop, time_minutes=60)
    return recipe
```

## Base Test Classes

Create base classes to reduce boilerplate:

```python
# base.py
class BaseModelTest:
    """Base class for model tests."""
    
    def test_model_has_stripe_like_id(self):
        instance = self.factory.create()
        assert instance.id.startswith(instance._get_id_prefix())
        assert len(instance.id.split('_')) == 3

class BaseRepositoryTest:
    """Base class for repository tests."""
    
    def setup_method(self):
        self.repository = self.repository_class()
    
    def test_get_by_id_returns_none_for_nonexistent(self):
        result = self.repository.get_by_id("nonexistent")
        assert result is None
```

## Testing Tools and Libraries

- **pytest**: Primary testing framework
- **pytest-django**: Django integration for pytest
- **unittest.mock**: For mocking dependencies
- **factory_boy**: Alternative to custom factories (optional)
- **freezegun**: For testing time-dependent code

## Running Tests

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/models/test_recipe.py

# Run with coverage
pytest --cov=core

# Run tests matching pattern
pytest -k "test_recipe"
```

## Common Anti-Patterns to Avoid

### ❌ Bad Examples

```python
# Don't test Django's built-in functionality
def test_model_str_method():
    recipe = Recipe(name="IPA")
    assert str(recipe) == "IPA"

# Don't create objects manually in every test
def test_recipe_calculation():
    user = User.objects.create(email="<EMAIL>")
    recipe = Recipe.objects.create(name="Test", user=user)
    # ... rest of test

# Don't test implementation details
def test_service_calls_repository_method():
    service.do_something()
    assert service.repository.some_method.called
```

### ✅ Good Examples

```python
# Test business logic and calculations
def test_recipe_calculates_original_gravity_from_fermentables():
    recipe = recipe_factory(fermentables=[
        fermentable_inclusion_factory(gravity_points=30),
        fermentable_inclusion_factory(gravity_points=20),
    ])
    assert recipe.calculated_original_gravity == 1.050

# Use factories for consistent test data
def test_hop_inclusion_calculates_ibu_contribution():
    inclusion = boil_hop_inclusion_factory(
        hop=hop_factory(alpha_acid=6.0),
        quantity=1.0,
        time_minutes=60
    )
    assert inclusion.ibu_contribution > 0

# Test behavior, not implementation
def test_service_adds_hop_to_recipe():
    result = self.service.add_boil_hop_to_recipe(
        recipe_id="rec_123",
        hop_id="hop_456",
        quantity=1.0,
        time_minutes=60
    )
    assert result.hop.id == "hop_456"
    assert result.time_minutes == 60
```

## Next Steps

1. Set up the testing infrastructure with base classes and factories
2. Implement model tests focusing on business logic
3. Create repository tests with proper mocking
4. Build service tests with dependency injection
5. Add agent system tests with LLM mocking
6. Implement view and serializer tests
7. Create integration tests for complete workflows

This guide will be updated as the testing framework evolves and new patterns emerge.
