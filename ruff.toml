target-version = "py310"
line-length = 88
extend-exclude = ["migrations"]

[lint]
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "ARG", # flake8-unused-arguments
    "SIM", # flake8-simplify
    "TCH", # flake8-type-checking
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "ARG001", # unused function argument
    "ARG002", # unused method argument
]

[lint.per-file-ignores]
"*/tests/*" = ["ARG", "S"]
"*/migrations/*" = ["E", "W", "F", "B", "C4", "UP", "ARG", "SIM", "TCH"]

[lint.isort]
known-first-party = ["hoplogic", "core"]
