#!/bin/bash

# Development startup script for hoplogic
# Starts dependencies via <PERSON><PERSON> and Django locally

set -e

echo "🚀 Starting hoplogic development environment..."

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo "❌ Docker is not running. Please start Docker Desktop and try again."
    exit 1
fi

# Check if Poetry is installed
if ! command -v poetry &> /dev/null; then
    echo "❌ Poetry is not installed. Please install Poetry from https://python-poetry.org/docs/#installation"
    exit 1
fi

# Start Docker dependencies
echo "🐳 Starting dependencies (PostgreSQL + Redis)..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 5

# Check if database is ready
echo "🔍 Checking database connection..."
until docker-compose exec -T db pg_isready -U hoplogic_user -d hoplogic > /dev/null 2>&1; do
    echo "Waiting for PostgreSQL..."
    sleep 2
done

echo "✅ Dependencies are ready!"

# Run any pending migrations
echo "🔄 Running migrations..."
poetry run python manage.py migrate

echo ""
echo "🎉 Development environment is ready!"
echo ""
echo "Dependencies running:"
echo "  🗄️  PostgreSQL: localhost:5432"
echo "  🔴 Redis: localhost:6379"
echo ""
echo "Starting Django development server..."
echo "  🌐 Web: http://localhost:8000"
echo ""

# Start Django development server
poetry run python manage.py runserver
