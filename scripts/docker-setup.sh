#!/bin/bash

# Dependencies setup script for hoplogic (PostgreSQL + Redis via Docker)

set -e

echo "🐳 Setting up hoplogic dependencies with Docker..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker Desktop from https://www.docker.com/products/docker-desktop"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo "❌ Docker is not running. Please start Docker Desktop and try again."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not available. Please install Docker Compose."
    exit 1
fi

echo "✅ Docker is installed and running"

# Check if Poetry is installed
if ! command -v poetry &> /dev/null; then
    echo "❌ Poetry is not installed. Please install Poetry from https://python-poetry.org/docs/#installation"
    exit 1
fi

echo "✅ Poetry is installed"

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.example..."
    cp .env.example .env
    echo "✅ .env file created. You may want to edit it with your specific configuration."
else
    echo "✅ .env file already exists"
fi

# Start Docker services (PostgreSQL + Redis)
echo "🏗️  Starting Docker services (PostgreSQL + Redis)..."
docker-compose up -d

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Install Python dependencies
echo "📦 Installing Python dependencies with Poetry..."
poetry install

# Run migrations
echo "🔄 Running database migrations..."
poetry run python manage.py migrate

# Check if we should create a superuser
echo "👤 Would you like to create a superuser? (y/n)"
read -r create_superuser
if [[ $create_superuser =~ ^[Yy]$ ]]; then
    poetry run python manage.py createsuperuser
fi

# Check if we should create sample data
echo "📊 Would you like to create sample brewing data? (y/n)"
read -r create_sample
if [[ $create_sample =~ ^[Yy]$ ]]; then
    poetry run python manage.py create_sample_data
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Your hoplogic dependencies are now running:"
echo "  🗄️  PostgreSQL: localhost:5432"
echo "  🔴 Redis: localhost:6379"
echo ""
echo "To start the Django development server:"
echo "  🚀 poetry run python manage.py runserver"
echo ""
echo "Useful commands:"
echo "  📋 View dependency logs: docker-compose logs -f"
echo "  🛑 Stop dependencies: docker-compose down"
echo "  🔄 Restart dependencies: docker-compose restart"
echo "  🧪 Run tests: poetry run python manage.py test"
echo ""
