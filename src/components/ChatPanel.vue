<template>
  <section class="chat-panel">
    <!-- Cha<PERSON> -->
    <div class="chat-header">
      <div class="chat-title">
        <h3>Recipe Assistant</h3>
        <span class="chat-subtitle">Ask questions, get suggestions, modify your recipe</span>
      </div>
    </div>

    <!-- Chat Messages -->
    <div class="chat-messages" ref="messagesContainer">
        <!-- Welcome Message -->
        <div class="message ai-message">
          <div class="message-avatar">⚗️</div>
          <div class="message-content">
            <div class="message-bubble">
              <p>Welcome to the Recipe Assistant!</p>
              <p>I can help you:</p>
              <ul>
                <li>Adjust grain bills and hop schedules</li>
                <li>Calculate brewing statistics</li>
                <li>Suggest style-appropriate modifications</li>
                <li>Troubleshoot brewing issues</li>
              </ul>
              <p>Ask me anything about your recipe to get started.</p>
            </div>
          </div>
        </div>

        <!-- Dynamic Messages -->
        <div
          v-for="message in messages"
          :key="message.id"
          class="message"
          :class="message.type + '-message'"
        >
          <div class="message-avatar">
            {{ message.type === 'user' ? '👤' : '⚗️' }}
          </div>
          <div class="message-content">
            <div class="message-bubble" :class="{ 'tool-bubble': message.isToolCall }">
              <div v-if="message.isToolCall" class="tool-info">{{ message.toolInfo }}</div>
              <div v-html="formatMessage(message.content)"></div>
            </div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>

        <!-- Thinking Message -->
        <div v-if="isThinking" class="message ai-message thinking-message">
          <div class="message-avatar">⚗️</div>
          <div class="message-content">
            <div class="message-bubble thinking">
              <div class="thinking-animation">
                <span></span><span></span><span></span>
              </div>
              <span class="thinking-text">{{ thinkingText }}</span>
            </div>
          </div>
        </div>
      </div>

    <!-- Chat Input -->
    <div class="chat-input">
      <div class="input-container">
        <textarea
          v-model="currentMessage"
          class="chat-textarea"
          placeholder="Ask me anything about your recipe..."
          rows="2"
          @keydown="handleKeydown"
          :disabled="isProcessing"
        ></textarea>
        <button
          class="send-btn"
          type="button"
          @click="sendMessage"
          :disabled="!currentMessage.trim() || isProcessing"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="22" y1="2" x2="11" y2="13"/>
            <polygon points="22,2 15,22 11,13 2,9 22,2"/>
          </svg>
        </button>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, nextTick, onMounted } from 'vue'
import axios from 'axios'

const props = defineProps({
  recipeId: String,
  csrfToken: String
})

const emit = defineEmits(['recipe-updated'])

// Reactive state
const messages = ref([])
const currentMessage = ref('')
const isProcessing = ref(false)
const isThinking = ref(false)
const thinkingText = ref('Thinking about your request...')
const messagesContainer = ref(null)

let messageIdCounter = 0

// Methods
const sendMessage = async () => {
  const message = currentMessage.value.trim()
  if (!message || isProcessing.value) return

  isProcessing.value = true

  // Add user message
  addMessage({
    type: 'user',
    content: message,
    timestamp: new Date()
  })

  currentMessage.value = ''
  isThinking.value = true

  try {
    const response = await axios.post(`/api/recipe/${props.recipeId}/chat/`, {
      message: message
    }, {
      headers: {
        'X-CSRFToken': props.csrfToken,
        'Content-Type': 'application/json'
      }
    })

    isThinking.value = false

    if (response.data.status === 'error') {
      addMessage({
        type: 'ai',
        content: 'Sorry, there was an error: ' + response.data.error,
        timestamp: new Date()
      })
      return
    }

    // Process events
    let hasResponse = false
    for (const event of response.data.events || []) {
      await new Promise(resolve => setTimeout(resolve, 300)) // Smooth delay

      if (event.event_type === 'ThinkingEvent') {
        thinkingText.value = event.content
        isThinking.value = true
      } else if (event.event_type === 'ToolCallEvent') {
        addMessage({
          type: 'ai',
          content: event.content,
          isToolCall: true,
          toolInfo: 'Using tools...',
          timestamp: new Date()
        })
      } else if (event.event_type === 'ToolOutputEvent') {
        addMessage({
          type: 'ai',
          content: event.content,
          isToolCall: true,
          toolInfo: 'Tool results:',
          timestamp: new Date()
        })
      } else if (event.event_type === 'ResponseEvent' || event.event_type === 'AIMessageEvent') {
        hasResponse = true
        addMessage({
          type: 'ai',
          content: event.content,
          timestamp: new Date()
        })
      }
    }

    if (!hasResponse) {
      addMessage({
        type: 'ai',
        content: "I've processed your request and updated the recipe accordingly.",
        timestamp: new Date()
      })
    }

    // Notify parent to refresh recipe
    emit('recipe-updated')

  } catch (error) {
    console.error('Error sending message:', error)
    addMessage({
      type: 'ai',
      content: 'Sorry, there was an error processing your message. Please try again.',
      timestamp: new Date()
    })
  } finally {
    isThinking.value = false
    isProcessing.value = false
  }
}

const addMessage = (message) => {
  messages.value.push({
    id: ++messageIdCounter,
    ...message
  })

  nextTick(() => {
    scrollToBottom()
  })
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const handleKeydown = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

const formatMessage = (content) => {
  // Simple markdown formatting
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/\n\n/g, '</p><p>')
    .replace(/\n/g, '<br>')
    .replace(/^(.*)$/, '<p>$1</p>')
}

const formatTime = (timestamp) => {
  return new Intl.DateTimeFormat('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  }).format(timestamp)
}

onMounted(() => {
  scrollToBottom()
})
</script>

<style scoped>
.chat-panel {
  background: var(--bg-secondary);
  border-left: 2px solid var(--border-color);
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-header {
  flex-shrink: 0;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-tertiary);
}

.chat-title h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.chat-subtitle {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-top: 0.125rem;
  display: block;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  scroll-behavior: smooth;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

.chat-messages::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: var(--accent-primary);
}

/* Messages */
.message {
  display: flex;
  gap: 1rem;
  max-width: 90%;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ai-message {
  align-self: flex-start;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  border: 2px solid var(--border-color);
}

.message-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.message-bubble {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 1.25rem;
  padding: 1.25rem;
  color: var(--text-primary);
  line-height: 1.6;
  font-size: 0.95rem;
}

.user-message .message-bubble {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.message-bubble :deep(p) {
  margin: 0 0 0.75rem 0;
}

.message-bubble :deep(p:last-child) {
  margin-bottom: 0;
}

.message-bubble :deep(ul), .message-bubble :deep(ol) {
  margin: 0.75rem 0;
  padding-left: 1.5rem;
}

.message-bubble :deep(li) {
  margin-bottom: 0.5rem;
}

.message-time {
  font-size: 0.75rem;
  color: var(--text-muted);
  padding: 0 0.75rem;
}

.user-message .message-time {
  text-align: right;
}

/* Tool Messages */
.tool-bubble {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--accent-primary) !important;
}

.tool-info {
  font-weight: 500;
  color: var(--accent-primary);
  margin-bottom: 0.5rem;
}

/* Thinking Animation */
.thinking {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.thinking-animation {
  display: flex;
  gap: 0.25rem;
}

.thinking-animation span {
  width: 0.5rem;
  height: 0.5rem;
  background: var(--accent-primary);
  border-radius: 50%;
  animation: thinking 1.4s ease-in-out infinite both;
}

.thinking-animation span:nth-child(1) { animation-delay: -0.32s; }
.thinking-animation span:nth-child(2) { animation-delay: -0.16s; }

@keyframes thinking {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Chat Input */
.chat-input {
  flex-shrink: 0;
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  background: var(--bg-tertiary);
}

.input-container {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
  width: 100%;
}

.chat-textarea {
  flex: 1;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  padding: 0.75rem;
  color: var(--text-primary);
  font-family: inherit;
  font-size: 0.9rem;
  resize: none;
  min-height: 2.25rem;
  max-height: 5rem;
  transition: all 0.2s ease;
  line-height: 1.4;
  box-sizing: border-box;
}

.chat-textarea:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(74, 158, 255, 0.1);
}

.chat-textarea::placeholder {
  color: var(--text-muted);
}

.chat-textarea:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.25rem;
  height: 2.25rem;
  background: var(--accent-primary);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  margin-bottom: 0.125rem;
}

.send-btn:hover:not(:disabled) {
  background: var(--accent-secondary);
  transform: scale(1.05);
}

.send-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-messages {
    padding: 1rem;
  }

  .chat-input {
    padding: 0.75rem;
  }

  .input-container {
    gap: 0.375rem;
  }

  .send-btn {
    width: 2rem;
    height: 2rem;
  }

  .chat-textarea {
    min-height: 2rem;
    max-height: 3rem;
    padding: 0.625rem;
    font-size: 0.875rem;
    border-radius: 0.5rem;
  }
}
</style>
