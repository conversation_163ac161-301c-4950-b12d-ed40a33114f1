<template>
  <div class="recipe-collaboration">
    <!-- Header Bar -->
    <header class="collab-header">
      <div class="header-content">
        <div class="recipe-info">
          <h1 class="recipe-title">{{ recipe?.name || 'Loading...' }}</h1>
          <span class="recipe-subtitle">{{ recipe?.beer_style?.name || 'Custom Style' }}</span>
        </div>
        <div class="header-actions">
          <button class="action-btn export-btn" title="Export Recipe">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
              <polyline points="7,10 12,15 17,10"/>
              <line x1="12" y1="15" x2="12" y2="3"/>
            </svg>
          </button>
          <a href="/" class="action-btn back-btn" title="Back to Recipes">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M19 12H5"/>
              <path d="M12 19l-7-7 7-7"/>
            </svg>
          </a>
        </div>
      </div>
    </header>

    <!-- Main Content Area -->
    <main class="collab-main">
      <!-- Recipe Panel (60% width) -->
      <div class="recipe-section">
        <RecipePanel
          :recipe="recipe"
          :loading="loading"
          @refresh="refreshRecipe"
        />
      </div>

      <!-- Chat Panel (40% width) -->
      <div class="chat-panel-clean">
        <!-- Chat Header -->
        <div class="chat-header-clean">
          <h3>Recipe Assistant</h3>
          <span>Ask questions, get suggestions, modify your recipe</span>
        </div>

        <!-- Chat Messages -->
        <div class="chat-messages-clean" ref="messagesContainer">
          <!-- Welcome Message -->
          <div v-if="messages.length === 0" class="message ai-message">
            <div class="message-content">
              <p>Welcome! I can help you adjust grain bills, hop schedules, calculate brewing statistics, and more. Ask me anything about your recipe to get started.</p>
            </div>
          </div>

          <!-- Chat Messages -->
          <div v-for="message in messages" :key="message.id" :class="['message', message.type + '-message']">
            <div class="message-content">
              <div v-if="message.type === 'thinking'" class="thinking-indicator">
                <div class="thinking-dots">
                  <span></span><span></span><span></span>
                </div>
                <span>{{ message.content }}</span>
              </div>
              <div v-else-if="message.type === 'tool'" class="tool-call">
                <strong>🔧 {{ message.tool_name }}</strong>
                <p>{{ message.content }}</p>
              </div>
              <div v-else v-html="renderMarkdown(message.content)"></div>
            </div>
          </div>
        </div>

        <!-- Chat Input -->
        <div class="chat-input-clean">
          <div class="input-container-clean">
            <textarea
              v-model="currentMessage"
              placeholder="Ask me anything about your recipe..."
              class="chat-textarea-clean"
              @keydown="handleKeydown"
              :disabled="isProcessing"
              rows="2"
            ></textarea>
            <button
              class="send-btn-clean"
              @click="sendMessage"
              :disabled="!currentMessage.trim() || isProcessing"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="22" y1="2" x2="11" y2="13"/>
                <polygon points="22,2 15,22 11,13 2,9 22,2"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import RecipePanel from './RecipePanel.vue'
import { useRecipe } from '../composables/useRecipe'

const props = defineProps({
  recipeId: String,
  csrfToken: String
})

const { recipe, loading, fetchRecipe, refreshRecipe } = useRecipe(props.recipeId)

const handleRecipeUpdate = () => {
  refreshRecipe()
}

// Chat functionality
const messages = ref([])
const currentMessage = ref('')
const isProcessing = ref(false)
const messagesContainer = ref(null)

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const addMessage = (type, content, toolName = null) => {
  const message = {
    id: Date.now() + Math.random(),
    type,
    content,
    tool_name: toolName,
    timestamp: new Date()
  }
  messages.value.push(message)
  setTimeout(scrollToBottom, 100)
  return message
}

const sendMessage = async () => {
  if (!currentMessage.value.trim() || isProcessing.value) return

  const userMessage = currentMessage.value.trim()
  currentMessage.value = ''

  // Add user message
  addMessage('user', userMessage)

  isProcessing.value = true

  try {
    const response = await fetch(`/api/recipe/${props.recipeId}/chat/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': props.csrfToken
      },
      body: JSON.stringify({
        message: userMessage
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()

    if (data.status === 'error') {
      addMessage('error', 'Sorry, there was an error: ' + data.error)
      return
    }

    // Process events from the agent
    let hasResponse = false
    for (const event of data.events || []) {
      await new Promise(resolve => setTimeout(resolve, 300)) // Smooth delay

      if (event.event_type === 'AIMessageEvent') {
        addMessage('ai', event.content)
        hasResponse = true
      } else if (event.event_type === 'ToolCallEvent') {
        if (event.tool_calls) {
          for (const toolCall of event.tool_calls) {
            addMessage('tool', `Using ${toolCall.tool_name}`, toolCall.tool_name)
          }
        }
      } else if (event.event_type === 'ThinkingEvent') {
        addMessage('thinking', event.content)
      }
    }

    if (!hasResponse) {
      addMessage('ai', "I've processed your request and updated the recipe accordingly.")
    }

    // Notify parent to refresh recipe
    handleRecipeUpdate()
  } catch (error) {
    console.error('Error sending message:', error)
    addMessage('error', 'Sorry, there was an error processing your message. Please try again.')
  } finally {
    isProcessing.value = false
  }
}

const handleKeydown = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

const renderMarkdown = (content) => {
  if (!content) return ''

  return content
    // Headers
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^# (.*$)/gm, '<h1>$1</h1>')

    // Bold and italic
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')

    // Code blocks and inline code
    .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
    .replace(/`(.*?)`/g, '<code>$1</code>')

    // Lists
    .replace(/^\* (.*$)/gm, '<li>$1</li>')
    .replace(/^- (.*$)/gm, '<li>$1</li>')
    .replace(/^\d+\. (.*$)/gm, '<li>$1</li>')

    // Wrap consecutive list items in ul/ol
    .replace(/(<li>.*<\/li>)/gs, (match) => {
      return '<ul>' + match + '</ul>'
    })

    // Links
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')

    // Line breaks and paragraphs
    .replace(/\n\n/g, '</p><p>')
    .replace(/\n/g, '<br>')
    .replace(/^(.*)$/, '<p>$1</p>')

    // Clean up empty paragraphs
    .replace(/<p><\/p>/g, '')
    .replace(/<p>(<h[1-6]>.*<\/h[1-6]>)<\/p>/g, '$1')
    .replace(/<p>(<ul>.*<\/ul>)<\/p>/g, '$1')
    .replace(/<p>(<pre>.*<\/pre>)<\/p>/g, '$1')
}

onMounted(() => {
  console.log('Vue app mounted!')
  console.log('Recipe ID:', props.recipeId)
  fetchRecipe()
})
</script>

<style scoped>
/* Main Container */
.recipe-collaboration {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  overflow: hidden;
}

/* Header */
.collab-header {
  flex-shrink: 0;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: none;
}

.recipe-info {
  display: flex;
  flex-direction: column;
}

.recipe-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.recipe-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition);
  cursor: pointer;
}

.action-btn:hover {
  background: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--accent-primary);
}

/* Main layout */
.collab-main {
  flex: 1;
  display: flex;
  min-height: 0;
  overflow: hidden;
}

.recipe-section {
  flex: 0 0 60%;
  overflow: hidden;
  height: 100%;
}

.chat-section {
  flex: 0 0 40%;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Clean Chat Layout */
.chat-panel-clean {
  position: relative;
  background: var(--bg-secondary);
  border-left: 2px solid var(--border-color);
}

.chat-header-clean {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-tertiary);
}

.chat-header-clean h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.chat-header-clean span {
  font-size: 0.8rem;
  color: var(--text-secondary);
  display: block;
  margin-top: 0.25rem;
}

.chat-messages-clean {
  height: calc(100% - 160px);
  overflow-y: auto;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  scroll-behavior: smooth;
}

.chat-input-clean {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  background: var(--bg-tertiary);
  box-sizing: border-box;
}

.input-container-clean {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  height: 100%;
}

.chat-textarea-clean {
  flex: 1;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  padding: 0.75rem;
  color: var(--text-primary);
  font-family: inherit;
  font-size: 0.9rem;
  resize: none;
  height: 40px;
  outline: none;
  transition: border-color 0.2s ease;
}

.chat-textarea-clean:focus {
  border-color: var(--accent-primary);
}

.send-btn-clean {
  background: var(--accent-primary);
  border: none;
  border-radius: 50%;
  width: 2.25rem;
  height: 2.25rem;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-btn-clean:hover {
  background: var(--accent-secondary);
  transform: scale(1.05);
}

.send-btn-clean:disabled {
  background: var(--text-tertiary);
  cursor: not-allowed;
  transform: none;
}

.message {
  display: flex;
  gap: 1rem;
}

.message-content {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 1.25rem;
  padding: 1.25rem;
  color: var(--text-primary);
  max-width: 85%;
}

.message-content p {
  margin: 0;
  line-height: 1.5;
}

/* Message Types */
.user-message {
  justify-content: flex-end;
}

.user-message .message-content {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.ai-message .message-content {
  background: var(--bg-primary);
  border-color: var(--border-color);
}

.thinking-message .message-content {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
  opacity: 0.8;
}

.tool-message .message-content {
  background: var(--accent-secondary);
  color: white;
  border-color: var(--accent-secondary);
}

.error-message .message-content {
  background: #dc3545;
  color: white;
  border-color: #dc3545;
}

/* Thinking Indicator */
.thinking-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.thinking-dots {
  display: flex;
  gap: 0.25rem;
}

.thinking-dots span {
  width: 0.5rem;
  height: 0.5rem;
  background: var(--text-secondary);
  border-radius: 50%;
  animation: thinking 1.4s infinite ease-in-out;
}

.thinking-dots span:nth-child(1) { animation-delay: -0.32s; }
.thinking-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes thinking {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Tool Call Styling */
.tool-call strong {
  display: block;
  margin-bottom: 0.5rem;
}

.tool-call p {
  margin: 0;
  font-size: 0.9rem;
}

/* Markdown Styling in Messages */
.message-content h1,
.message-content h2,
.message-content h3 {
  margin: 0.5rem 0;
  color: inherit;
}

.message-content h1 { font-size: 1.2rem; }
.message-content h2 { font-size: 1.1rem; }
.message-content h3 { font-size: 1rem; }

.message-content ul {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.message-content li {
  margin: 0.25rem 0;
}

.message-content code {
  background: rgba(0, 0, 0, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.85em;
}

.message-content pre {
  background: rgba(0, 0, 0, 0.1);
  padding: 0.75rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 0.5rem 0;
}

.message-content pre code {
  background: none;
  padding: 0;
}

.message-content a {
  color: var(--accent-primary);
  text-decoration: underline;
}

.message-content strong {
  font-weight: 600;
}

.message-content em {
  font-style: italic;
}

/* Inline Chat Styles - Grid applied by Django template */
.chat-panel-inline {
  background: var(--bg-secondary);
  border-left: 2px solid var(--border-color);
  height: 100%;
}

.chat-header-inline {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-tertiary);
}

.chat-header-inline h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.chat-header-inline span {
  font-size: 0.8rem;
  color: var(--text-secondary);
  display: block;
  margin-top: 0.25rem;
}

.chat-messages-inline {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.chat-input-inline {
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  background: var(--bg-tertiary);
}

.input-container-inline {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
}

.chat-textarea-inline {
  flex: 1;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  padding: 0.75rem;
  color: var(--text-primary);
  font-family: inherit;
  font-size: 0.9rem;
  resize: none;
  min-height: 2.25rem;
  max-height: 5rem;
}

.send-btn-inline {
  background: var(--accent-primary);
  border: none;
  border-radius: 50%;
  width: 2.25rem;
  height: 2.25rem;
  color: white;
  cursor: pointer;
}

.message {
  display: flex;
  gap: 1rem;
}

.message-content {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 1.25rem;
  padding: 1.25rem;
  color: var(--text-primary);
}

/* Responsive Layout */
@media (max-width: 1024px) {
  .collab-main {
    flex-direction: column;
  }

  .recipe-section,
  .chat-section {
    flex: 1;
    min-height: 50vh;
  }
}
</style>
