<template>
  <section class="recipe-panel">
    <div class="panel-content">
      <!-- Loading State -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>Loading recipe...</p>
      </div>

      <!-- Recipe Content -->
      <div v-else-if="recipe" class="recipe-content">
        <!-- Quick Stats -->
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-value">{{ recipe.batch_size_gallons?.toFixed(1) || '0.0' }}</div>
            <div class="stat-label">Gallons</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ recipe.original_gravity?.toFixed(3) || '1.000' }}</div>
            <div class="stat-label">OG</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ recipe.estimated_final_gravity?.toFixed(3) || '1.000' }}</div>
            <div class="stat-label">FG</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ recipe.estimated_abv?.toFixed(1) || '0.0' }}%</div>
            <div class="stat-label">ABV</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ recipe.calculated_srm?.toFixed(1) || '0.0' }}</div>
            <div class="stat-label">SRM</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ recipe.total_ibus?.toFixed(1) || '0.0' }}</div>
            <div class="stat-label">IBU</div>
          </div>
        </div>

        <!-- Recipe Tabs -->
        <div class="recipe-tabs">
          <div class="tab-nav">
            <button
              class="tab-btn"
              :class="{ active: activeTab === 'ingredients' }"
              @click="activeTab = 'ingredients'"
            >
              Ingredients
            </button>
            <button
              class="tab-btn"
              :class="{ active: activeTab === 'technique' }"
              @click="activeTab = 'technique'"
            >
              Technique
            </button>
          </div>

          <!-- Ingredients Tab -->
          <div v-show="activeTab === 'ingredients'" class="tab-content">
            <!-- Fermentables Section -->
            <div class="recipe-section">
              <div class="section-header" @click="toggleSection('fermentables')">
                <div class="header-content">
                  <h2 class="section-title">Fermentables</h2>
                  <div class="section-summary" v-if="!expandedSections.fermentables">
                    {{ fermentablesSummary }}
                  </div>
                </div>
                <div class="collapse-icon" :class="{ expanded: expandedSections.fermentables }">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="6,9 12,15 18,9"></polyline>
                  </svg>
                </div>
              </div>
              <div class="section-content" v-show="expandedSections.fermentables">
                <div v-if="recipe.fermentable_inclusions?.length" class="ingredient-list">
                  <div
                    v-for="inclusion in recipe.fermentable_inclusions"
                    :key="inclusion.id"
                    class="ingredient-item"
                  >
                    <div class="ingredient-main">
                      <div class="ingredient-name">{{ inclusion.fermentable?.name || 'Unknown' }}</div>
                      <div class="ingredient-type">{{ inclusion.fermentable?.fermentable_type || '' }}</div>
                    </div>
                    <div class="ingredient-amount">
                      <div class="amount-primary">{{ inclusion.quantity }} {{ inclusion.quantity_unit }}</div>
                      <div class="amount-secondary">{{ inclusion.gravity_points_contribution?.toFixed(1) || '0.0' }} pts</div>
                    </div>
                  </div>
                </div>
                <div v-else class="empty-section">
                  <div class="empty-icon">🌾</div>
                  <div class="empty-text">Ask the AI to help build your fermentable bill</div>
                </div>
              </div>
            </div>

            <!-- Hops Section -->
            <div class="recipe-section">
              <div class="section-header" @click="toggleSection('hops')">
                <div class="header-content">
                  <h2 class="section-title">Hop Schedule</h2>
                  <div class="section-summary" v-if="!expandedSections.hops">
                    {{ hopsSummary }}
                  </div>
                </div>
                <div class="collapse-icon" :class="{ expanded: expandedSections.hops }">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="6,9 12,15 18,9"></polyline>
                  </svg>
                </div>
              </div>
              <div class="section-content" v-show="expandedSections.hops">
                <div v-if="hasHops" class="ingredient-list">
                  <!-- Boil Hops -->
                  <div
                    v-for="hop in recipe.boil_hop_inclusions"
                    :key="`boil-${hop.id}`"
                    class="ingredient-item"
                  >
                    <div class="ingredient-main">
                      <div class="ingredient-name">{{ hop.hop?.name || 'Unknown' }}</div>
                      <div class="ingredient-type">{{ hop.hop?.alpha_acid_percent?.toFixed(1) || '0.0' }}% AA</div>
                    </div>
                    <div class="ingredient-details">
                      <div class="detail-item">{{ hop.quantity }} {{ hop.quantity_unit }}</div>
                      <div class="detail-item">{{ hop.time_minutes }} min</div>
                      <div class="detail-item">Boil</div>
                    </div>
                  </div>

                  <!-- Dry Hops -->
                  <div
                    v-for="hop in recipe.dry_hop_inclusions"
                    :key="`dry-${hop.id}`"
                    class="ingredient-item"
                  >
                    <div class="ingredient-main">
                      <div class="ingredient-name">{{ hop.hop?.name || 'Unknown' }}</div>
                      <div class="ingredient-type">{{ hop.hop?.alpha_acid_percent?.toFixed(1) || '0.0' }}% AA</div>
                    </div>
                    <div class="ingredient-details">
                      <div class="detail-item">{{ hop.quantity }} {{ hop.quantity_unit }}</div>
                      <div class="detail-item">{{ hop.time_days }} days</div>
                      <div class="detail-item">Dry Hop</div>
                    </div>
                  </div>
                </div>
                <div v-else class="empty-section">
                  <div class="empty-icon">🌿</div>
                  <div class="empty-text">Ask the AI to design your hop schedule</div>
                </div>
              </div>
            </div>

            <!-- Yeast Section -->
            <div class="recipe-section">
              <div class="section-header" @click="toggleSection('yeast')">
                <div class="header-content">
                  <h2 class="section-title">Yeast</h2>
                  <div class="section-summary" v-if="!expandedSections.yeast">
                    {{ yeastSummary }}
                  </div>
                </div>
                <div class="collapse-icon" :class="{ expanded: expandedSections.yeast }">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="6,9 12,15 18,9"></polyline>
                  </svg>
                </div>
              </div>
              <div class="section-content" v-show="expandedSections.yeast">
                <div v-if="recipe.yeast_inclusions?.length" class="ingredient-list">
                  <div
                    v-for="yeast in recipe.yeast_inclusions"
                    :key="yeast.id"
                    class="ingredient-item"
                  >
                    <div class="ingredient-main">
                      <div class="ingredient-name">{{ yeast.yeast?.name || 'Unknown' }}</div>
                      <div class="ingredient-type">{{ yeast.yeast?.yeast_type || '' }}</div>
                    </div>
                    <div class="ingredient-details">
                      <div class="detail-item">{{ yeast.quantity }} {{ yeast.quantity_unit }}</div>
                      <div class="detail-item">{{ yeast.yeast?.attenuation_percent || '0' }}% attenuation</div>
                    </div>
                  </div>
                </div>
                <div v-else class="empty-section">
                  <div class="empty-icon">🦠</div>
                  <div class="empty-text">Ask the AI to help select yeast</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Technique Tab -->
          <div v-show="activeTab === 'technique'" class="tab-content">
            <!-- Mash Schedule Section -->
            <div class="recipe-section">
              <div class="section-header" @click="toggleSection('mash')">
                <div class="header-content">
                  <h2 class="section-title">Mash Schedule</h2>
                  <div class="section-summary" v-if="!expandedSections.mash">
                    {{ mashSummary }}
                  </div>
                </div>
                <div class="collapse-icon" :class="{ expanded: expandedSections.mash }">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="6,9 12,15 18,9"></polyline>
                  </svg>
                </div>
              </div>
              <div class="section-content" v-show="expandedSections.mash">
                <div v-if="recipe.mash_steps?.length" class="technique-list">
                  <div
                    v-for="step in recipe.mash_steps"
                    :key="step.id"
                    class="technique-item"
                  >
                    <div class="technique-main">
                      <div class="technique-name">{{ step.name }}</div>
                      <div class="technique-type">{{ step.step_type }}</div>
                    </div>
                    <div class="technique-details">
                      <div class="detail-item temp">{{ step.temperature_fahrenheit }}°F</div>
                      <div class="detail-item duration">{{ step.duration_display }}</div>
                      <div class="detail-item order">Step {{ step.step_order }}</div>
                    </div>
                  </div>
                </div>
                <div v-else class="empty-section">
                  <div class="empty-icon">🌡️</div>
                  <div class="empty-text">Ask the AI to design your mash schedule</div>
                </div>
              </div>
            </div>

            <!-- Fermentation Schedule Section -->
            <div class="recipe-section">
              <div class="section-header" @click="toggleSection('fermentation')">
                <div class="header-content">
                  <h2 class="section-title">Fermentation Schedule</h2>
                  <div class="section-summary" v-if="!expandedSections.fermentation">
                    {{ fermentationSummary }}
                  </div>
                </div>
                <div class="collapse-icon" :class="{ expanded: expandedSections.fermentation }">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="6,9 12,15 18,9"></polyline>
                  </svg>
                </div>
              </div>
              <div class="section-content" v-show="expandedSections.fermentation">
                <div v-if="recipe.fermentation_phases?.length" class="technique-list">
                  <div
                    v-for="phase in recipe.fermentation_phases"
                    :key="phase.id"
                    class="technique-item"
                  >
                    <div class="technique-main">
                      <div class="technique-name">{{ phase.name }}</div>
                      <div class="technique-type">{{ phase.phase_type.replace('_', ' ') }}</div>
                    </div>
                    <div class="technique-details">
                      <div class="detail-item temp">{{ phase.temperature_display }}</div>
                      <div class="detail-item duration">{{ phase.duration_display }}</div>
                      <div class="detail-item order">Phase {{ phase.phase_order }}</div>
                    </div>
                  </div>
                </div>
                <div v-else class="empty-section">
                  <div class="empty-icon">🍺</div>
                  <div class="empty-text">Ask the AI to plan your fermentation schedule</div>
                </div>
              </div>
            </div>

            <!-- Water Profile Section -->
            <div class="recipe-section" v-if="recipe.water_profile">
              <div class="section-header" @click="toggleSection('water')">
                <div class="header-content">
                  <h2 class="section-title">Water Profile</h2>
                  <div class="section-summary" v-if="!expandedSections.water">
                    {{ waterSummary }}
                  </div>
                </div>
                <div class="collapse-icon" :class="{ expanded: expandedSections.water }">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="6,9 12,15 18,9"></polyline>
                  </svg>
                </div>
              </div>
              <div class="section-content" v-show="expandedSections.water">
                <div class="water-profile">
                  <div class="water-name">{{ recipe.water_profile.name }}</div>
                  <div class="water-minerals">
                    <div class="mineral-item">
                      <span class="mineral-name">Calcium</span>
                      <span class="mineral-value">{{ recipe.water_profile.calcium_ppm }} ppm</span>
                    </div>
                    <div class="mineral-item">
                      <span class="mineral-name">Magnesium</span>
                      <span class="mineral-value">{{ recipe.water_profile.magnesium_ppm }} ppm</span>
                    </div>
                    <div class="mineral-item">
                      <span class="mineral-name">Sodium</span>
                      <span class="mineral-value">{{ recipe.water_profile.sodium_ppm }} ppm</span>
                    </div>
                    <div class="mineral-item">
                      <span class="mineral-name">Sulfate</span>
                      <span class="mineral-value">{{ recipe.water_profile.sulfate_ppm }} ppm</span>
                    </div>
                    <div class="mineral-item">
                      <span class="mineral-name">Chloride</span>
                      <span class="mineral-value">{{ recipe.water_profile.chloride_ppm }} ppm</span>
                    </div>
                    <div class="mineral-item">
                      <span class="mineral-name">Bicarbonate</span>
                      <span class="mineral-value">{{ recipe.water_profile.bicarbonate_ppm }} ppm</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else class="error-state">
        <div class="error-icon">⚠️</div>
        <p>Failed to load recipe</p>
        <button @click="$emit('refresh')" class="retry-btn">Retry</button>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  recipe: Object,
  loading: Boolean
})

const emit = defineEmits(['refresh'])

const activeTab = ref('ingredients')

// Collapsible sections state
const expandedSections = ref({
  fermentables: true,  // Start with fermentables expanded
  hops: false,
  yeast: false,
  mash: false,
  fermentation: false,
  water: false
})

const toggleSection = (section) => {
  expandedSections.value[section] = !expandedSections.value[section]
}

const hasHops = computed(() => {
  return (props.recipe?.boil_hop_inclusions?.length || 0) +
         (props.recipe?.dry_hop_inclusions?.length || 0) > 0
})

// Summary computeds for collapsed sections
const fermentablesSummary = computed(() => {
  const count = props.recipe?.fermentable_inclusions?.length || 0
  if (count === 0) return 'No fermentables'

  const totalLbs = props.recipe?.fermentable_inclusions?.reduce((sum, f) => {
    let quantity = f.quantity || 0

    // Convert to pounds based on unit
    if (f.quantity_unit === 'lbs') {
      return sum + quantity
    } else if (f.quantity_unit === 'oz') {
      return sum + (quantity / 16) // 16 oz = 1 lb
    } else if (f.quantity_unit === 'kg') {
      return sum + (quantity * 2.20462) // kg to lbs
    } else if (f.quantity_unit === 'g') {
      return sum + (quantity / 453.592) // grams to lbs
    } else {
      // Default to treating as lbs if unknown unit
      return sum + quantity
    }
  }, 0) || 0

  return `${count} ingredients • ${totalLbs.toFixed(1)} lbs total`
})

const hopsSummary = computed(() => {
  const boilCount = props.recipe?.boil_hop_inclusions?.length || 0
  const dryCount = props.recipe?.dry_hop_inclusions?.length || 0
  const total = boilCount + dryCount
  if (total === 0) return 'No hops'

  const totalOz = [...(props.recipe?.boil_hop_inclusions || []), ...(props.recipe?.dry_hop_inclusions || [])]
    .reduce((sum, h) => {
      let quantity = h.quantity || 0

      // Convert to ounces based on unit
      if (h.quantity_unit === 'oz') {
        return sum + quantity
      } else if (h.quantity_unit === 'g') {
        return sum + (quantity / 28.3495) // grams to oz
      } else if (h.quantity_unit === 'lbs') {
        return sum + (quantity * 16) // lbs to oz
      } else {
        // Default to treating as oz if unknown unit
        return sum + quantity
      }
    }, 0)

  return `${total} additions • ${totalOz.toFixed(1)} oz total`
})

const yeastSummary = computed(() => {
  const count = props.recipe?.yeast_inclusions?.length || 0
  if (count === 0) return 'No yeast'
  const yeast = props.recipe?.yeast_inclusions?.[0]
  return `${yeast?.yeast?.name || 'Unknown'} • ${yeast?.yeast?.attenuation_percent || '0'}% attenuation`
})

const mashSummary = computed(() => {
  const count = props.recipe?.mash_steps?.length || 0
  if (count === 0) return 'No mash steps'
  const mainStep = props.recipe?.mash_steps?.find(s => s.step_type === 'saccharification') || props.recipe?.mash_steps?.[0]
  return `${count} steps • ${mainStep?.temperature_fahrenheit || '0'}°F main`
})

const fermentationSummary = computed(() => {
  const count = props.recipe?.fermentation_phases?.length || 0
  if (count === 0) return 'No fermentation phases'
  const primary = props.recipe?.fermentation_phases?.find(p => p.phase_type === 'primary') || props.recipe?.fermentation_phases?.[0]
  return `${count} phases • ${primary?.temperature_display || 'Unknown temp'}`
})

const waterSummary = computed(() => {
  if (!props.recipe?.water_profile) return 'No water profile'
  return `${props.recipe.water_profile.name} • ${props.recipe.water_profile.calcium_ppm || 0} Ca, ${props.recipe.water_profile.sulfate_ppm || 0} SO₄`
})
</script>

<style scoped>
.recipe-panel {
  background: var(--bg-primary);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  scroll-behavior: smooth;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: all 0.2s ease;
}

.stat-card:hover {
  border-color: var(--accent-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--accent-primary);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Recipe Tabs */
.recipe-tabs {
  margin-top: 1rem;
}

.tab-nav {
  display: flex;
  background: var(--bg-tertiary);
  border-radius: 0.75rem;
  padding: 0.25rem;
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color);
}

.tab-btn {
  flex: 1;
  padding: 0.75rem 1.5rem;
  background: transparent;
  border: none;
  border-radius: 0.5rem;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-btn.active {
  background: var(--accent-primary);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-btn:hover:not(.active) {
  background: var(--bg-primary);
  color: var(--text-primary);
}

/* Recipe Sections */
.recipe-section {
  margin-bottom: 2rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.2s ease;
}

.recipe-section:hover {
  border-color: var(--accent-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.section-header:hover {
  background: var(--bg-secondary);
}

.header-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.section-summary {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.collapse-icon {
  transition: transform 0.2s ease;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapse-icon.expanded {
  transform: rotate(180deg);
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.section-content {
  padding: 1rem;
}

/* Ingredient Lists - Condensed */
.ingredient-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.ingredient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.ingredient-item:hover {
  border-color: var(--accent-primary);
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ingredient-main {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.ingredient-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.ingredient-type {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.ingredient-amount {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.amount-primary {
  font-weight: 600;
  color: var(--accent-primary);
  font-size: 0.85rem;
}

.amount-secondary {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.ingredient-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.detail-item {
  font-size: 0.85rem;
  color: var(--text-secondary);
  padding: 0.25rem 0.5rem;
  background: var(--bg-secondary);
  border-radius: 0.375rem;
}

/* Empty States */
.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 3rem 2rem;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  opacity: 0.5;
}

.empty-text {
  color: var(--text-secondary);
  font-size: 0.95rem;
  max-width: 300px;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 1rem;
}

.error-icon {
  font-size: 3rem;
  opacity: 0.5;
}

.retry-btn {
  padding: 0.5rem 1rem;
  background: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-btn:hover {
  background: var(--accent-secondary);
}

/* Technique Tab Styles */
.technique-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.technique-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  transition: all 0.2s ease;
}

.technique-item:hover {
  border-color: var(--accent-primary);
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.technique-main {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.technique-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.95rem;
}

.technique-type {
  font-size: 0.8rem;
  color: var(--text-secondary);
  text-transform: capitalize;
}

.technique-details {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.technique-details .detail-item {
  font-size: 0.85rem;
  color: var(--text-secondary);
  padding: 0.25rem 0.5rem;
  background: var(--bg-secondary);
  border-radius: 0.375rem;
  font-weight: 500;
}

.technique-details .detail-item.temp {
  color: var(--accent-primary);
  background: rgba(74, 158, 255, 0.1);
}

.technique-details .detail-item.duration {
  color: var(--text-primary);
}

.technique-details .detail-item.order {
  color: var(--text-muted);
  font-size: 0.75rem;
}

/* Water Profile Styles */
.water-profile {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  padding: 1.25rem;
}

.water-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1.1rem;
  margin-bottom: 1rem;
}

.water-minerals {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
}

.mineral-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: var(--bg-secondary);
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
}

.mineral-name {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.mineral-value {
  font-size: 0.85rem;
  color: var(--text-primary);
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .panel-content {
    padding: 1rem;
  }

  .section-content {
    padding: 1rem;
  }

  .technique-details {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-end;
  }

  .water-minerals {
    grid-template-columns: 1fr;
  }
}
</style>
