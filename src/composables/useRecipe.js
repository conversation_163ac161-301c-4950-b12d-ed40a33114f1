import { ref, reactive } from 'vue'
import axios from 'axios'

export function useRecipe(recipeId) {
  const recipe = ref(null)
  const loading = ref(false)
  const error = ref(null)

  const fetchRecipe = async () => {
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.get(`/api/recipe/${recipeId}/`)
      recipe.value = response.data
    } catch (err) {
      error.value = err.message
      console.error('Error fetching recipe:', err)
    } finally {
      loading.value = false
    }
  }

  const refreshRecipe = async () => {
    try {
      const response = await axios.get(`/api/recipe/${recipeId}/refresh/`)
      recipe.value = response.data
    } catch (err) {
      error.value = err.message
      console.error('Error refreshing recipe:', err)
    }
  }

  return {
    recipe,
    loading,
    error,
    fetchRecipe,
    refreshRecipe
  }
}
