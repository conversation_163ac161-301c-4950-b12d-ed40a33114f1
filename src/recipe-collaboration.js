import { createApp } from 'vue'
import RecipeCollaborationApp from './components/RecipeCollaborationApp.vue'

// Get recipe ID from the DOM
const recipeId = document.getElementById('recipe-collaboration-app').dataset.recipeId
const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value

// Create and mount the Vue app
const app = createApp(RecipeCollaborationApp, {
  recipeId,
  csrfToken
})

app.mount('#recipe-collaboration-app')
