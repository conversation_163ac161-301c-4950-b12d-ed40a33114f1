/* Hoplogic Dark Theme Base Styles */

:root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3a3a3a;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #808080;
    --accent-primary: #4a9eff;
    --accent-secondary: #6b73ff;
    --accent-success: #4caf50;
    --accent-warning: #ff9800;
    --accent-danger: #f44336;
    --border-color: #404040;
    --shadow: rgba(0, 0, 0, 0.3);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

/* Header Styles */
.header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    box-shadow: 0 2px 4px var(--shadow);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--accent-primary);
    text-decoration: none;
    transition: var(--transition);
}

.logo:hover {
    color: var(--accent-secondary);
}

.nav-links {
    display: flex;
    gap: 2rem;
    list-style: none;
}

.nav-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--text-primary);
    background-color: var(--bg-tertiary);
}

/* Main Content */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
}

.btn-primary {
    background-color: var(--accent-primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--accent-secondary);
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--bg-secondary);
    border-color: var(--accent-primary);
}

.btn-success {
    background-color: var(--accent-success);
    color: white;
}

.btn-success:hover {
    background-color: #45a049;
}

/* Card Styles */
.card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px var(--shadow);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow);
}

.card-header {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.card-subtitle {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.card-body {
    color: var(--text-secondary);
}

.card-footer {
    border-top: 1px solid var(--border-color);
    padding-top: 1rem;
    margin-top: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Grid Layouts */
.grid {
    display: grid;
    gap: 1.5rem;
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Form Styles */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition);
}

.form-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(74, 158, 255, 0.2);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-muted { color: var(--text-muted); }
.text-primary { color: var(--accent-primary); }
.text-success { color: var(--accent-success); }
.text-warning { color: var(--accent-warning); }
.text-danger { color: var(--accent-danger); }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }

.d-flex { display: flex; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }
.gap-1 { gap: 0.5rem; }
.gap-2 { gap: 1rem; }
.gap-3 { gap: 1.5rem; }

.w-full { width: 100%; }
.h-full { height: 100%; }

/* Chat Interface Styles */
.chat-panel {
    flex: 0 0 40%;
    width: 40%;
    background: var(--bg-secondary);
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    height: 100vh;
    min-width: 400px;
    border-radius: 0; /* No rounding for chat panel */
}

/* Chat container styling handled in template */

/* Chat header styling handled in template */

.chat-header h4 {
    color: var(--text-primary);
    margin: 0;
    font-size: 1rem;
}

.ai-avatar {
    font-size: 1.5rem;
    margin-right: 0.5rem;
}

.status-indicator {
    color: var(--accent-success);
    font-size: 0.875rem;
}

/* Chat messages styling handled in template */

.message {
    display: flex;
    gap: 0.5rem;
    max-width: 95%;
    margin-bottom: 0.5rem;
}

.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.ai-message {
    align-self: flex-start;
}

.thinking-message {
    opacity: 0.8;
}

.message-avatar {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    background: var(--accent-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    flex-shrink: 0;
    margin-top: 0.25rem;
}

.user-message .message-avatar {
    background: var(--accent-secondary);
}

.message-content {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
    flex: 1;
}

.message-bubble {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0.75rem;
    position: relative;
    color: var(--text-primary);
    line-height: 1.5;
}

.user-message .message-bubble {
    background: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.message-bubble.thinking {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    border-style: dashed;
    color: var(--text-secondary);
}

.message-bubble.error {
    background: rgba(244, 67, 54, 0.1);
    border-color: var(--accent-danger);
    color: var(--accent-danger);
}

.thinking-dots {
    display: inline-flex;
    gap: 0.25rem;
    margin-left: 0.5rem;
}

.thinking-dots span {
    width: 4px;
    height: 4px;
    background: var(--text-muted);
    border-radius: 50%;
    animation: thinking 1.4s infinite ease-in-out;
}

.thinking-dots span:nth-child(1) { animation-delay: -0.32s; }
.thinking-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes thinking {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.message-bubble p {
    margin: 0 0 0.5rem 0;
    line-height: 1.5;
}

.message-bubble p:last-child {
    margin-bottom: 0;
}

.message-bubble h1,
.message-bubble h2,
.message-bubble h3,
.message-bubble h4,
.message-bubble h5,
.message-bubble h6 {
    margin: 0.75rem 0 0.5rem 0;
    font-weight: 600;
    color: inherit;
}

.message-bubble h1:first-child,
.message-bubble h2:first-child,
.message-bubble h3:first-child,
.message-bubble h4:first-child,
.message-bubble h5:first-child,
.message-bubble h6:first-child {
    margin-top: 0;
}

.message-bubble ul,
.message-bubble ol {
    margin: 0.5rem 0;
    padding-left: 1.25rem;
}

.message-bubble li {
    margin-bottom: 0.25rem;
    line-height: 1.4;
}

.message-bubble code {
    background: rgba(0, 0, 0, 0.1);
    padding: 0.125rem 0.25rem;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85em;
}

.user-message .message-bubble code {
    background: rgba(255, 255, 255, 0.2);
}

.message-bubble pre {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.75rem;
    margin: 0.5rem 0;
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85em;
    line-height: 1.4;
}

.user-message .message-bubble pre {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.message-bubble pre code {
    background: none;
    padding: 0;
    border-radius: 0;
}

.message-bubble blockquote {
    border-left: 3px solid var(--accent-primary);
    margin: 0.5rem 0;
    padding-left: 0.75rem;
    font-style: italic;
    opacity: 0.8;
}

.message-bubble strong {
    font-weight: 600;
}

.message-bubble em {
    font-style: italic;
}

.message-bubble a {
    color: var(--accent-primary);
    text-decoration: underline;
}

.user-message .message-bubble a {
    color: rgba(255, 255, 255, 0.9);
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-align: right;
}

.user-message .message-time {
    text-align: left;
}

/* Chat input styling handled in template */

.chat-input-container {
    display: flex;
    gap: 0.5rem;
    align-items: flex-end;
}

.chat-textarea {
    flex: 1;
    min-height: 40px;
    max-height: 120px;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    resize: none;
    font-family: inherit;
    font-size: 0.875rem;
    line-height: 1.4;
}

.chat-textarea:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(74, 158, 255, 0.1);
}

.chat-textarea:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.send-btn {
    padding: 0.75rem 1rem;
    background: var(--accent-primary);
    color: white;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
}

.send-btn:hover {
    background: var(--accent-secondary);
}

.send-btn:disabled {
    background: var(--border-color);
    cursor: not-allowed;
}

/* Recipe Panel Styles */
.recipe-panel {
    flex: 1;
    width: 60%;
    padding: 0.75rem;
    overflow-y: auto;
    height: 100%;
    background: var(--bg-primary);
}

.collaboration-layout {
    display: flex;
    height: 100vh;
    width: 100%;
    margin: 0;
    padding: 0;
}

/* Reduce card spacing in recipe panel */
.recipe-panel .card {
    margin-bottom: 0.75rem !important;
}

.recipe-panel .card-header {
    padding: 0.5rem 0.75rem;
}

.recipe-panel .card-body {
    padding: 0.75rem;
}

.recipe-panel .card-title {
    margin: 0;
    font-size: 1.1rem;
}

/* Reduce spacing on recipe content */
.recipe-content h1,
.recipe-content h2,
.recipe-content h3,
.recipe-content h4 {
    margin-top: 0;
    margin-bottom: 0.5rem;
}

.recipe-content .section {
    margin-bottom: 0.75rem;
}

/* Tighter spacing for ingredients list */
.ingredients-list {
    margin: 0;
}

.ingredients-list .ingredient-item:last-child {
    margin-bottom: 0;
}

/* Recipe Content Styles */
.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.overview-item {
    display: flex;
    flex-direction: column;
    text-align: center;
    padding: 0.5rem;
    background-color: var(--bg-tertiary);
    border-radius: var(--border-radius);
}

.overview-item .label {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: 0.25rem;
}

.overview-item .value {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.ingredient-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background-color: var(--bg-tertiary);
    border-radius: var(--border-radius);
    margin-bottom: 0.375rem;
}

.ingredient-info {
    display: flex;
    flex-direction: column;
}

.ingredient-name {
    font-weight: 500;
    color: var(--text-primary);
}

.ingredient-type {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.ingredient-amount {
    text-align: right;
}

.amount {
    font-weight: 500;
    color: var(--text-primary);
}

.percentage {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.empty-section {
    text-align: center;
    padding: 1.5rem;
    background-color: var(--bg-tertiary);
    border-radius: var(--border-radius);
    border: 2px dashed var(--border-color);
    color: var(--text-muted);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-links {
        gap: 1rem;
    }

    .main-content {
        padding: 1rem;
    }

    .grid-2,
    .grid-3 {
        grid-template-columns: 1fr;
    }

    .collaboration-layout {
        flex-direction: column;
    }

    .recipe-panel,
    .chat-panel {
        width: 100%;
        height: 50vh;
    }
}
