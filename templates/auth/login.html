{% extends 'base.html' %}

{% block title %}{{ page_title }} - Hoplogic{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card card">
        <div class="card-header text-center">
            <h1 class="text-primary mb-2">🍺 Welcome Back</h1>
            <p class="text-secondary">Sign in to continue brewing</p>
        </div>

        <div class="card-body">
            <form method="post" class="auth-form">
                {% csrf_token %}

                {% if form.non_field_errors %}
                    <div class="alert alert-error mb-3">
                        {{ form.non_field_errors }}
                    </div>
                {% endif %}

                <div class="form-group mb-3">
                    <label for="{{ form.username.id_for_label }}" class="form-label">Email</label>
                    {{ form.username }}
                    {% if form.username.errors %}
                        <div class="form-error">{{ form.username.errors }}</div>
                    {% endif %}
                </div>

                <div class="form-group mb-4">
                    <label for="{{ form.password.id_for_label }}" class="form-label">Password</label>
                    {{ form.password }}
                    {% if form.password.errors %}
                        <div class="form-error">{{ form.password.errors }}</div>
                    {% endif %}
                </div>

                <button type="submit" class="btn btn-primary btn-full mb-3">
                    Sign In
                </button>

                <div class="text-center">
                    <p class="text-secondary">
                        Don't have an account?
                        <a href="{% url 'register' %}" class="text-primary">Register here</a>
                    </p>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
    padding: 2rem;
}

.auth-card {
    width: 100%;
    max-width: 400px;
}

.auth-form .form-input {
    width: 100%;
}

.btn-full {
    width: 100%;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-greeting {
    color: var(--text-secondary);
    font-size: 0.9rem;
}
</style>
{% endblock %}
