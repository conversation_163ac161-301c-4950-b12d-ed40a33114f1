{% extends 'base.html' %}

{% block title %}{{ page_title }} - Hoplogic{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card card">
        <div class="card-header text-center">
            <h1 class="text-primary mb-2">🍺 Join <PERSON>logic</h1>
            <p class="text-secondary">Create your account to start brewing</p>
        </div>

        <div class="card-body">
            <form method="post" class="auth-form">
                {% csrf_token %}

                {% if form.non_field_errors %}
                    <div class="alert alert-error mb-3">
                        {{ form.non_field_errors }}
                    </div>
                {% endif %}

                <div class="form-group mb-3">
                    <label for="{{ form.email.id_for_label }}" class="form-label">Email</label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <div class="form-error">{{ form.email.errors }}</div>
                    {% endif %}
                </div>

                <div class="form-row mb-3">
                    <div class="form-group">
                        <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name</label>
                        {{ form.first_name }}
                        {% if form.first_name.errors %}
                            <div class="form-error">{{ form.first_name.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name</label>
                        {{ form.last_name }}
                        {% if form.last_name.errors %}
                            <div class="form-error">{{ form.last_name.errors }}</div>
                        {% endif %}
                    </div>
                </div>

                <div class="form-group mb-4">
                    <label for="{{ form.password.id_for_label }}" class="form-label">Password</label>
                    {{ form.password }}
                    {% if form.password.errors %}
                        <div class="form-error">{{ form.password.errors }}</div>
                    {% endif %}
                </div>

                <button type="submit" class="btn btn-primary btn-full mb-3">
                    Create Account
                </button>

                <div class="text-center">
                    <p class="text-secondary">
                        Already have an account?
                        <a href="{% url 'login' %}" class="text-primary">Sign in here</a>
                    </p>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-row .form-group {
    margin-bottom: 0;
}
</style>
{% endblock %}
