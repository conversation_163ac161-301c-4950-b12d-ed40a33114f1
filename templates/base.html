<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Hoplogic{% endblock %}</title>
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/base.css' %}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <header class="header">
        <div class="header-content">
            <a href="{% url 'home' %}" class="logo">🍺 Hoplogic</a>
            <nav>
                <ul class="nav-links">
                    {% if user.is_authenticated %}
                        <li><a href="{% url 'home' %}" class="{% if request.resolver_match.url_name == 'home' %}active{% endif %}">Home</a></li>
                        <li><a href="#" class="text-muted">Recipes</a></li>
                        <li class="user-menu">
                            <span class="user-greeting">Hello, {{ user.short_name }}!</span>
                            <a href="{% url 'logout' %}" class="btn btn-secondary btn-sm">Logout</a>
                        </li>
                    {% else %}
                        <li><a href="{% url 'login' %}" class="btn btn-primary">Login</a></li>
                        <li><a href="{% url 'register' %}" class="btn btn-secondary">Register</a></li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        {% if messages %}
            <div class="messages mb-3">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        {% block content %}{% endblock %}
    </main>

    {% block extra_js %}{% endblock %}
</body>
</html>
