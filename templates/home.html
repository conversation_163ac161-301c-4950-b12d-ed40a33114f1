{% extends 'base.html' %}

{% block title %}{{ page_title }} - Hoplogic{% endblock %}

{% block content %}
<div class="home-container">
    <!-- Header Section -->
    <div class="home-header mb-4">
        <div class="d-flex justify-between align-center">
            <div>
                <h1 class="text-primary mb-2">🍺 {{ page_title }}</h1>
                <p class="text-secondary">Craft your perfect brew with AI-powered recipe development</p>
            </div>
            <div>
                <form method="post" action="{% url 'create_recipe' %}" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-primary">
                        <span>✨</span> Create New Recipe
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Stats Section -->
    <div class="stats-grid grid grid-3 mb-4">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ recipes|length }}</h3>
                <p class="text-secondary">Total Recipes</p>
            </div>
        </div>
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success">{{ recipes|length }}</h3>
                <p class="text-secondary">Active Recipes</p>
            </div>
        </div>
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning">0</h3>
                <p class="text-secondary">In Progress</p>
            </div>
        </div>
    </div>

    <!-- Recipes Section -->
    <div class="recipes-section">
        <div class="d-flex justify-between align-center mb-3">
            <h2>Your Recipes</h2>
            <div class="recipe-filters">
                <select class="form-input" style="width: auto;">
                    <option>All Styles</option>
                    <option>IPA</option>
                    <option>Stout</option>
                    <option>Lager</option>
                    <option>Wheat</option>
                </select>
            </div>
        </div>

        {% if recipes %}
            <div class="recipes-grid grid grid-2">
                {% for recipe in recipes %}
                    <div class="recipe-card card">
                        <div class="card-header">
                            <div class="d-flex justify-between align-center">
                                <h3 class="card-title">{{ recipe.name }}</h3>
                                <span class="recipe-status text-success">Active</span>
                            </div>
                            <p class="card-subtitle">Created {{ recipe.created_at|date:"M d, Y" }}</p>
                        </div>

                        <div class="card-body">
                            <p class="recipe-description text-secondary mb-3">
                                {% if recipe.description %}
                                    {{ recipe.description|truncatewords:20 }}
                                {% else %}
                                    No description available
                                {% endif %}
                            </p>

                            <div class="recipe-stats">
                                <div class="stats-row d-flex gap-3 mb-2">
                                    <div class="stat">
                                        <span class="stat-label text-muted">OG:</span>
                                        <span class="stat-value">{{ recipe.original_gravity|floatformat:3 }}</span>
                                    </div>
                                    <div class="stat">
                                        <span class="stat-label text-muted">SRM:</span>
                                        <span class="stat-value">{{ recipe.calculated_srm|floatformat:1 }}</span>
                                    </div>
                                    <div class="stat">
                                        <span class="stat-label text-muted">IBU:</span>
                                        <span class="stat-value">{{ recipe.total_ibus|floatformat:1 }}</span>
                                    </div>
                                </div>
                                <div class="stats-row d-flex gap-3">
                                    <div class="stat">
                                        <span class="stat-label text-muted">Batch:</span>
                                        <span class="stat-value">{{ recipe.batch_size_gallons|floatformat:1 }} gal</span>
                                    </div>
                                    <div class="stat">
                                        <span class="stat-label text-muted">Efficiency:</span>
                                        <span class="stat-value">{{ recipe.mash_efficiency_percent|floatformat:0 }}%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="recipe-actions d-flex gap-2">
                                <a href="{% url 'recipe_collaboration' recipe.pk %}" class="btn btn-primary">
                                    💬 Collaborate
                                </a>
                                <a href="#" class="btn btn-secondary">
                                    📋 View Details
                                </a>
                                <a href="#" class="btn btn-secondary">
                                    📄 Export
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- Empty State -->
            <div class="empty-state text-center p-4">
                <div class="empty-icon mb-3">
                    <span style="font-size: 4rem;">🍺</span>
                </div>
                <h3 class="mb-2">No Recipes Yet</h3>
                <p class="text-secondary mb-4">Start your brewing journey by creating your first recipe with our AI assistant.</p>
                <form method="post" action="{% url 'create_recipe' %}" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-primary">
                        <span>✨</span> Create Your First Recipe
                    </button>
                </form>
            </div>
        {% endif %}
    </div>
</div>

<style>
.home-container {
    max-width: 1200px;
    margin: 0 auto;
}

.home-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
}

.stats-grid .card {
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
}

.recipe-card {
    transition: var(--transition);
}

.recipe-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px var(--shadow);
}

.recipe-status {
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    background-color: rgba(76, 175, 80, 0.1);
}

.recipe-stats {
    background-color: var(--bg-tertiary);
    padding: 1rem;
    border-radius: var(--border-radius);
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-label {
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}

.stat-value {
    font-weight: 600;
    color: var(--text-primary);
}

.recipe-actions {
    flex-wrap: wrap;
}

.recipe-actions .btn {
    flex: 1;
    min-width: 120px;
    font-size: 0.9rem;
}

.empty-state {
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 2px dashed var(--border-color);
}

@media (max-width: 768px) {
    .home-header .d-flex {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .stats-row {
        flex-direction: column;
        gap: 0.5rem !important;
    }

    .recipe-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}
