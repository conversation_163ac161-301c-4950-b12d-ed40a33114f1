{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Hoplogic{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'js/dist/recipe-collaboration.css' %}">
<style>
/* Override base.css for full-height Vue app */
body {
    margin: 0 !important;
    padding: 0 !important;
    height: 100vh !important;
    overflow: hidden !important;
}

.main-content {
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100vw !important;
    height: calc(100vh - 80px) !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
    display: flex !important;
    flex-direction: column !important;
}

.header {
    padding: 0.75rem 1rem !important;
    height: 80px !important;
    box-sizing: border-box !important;
    flex-shrink: 0 !important;
}

.header-content {
    max-width: none !important;
    margin: 0 !important;
    padding: 0 0.5rem !important;
}

/* FORCE EXACT LAYOUT WITH VIEWPORT UNITS */
#recipe-collaboration-app {
    height: calc(100vh - 80px) !important;
    width: 100vw !important;
    display: flex !important;
    flex-direction: column !important;
}

.collab-main {
    flex: 1 !important;
    display: flex !important;
    height: 100% !important;
}

.recipe-section {
    flex: 0 0 60% !important;
    height: 100% !important;
}

.chat-section {
    flex: 0 0 40% !important;
    height: 100% !important;
}

/* FORCE CHAT PANEL TO EXACT HEIGHT */
.chat-section > div {
    height: calc(100vh - 80px) !important;
    max-height: calc(100vh - 80px) !important;
    overflow: hidden !important;
}
</style>
{% endblock %}

{% block content %}
{% csrf_token %}
<!-- Vue.js App Container -->
<div id="recipe-collaboration-app" data-recipe-id="{{ recipe.id }}"></div>
{% endblock %}

{% block extra_js %}
<script type="module" src="{% static 'js/dist/recipe-collaboration.js' %}"></script>
{% endblock %}
