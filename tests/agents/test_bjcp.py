"""
Tests for BJCP style retriever tool.
"""

from unittest.mock import Mock, patch

from langchain_core.tools import BaseTool

from core.agents.tools.bjcp import create_bjcp_retriever_tool


class TestCreateBJCPRetrieverTool:
    """Test the create_bjcp_retriever_tool function."""

    @patch("core.agents.tools.bjcp.provide_vector_store")
    def test_create_bjcp_retriever_tool_returns_tool(self, mock_provide_vector_store):
        """Test that create_bjcp_retriever_tool returns a tool."""
        mock_vector_store = Mock()
        mock_retriever = Mock()
        mock_vector_store.as_retriever.return_value = mock_retriever
        mock_provide_vector_store.return_value = mock_vector_store

        tool = create_bjcp_retriever_tool()

        assert isinstance(tool, BaseTool)
        assert hasattr(tool, 'name')
        assert hasattr(tool, 'description')

    @patch("core.agents.tools.bjcp.provide_vector_store")
    def test_bjcp_tool_name(self, mock_provide_vector_store):
        """Test that the BJCP tool has the correct name."""
        mock_vector_store = Mock()
        mock_retriever = Mock()
        mock_vector_store.as_retriever.return_value = mock_retriever
        mock_provide_vector_store.return_value = mock_vector_store

        tool = create_bjcp_retriever_tool()

        assert tool.name == "search_bjcp_guidelines"

    @patch("core.agents.tools.bjcp.provide_vector_store")
    def test_bjcp_tool_description(self, mock_provide_vector_store):
        """Test that the BJCP tool has a meaningful description."""
        mock_vector_store = Mock()
        mock_retriever = Mock()
        mock_vector_store.as_retriever.return_value = mock_retriever
        mock_provide_vector_store.return_value = mock_vector_store

        tool = create_bjcp_retriever_tool()

        assert "BJCP" in tool.description
        assert "beer styles" in tool.description.lower()
        assert len(tool.description) > 20  # Should be descriptive

    @patch("core.agents.tools.bjcp.provide_vector_store")
    @patch("core.agents.tools.bjcp.EmbeddingConfiguration.from_settings")
    def test_bjcp_tool_uses_default_config(self, mock_from_settings, mock_provide_vector_store):
        """Test that the tool uses default embedding configuration when none provided."""
        mock_config = Mock()
        mock_from_settings.return_value = mock_config
        mock_vector_store = Mock()
        mock_retriever = Mock()
        mock_vector_store.as_retriever.return_value = mock_retriever
        mock_provide_vector_store.return_value = mock_vector_store

        tool = create_bjcp_retriever_tool()

        mock_from_settings.assert_called_once()
        mock_provide_vector_store.assert_called_once()

    @patch("core.agents.tools.bjcp.provide_vector_store")
    def test_bjcp_tool_uses_custom_config(self, mock_provide_vector_store):
        """Test that the tool uses provided embedding configuration."""
        from core.agents.embedding import EmbeddingConfiguration, EmbeddingVendor

        custom_config = EmbeddingConfiguration(
            model_provider=EmbeddingVendor.OLLAMA,
            model_name="nomic-embed-text",
            base_url="http://localhost:11434",
            api_key="",
        )

        mock_vector_store = Mock()
        mock_retriever = Mock()
        mock_vector_store.as_retriever.return_value = mock_retriever
        mock_provide_vector_store.return_value = mock_vector_store

        tool = create_bjcp_retriever_tool(custom_config)

        # Should have called provide_vector_store with the custom config
        mock_provide_vector_store.assert_called_once()
        call_args = mock_provide_vector_store.call_args[0]
        assert call_args[0] == custom_config

    @patch("core.agents.tools.bjcp.provide_vector_store")
    def test_bjcp_tool_creates_retriever(self, mock_provide_vector_store):
        """Test that the tool creates a retriever from the vector store."""
        mock_vector_store = Mock()
        mock_retriever = Mock()
        mock_vector_store.as_retriever.return_value = mock_retriever
        mock_provide_vector_store.return_value = mock_vector_store

        tool = create_bjcp_retriever_tool()

        mock_vector_store.as_retriever.assert_called_once()

    @patch("core.agents.tools.bjcp._hydrate_bjcp")
    @patch("core.agents.tools.bjcp.provide_vector_store")
    def test_bjcp_tool_uses_hydrate_function(self, mock_provide_vector_store, mock_hydrate):
        """Test that the tool uses the BJCP hydration function."""
        mock_vector_store = Mock()
        mock_retriever = Mock()
        mock_vector_store.as_retriever.return_value = mock_retriever
        mock_provide_vector_store.return_value = mock_vector_store

        tool = create_bjcp_retriever_tool()

        # Should have called provide_vector_store with the hydrate function
        mock_provide_vector_store.assert_called_once()
        call_args = mock_provide_vector_store.call_args[0]
        # The second argument should be the hydrate function
        assert callable(call_args[1])

    @patch("core.agents.tools.bjcp.provide_vector_store")
    def test_bjcp_tool_integration(self, mock_provide_vector_store):
        """Test basic integration of the BJCP tool creation."""
        mock_vector_store = Mock()
        mock_retriever = Mock()
        mock_vector_store.as_retriever.return_value = mock_retriever
        mock_provide_vector_store.return_value = mock_vector_store

        tool = create_bjcp_retriever_tool()

        # Should be a valid tool
        assert isinstance(tool, BaseTool)
        assert tool.name == "search_bjcp_guidelines"
        assert "BJCP" in tool.description
        assert "beer styles" in tool.description
