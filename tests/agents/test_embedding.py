"""
Tests for agent embedding system and vector store functionality.
"""

import pytest
from unittest.mock import patch, Mock
from os import environ

from langchain_core.documents import Document

from core.agents.embedding import (
    EmbeddingConfiguration,
    EmbeddingVendor,
    provide_vector_store,
)


class TestEmbeddingVendor:
    """Test the EmbeddingVendor enum."""

    def test_enum_values(self):
        """Test that enum has expected values."""
        assert EmbeddingVendor.OLLAMA.value == "ollama"
        assert EmbeddingVendor.OPENAI.value == "openai"

    def test_enum_membership(self):
        """Test enum membership checks."""
        assert EmbeddingVendor.OLLAMA in EmbeddingVendor
        assert EmbeddingVendor.OPENAI in EmbeddingVendor


class TestEmbeddingConfiguration:
    """Test the EmbeddingConfiguration dataclass."""

    def test_initialization(self):
        """Test basic initialization of EmbeddingConfiguration."""
        config = EmbeddingConfiguration(
            model_provider=EmbeddingVendor.OLLAMA,
            model_name="nomic-embed-text",
            base_url="http://localhost:11434",
            api_key="",
        )

        assert config.model_provider == EmbeddingVendor.OLLAMA
        assert config.model_name == "nomic-embed-text"
        assert config.base_url == "http://localhost:11434"
        assert config.api_key == ""

    def test_frozen_dataclass(self):
        """Test that EmbeddingConfiguration is frozen (immutable)."""
        config = EmbeddingConfiguration(
            model_provider=EmbeddingVendor.OPENAI,
            model_name="text-embedding-ada-002",
            base_url=None,
            api_key="test-key",
        )

        with pytest.raises(AttributeError):
            config.model_name = "new-model"

    @patch.dict(environ, {"OPENAI_API_KEY": "test-openai-key"})
    def test_from_settings_default(self):
        """Test creating configuration from default settings."""
        config = EmbeddingConfiguration.from_settings()

        assert config.model_provider == EmbeddingVendor.OPENAI
        assert config.base_url is None
        assert config.model_name == "text-embedding-ada-002"
        assert config.api_key == "test-openai-key"

    @patch.dict(environ, {}, clear=True)
    def test_from_settings_no_api_key(self):
        """Test creating configuration when no API key is set."""
        config = EmbeddingConfiguration.from_settings()

        assert config.api_key == ""

    @patch("core.agents.embedding.OllamaEmbeddings")
    def test_make_embeddings_ollama(self, mock_ollama_embeddings):
        """Test creating Ollama embeddings."""
        mock_embeddings = Mock()
        mock_ollama_embeddings.return_value = mock_embeddings

        config = EmbeddingConfiguration(
            model_provider=EmbeddingVendor.OLLAMA,
            model_name="nomic-embed-text",
            base_url="http://localhost:11434",
            api_key="",
        )

        result = config.make_embeddings()

        assert result == mock_embeddings
        mock_ollama_embeddings.assert_called_once_with(
            model="nomic-embed-text",
            base_url="http://localhost:11434"
        )

    @patch("core.agents.embedding.OpenAIEmbeddings")
    def test_make_embeddings_openai(self, mock_openai_embeddings):
        """Test creating OpenAI embeddings."""
        mock_embeddings = Mock()
        mock_openai_embeddings.return_value = mock_embeddings

        config = EmbeddingConfiguration(
            model_provider=EmbeddingVendor.OPENAI,
            model_name="text-embedding-ada-002",
            base_url=None,
            api_key="test-key",
        )

        result = config.make_embeddings()

        assert result == mock_embeddings
        mock_openai_embeddings.assert_called_once_with(
            model="text-embedding-ada-002",
            base_url=None,
            api_key="test-key",
        )

    @patch("core.agents.embedding.OpenAIEmbeddings")
    def test_make_embeddings_openai_with_base_url(self, mock_openai_embeddings):
        """Test creating OpenAI embeddings with custom base URL."""
        mock_embeddings = Mock()
        mock_openai_embeddings.return_value = mock_embeddings

        config = EmbeddingConfiguration(
            model_provider=EmbeddingVendor.OPENAI,
            model_name="text-embedding-3-small",
            base_url="https://custom-api.example.com",
            api_key="test-key",
        )

        result = config.make_embeddings()

        assert result == mock_embeddings
        mock_openai_embeddings.assert_called_once_with(
            model="text-embedding-3-small",
            base_url="https://custom-api.example.com",
            api_key="test-key",
        )

    def test_make_embeddings_unknown_provider(self):
        """Test that unknown provider raises ValueError."""
        config = EmbeddingConfiguration(
            model_provider="unknown",  # This will be a string, not enum
            model_name="test-model",
            base_url=None,
            api_key="test-key",
        )

        with pytest.raises(ValueError, match="Unknown model provider: unknown"):
            config.make_embeddings()

    def test_slots_optimization(self):
        """Test that the dataclass uses slots for memory optimization."""
        config = EmbeddingConfiguration(
            model_provider=EmbeddingVendor.OPENAI,
            model_name="text-embedding-ada-002",
            base_url=None,
            api_key="test-key",
        )

        # Should not be able to add arbitrary attributes due to slots
        # The exact exception type may vary, but it should prevent attribute assignment
        with pytest.raises((AttributeError, TypeError)):
            config.new_attribute = "test"


class TestProvideVectorStore:
    """Test the provide_vector_store function."""

    @patch("core.agents.embedding.InMemoryVectorStore")
    @patch("core.agents.embedding.EmbeddingConfiguration.make_embeddings")
    def test_provide_vector_store_creates_store(self, mock_make_embeddings, mock_vector_store_class):
        """Test that provide_vector_store creates an InMemoryVectorStore."""
        mock_embeddings = Mock()
        mock_vector_store = Mock()
        mock_vector_store.similarity_search.return_value = []  # Empty store
        mock_vector_store_class.return_value = mock_vector_store
        mock_make_embeddings.return_value = mock_embeddings

        config = EmbeddingConfiguration(
            model_provider=EmbeddingVendor.OPENAI,
            model_name="text-embedding-ada-002",
            base_url=None,
            api_key="test-key",
        )

        def mock_hydrate(embeddings):
            return [Document(page_content="test content")]

        result = provide_vector_store(config, mock_hydrate)

        assert result == mock_vector_store
        mock_vector_store_class.assert_called_once_with(mock_embeddings)

    @patch("core.agents.embedding.InMemoryVectorStore")
    @patch("core.agents.embedding.EmbeddingConfiguration.make_embeddings")
    def test_provide_vector_store_hydrates_empty_store(self, mock_make_embeddings, mock_vector_store_class):
        """Test that empty vector store gets hydrated with documents."""
        mock_embeddings = Mock()
        mock_vector_store = Mock()
        mock_vector_store.similarity_search.return_value = []  # Empty store
        mock_vector_store_class.return_value = mock_vector_store
        mock_make_embeddings.return_value = mock_embeddings

        config = EmbeddingConfiguration(
            model_provider=EmbeddingVendor.OPENAI,
            model_name="text-embedding-ada-002",
            base_url=None,
            api_key="test-key",
        )

        test_documents = [
            Document(page_content="Document 1"),
            Document(page_content="Document 2"),
        ]

        def mock_hydrate(embeddings):
            assert embeddings == mock_embeddings
            return test_documents

        result = provide_vector_store(config, mock_hydrate)

        mock_vector_store.add_documents.assert_called_once_with(test_documents)

    @patch("core.agents.embedding.InMemoryVectorStore")
    @patch("core.agents.embedding.EmbeddingConfiguration.make_embeddings")
    def test_provide_vector_store_skips_hydration_for_populated_store(self, mock_make_embeddings, mock_vector_store_class):
        """Test that populated vector store doesn't get re-hydrated."""
        mock_embeddings = Mock()
        mock_vector_store = Mock()
        # Non-empty store
        mock_vector_store.similarity_search.return_value = [Document(page_content="existing")]
        mock_vector_store_class.return_value = mock_vector_store
        mock_make_embeddings.return_value = mock_embeddings

        config = EmbeddingConfiguration(
            model_provider=EmbeddingVendor.OPENAI,
            model_name="text-embedding-ada-002",
            base_url=None,
            api_key="test-key",
        )

        def mock_hydrate(embeddings):
            # This should not be called
            raise AssertionError("Hydrate should not be called for populated store")

        result = provide_vector_store(config, mock_hydrate)

        # add_documents should not be called
        mock_vector_store.add_documents.assert_not_called()

    @patch("core.agents.embedding.InMemoryVectorStore")
    def test_provide_vector_store_with_none_config(self, mock_vector_store_class):
        """Test that None config uses default configuration."""
        mock_embeddings = Mock()
        mock_vector_store = Mock()
        mock_vector_store.similarity_search.return_value = []
        mock_vector_store_class.return_value = mock_vector_store

        def mock_hydrate(embeddings):
            return [Document(page_content="test")]

        with patch("core.agents.embedding.EmbeddingConfiguration.from_settings") as mock_from_settings:
            mock_config = Mock()
            mock_config.make_embeddings.return_value = mock_embeddings
            mock_from_settings.return_value = mock_config

            result = provide_vector_store(None, mock_hydrate)

        mock_from_settings.assert_called_once()

    @patch("core.agents.embedding.InMemoryVectorStore")
    @patch("core.agents.embedding.EmbeddingConfiguration.make_embeddings")
    def test_provide_vector_store_hydrate_function_called_with_embeddings(self, mock_make_embeddings, mock_vs_class):
        """Test that hydrate function receives the embeddings object."""
        config = EmbeddingConfiguration(
            model_provider=EmbeddingVendor.OPENAI,
            model_name="text-embedding-ada-002",
            base_url=None,
            api_key="test-key",
        )

        received_embeddings = None
        mock_embeddings = Mock()
        mock_make_embeddings.return_value = mock_embeddings

        def capture_embeddings_hydrate(embeddings):
            nonlocal received_embeddings
            received_embeddings = embeddings
            return [Document(page_content="test")]

        mock_vector_store = Mock()
        mock_vector_store.similarity_search.return_value = []
        mock_vs_class.return_value = mock_vector_store

        provide_vector_store(config, capture_embeddings_hydrate)

        assert received_embeddings == mock_embeddings
