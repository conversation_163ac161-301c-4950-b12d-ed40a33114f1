"""
Tests for agent event system and message parsing.
"""

import pytest
import json
from unittest.mock import Mock

from langchain_core.messages import ToolMessage, AIMessage

from core.agents.events import (
    ToolCall,
    ToolOutput,
    AgentEvent,
    ToolCallEvent,
    ToolOutputEvent,
    AIMessageEvent,
)


class TestToolCall:
    """Test the ToolCall dataclass."""

    def test_initialization(self):
        """Test basic initialization of ToolCall."""
        tool_call = ToolCall(
            id="call_123",
            tool_name="test_tool",
            args={"param1": "value1", "param2": 42}
        )

        assert tool_call.id == "call_123"
        assert tool_call.tool_name == "test_tool"
        assert tool_call.args == {"param1": "value1", "param2": 42}

    def test_from_call_data(self):
        """Test creating ToolCall from call data."""
        call_data = {
            "id": "call_456",
            "function": {
                "name": "search_recipes",
                "arguments": '{"query": "IPA", "limit": 10}'
            }
        }

        tool_call = ToolCall.from_call_data(call_data)

        assert tool_call.id == "call_456"
        assert tool_call.tool_name == "search_recipes"
        assert tool_call.args == {"query": "IPA", "limit": 10}

    def test_from_call_data_complex_args(self):
        """Test parsing complex JSON arguments."""
        call_data = {
            "id": "call_789",
            "function": {
                "name": "update_recipe",
                "arguments": '{"recipe_id": "rec_123", "updates": {"name": "New IPA", "abv": 6.5}, "notes": ["Updated", "Tested"]}'
            }
        }

        tool_call = ToolCall.from_call_data(call_data)

        assert tool_call.id == "call_789"
        assert tool_call.tool_name == "update_recipe"
        expected_args = {
            "recipe_id": "rec_123",
            "updates": {"name": "New IPA", "abv": 6.5},
            "notes": ["Updated", "Tested"]
        }
        assert tool_call.args == expected_args

    def test_from_call_data_invalid_json(self):
        """Test handling invalid JSON in arguments."""
        call_data = {
            "id": "call_invalid",
            "function": {
                "name": "test_tool",
                "arguments": '{"invalid": json}'
            }
        }

        with pytest.raises(json.JSONDecodeError):
            ToolCall.from_call_data(call_data)

    def test_frozen_dataclass(self):
        """Test that ToolCall is frozen (immutable)."""
        tool_call = ToolCall(
            id="call_123",
            tool_name="test_tool",
            args={"param": "value"}
        )

        with pytest.raises(AttributeError):
            tool_call.id = "new_id"


class TestToolOutput:
    """Test the ToolOutput dataclass."""

    def test_initialization(self):
        """Test basic initialization of ToolOutput."""
        tool_output = ToolOutput(
            id="call_123",
            output="Tool execution successful"
        )

        assert tool_output.id == "call_123"
        assert tool_output.output == "Tool execution successful"

    def test_from_message(self):
        """Test creating ToolOutput from ToolMessage."""
        tool_message = ToolMessage(
            content="Recipe found: IPA with 6.5% ABV",
            tool_call_id="call_456"
        )

        tool_output = ToolOutput.from_message(tool_message)

        assert tool_output.id == "call_456"
        assert tool_output.output == "Recipe found: IPA with 6.5% ABV"

    def test_from_message_empty_content(self):
        """Test handling empty content in ToolMessage."""
        tool_message = ToolMessage(
            content="",
            tool_call_id="call_empty"
        )

        tool_output = ToolOutput.from_message(tool_message)

        assert tool_output.id == "call_empty"
        assert tool_output.output == ""

    def test_frozen_dataclass(self):
        """Test that ToolOutput is frozen (immutable)."""
        tool_output = ToolOutput(
            id="call_123",
            output="test output"
        )

        with pytest.raises(AttributeError):
            tool_output.output = "new output"


class TestToolCallEvent:
    """Test the ToolCallEvent class."""

    def test_initialization(self):
        """Test basic initialization of ToolCallEvent."""
        tool_calls = [
            ToolCall("call_1", "tool_1", {"param": "value1"}),
            ToolCall("call_2", "tool_2", {"param": "value2"}),
        ]

        event = ToolCallEvent(tool_calls=tool_calls)

        assert len(event.tool_calls) == 2
        assert event.tool_calls[0].id == "call_1"
        assert event.tool_calls[1].id == "call_2"

    def test_from_tool_calls(self):
        """Test creating ToolCallEvent from raw tool call data."""
        tool_calls_data = [
            {
                "id": "call_1",
                "function": {
                    "name": "search_hops",
                    "arguments": '{"query": "cascade"}'
                }
            },
            {
                "id": "call_2",
                "function": {
                    "name": "get_recipe",
                    "arguments": '{"recipe_id": "rec_123"}'
                }
            }
        ]

        event = ToolCallEvent.from_tool_calls(tool_calls_data)

        assert len(event.tool_calls) == 2
        assert event.tool_calls[0].tool_name == "search_hops"
        assert event.tool_calls[1].tool_name == "get_recipe"

    def test_display_single_tool(self):
        """Test display message for single tool call."""
        tool_calls = [ToolCall("call_1", "tool_1", {})]
        event = ToolCallEvent(tool_calls=tool_calls)

        assert event.display() == "Calling 1 tool"

    def test_display_multiple_tools(self):
        """Test display message for multiple tool calls."""
        tool_calls = [
            ToolCall("call_1", "tool_1", {}),
            ToolCall("call_2", "tool_2", {}),
            ToolCall("call_3", "tool_3", {}),
        ]
        event = ToolCallEvent(tool_calls=tool_calls)

        assert event.display() == "Calling 3 tools"

    def test_str_method(self):
        """Test string representation uses display method."""
        tool_calls = [ToolCall("call_1", "tool_1", {})]
        event = ToolCallEvent(tool_calls=tool_calls)

        assert str(event) == "Calling 1 tool"


class TestToolOutputEvent:
    """Test the ToolOutputEvent class."""

    def test_initialization(self):
        """Test basic initialization of ToolOutputEvent."""
        tool_outputs = [
            ToolOutput("call_1", "Output 1"),
            ToolOutput("call_2", "Output 2"),
        ]

        event = ToolOutputEvent(tool_outputs=tool_outputs)

        assert len(event.tool_outputs) == 2
        assert event.tool_outputs[0].output == "Output 1"
        assert event.tool_outputs[1].output == "Output 2"

    def test_from_tool_messages(self):
        """Test creating ToolOutputEvent from ToolMessages."""
        tool_messages = [
            ToolMessage(content="Found 5 hops", tool_call_id="call_1"),
            ToolMessage(content="Recipe updated", tool_call_id="call_2"),
        ]

        event = ToolOutputEvent.from_tool_messages(tool_messages)

        assert len(event.tool_outputs) == 2
        assert event.tool_outputs[0].output == "Found 5 hops"
        assert event.tool_outputs[1].output == "Recipe updated"

    def test_display(self):
        """Test display message for tool outputs."""
        tool_outputs = [
            ToolOutput("call_1", "Output 1"),
            ToolOutput("call_2", "Output 2"),
            ToolOutput("call_3", "Output 3"),
        ]
        event = ToolOutputEvent(tool_outputs=tool_outputs)

        assert event.display() == "3 tool calls completed"


class TestAIMessageEvent:
    """Test the AIMessageEvent class."""

    def test_initialization(self):
        """Test basic initialization of AIMessageEvent."""
        content = "I found 3 recipes that match your criteria."
        event = AIMessageEvent(content=content)

        assert event.content == content

    def test_display(self):
        """Test display returns the content."""
        content = "Here's your recipe analysis..."
        event = AIMessageEvent(content=content)

        assert event.display() == content

    def test_str_method(self):
        """Test string representation uses display method."""
        content = "Recipe created successfully!"
        event = AIMessageEvent(content=content)

        assert str(event) == content


class TestAgentEventFromOutput:
    """Test the AgentEvent.from_output factory method."""

    def test_ai_message_event(self):
        """Test parsing AI message event from output."""
        ai_message = AIMessage(
            content="I can help you with that recipe!",
            additional_kwargs={}
        )

        data = {
            "agent": {
                "messages": [ai_message]
            }
        }

        event = AgentEvent.from_output(data)

        assert isinstance(event, AIMessageEvent)
        assert event.content == "I can help you with that recipe!"

    def test_tool_call_event(self):
        """Test parsing tool call event from output."""
        ai_message = AIMessage(
            content="",
            additional_kwargs={
                "tool_calls": [
                    {
                        "id": "call_123",
                        "function": {
                            "name": "search_recipes",
                            "arguments": '{"query": "IPA"}'
                        }
                    }
                ]
            }
        )

        data = {
            "agent": {
                "messages": [ai_message]
            }
        }

        event = AgentEvent.from_output(data)

        assert isinstance(event, ToolCallEvent)
        assert len(event.tool_calls) == 1
        assert event.tool_calls[0].tool_name == "search_recipes"

    def test_tool_output_event(self):
        """Test parsing tool output event from output."""
        tool_message = ToolMessage(
            content="Found 3 recipes",
            tool_call_id="call_123"
        )

        data = {
            "tools": {
                "messages": [tool_message]
            }
        }

        event = AgentEvent.from_output(data)

        assert isinstance(event, ToolOutputEvent)
        assert len(event.tool_outputs) == 1
        assert event.tool_outputs[0].output == "Found 3 recipes"

    def test_no_message_found(self):
        """Test error when no valid message is found."""
        data = {
            "unknown": {
                "messages": []
            }
        }

        with pytest.raises(ValueError, match="No message found"):
            AgentEvent.from_output(data)

    def test_empty_data(self):
        """Test error with empty data."""
        data = {}

        with pytest.raises(ValueError, match="No message found"):
            AgentEvent.from_output(data)
