"""
Tests for the MasterBrewerAgent class and agent graph functionality.
"""

import pytest
from unittest.mock import Mock, patch

from core.agents.master_brewer import MasterBrewerAgent, create_all_auto_generated_tools
from core.agents.models import ModelConfiguration, ChatModelProvider
from core.agents.events import AgentEvent


class TestCreateAllAutoGeneratedTools:
    """Test the create_all_auto_generated_tools function."""

    @patch("core.agents.master_brewer.ServiceToolFactory")
    def test_creates_tools_from_all_services(self, mock_factory):
        """Test that tools are created from all service classes."""
        mock_tools = [<PERSON><PERSON>(), <PERSON><PERSON>(), <PERSON><PERSON>()]
        mock_factory.create_service_tools.return_value = mock_tools

        result = create_all_auto_generated_tools()

        # Should call create_service_tools for each service
        assert mock_factory.create_service_tools.call_count == 5
        # Should return all tools combined
        assert len(result) == 15  # 5 services * 3 tools each


class TestMasterBrewerAgent:
    """Test the MasterBrewerAgent class."""

    def test_initialization(self):
        """Test basic initialization of MasterBrewerAgent."""
        mock_graph = Mock()

        agent = MasterBrewerAgent(graph=mock_graph)

        assert agent.graph == mock_graph

    @patch("core.agents.master_brewer.create_react_agent")
    @patch("core.agents.master_brewer.provide_memory")
    @patch("core.agents.master_brewer.create_bjcp_retriever_tool")
    @patch("core.agents.master_brewer.create_all_auto_generated_tools")
    @patch("core.agents.master_brewer.ModelConfiguration.from_settings")
    @patch("core.agents.master_brewer.ToolNode")
    def test_create_with_default_config(self, mock_tool_node, mock_from_settings, mock_auto_tools, mock_bjcp_tool, mock_memory, mock_create_agent):
        """Test creating agent with default configuration."""
        mock_graph = Mock()
        mock_create_agent.return_value = mock_graph
        mock_auto_tools.return_value = [Mock(), Mock()]
        mock_bjcp_tool.return_value = Mock()
        mock_memory.return_value = Mock()
        mock_tool_node.return_value = Mock()

        # Mock the model configuration
        mock_config = Mock()
        mock_model = Mock()
        mock_config.make_model.return_value = mock_model
        mock_from_settings.return_value = mock_config

        agent = MasterBrewerAgent.create()

        assert isinstance(agent, MasterBrewerAgent)
        assert agent.graph == mock_graph
        mock_create_agent.assert_called_once()

    @patch("core.agents.master_brewer.create_react_agent")
    @patch("core.agents.master_brewer.provide_memory")
    @patch("core.agents.master_brewer.create_bjcp_retriever_tool")
    @patch("core.agents.master_brewer.create_all_auto_generated_tools")
    @patch("core.agents.master_brewer.ToolNode")
    def test_create_with_custom_config(self, mock_tool_node, mock_auto_tools, mock_bjcp_tool, mock_memory, mock_create_agent):
        """Test creating agent with custom model configuration."""
        mock_graph = Mock()
        mock_create_agent.return_value = mock_graph
        mock_auto_tools.return_value = [Mock(), Mock()]
        mock_bjcp_tool.return_value = Mock()
        mock_memory.return_value = Mock()
        mock_tool_node.return_value = Mock()

        # Create a mock config instead of a real one to avoid super() issues
        custom_config = Mock()
        mock_model = Mock()
        custom_config.make_model.return_value = mock_model

        agent = MasterBrewerAgent.create(model=custom_config)

        assert isinstance(agent, MasterBrewerAgent)
        assert agent.graph == mock_graph

    @patch("core.agents.master_brewer.create_react_agent")
    @patch("core.agents.master_brewer.provide_memory")
    @patch("core.agents.master_brewer.create_bjcp_retriever_tool")
    @patch("core.agents.master_brewer.create_all_auto_generated_tools")
    @patch("core.agents.master_brewer.ModelConfiguration.from_settings")
    @patch("core.agents.master_brewer.ToolNode")
    def test_create_calls_all_dependencies(self, mock_tool_node, mock_from_settings, mock_auto_tools, mock_bjcp_tool, mock_memory, mock_create_agent):
        """Test that create method calls all required dependencies."""
        mock_graph = Mock()
        mock_create_agent.return_value = mock_graph
        mock_auto_tools.return_value = [Mock(), Mock()]
        mock_bjcp_tool.return_value = Mock()
        mock_memory_instance = Mock()
        mock_memory.return_value = mock_memory_instance
        mock_tool_node.return_value = Mock()

        # Mock the model configuration
        mock_config = Mock()
        mock_model = Mock()
        mock_config.make_model.return_value = mock_model
        mock_from_settings.return_value = mock_config

        agent = MasterBrewerAgent.create()

        mock_auto_tools.assert_called_once()
        mock_bjcp_tool.assert_called_once()
        mock_memory.assert_called_once()
        mock_create_agent.assert_called_once()

        # Check that create_react_agent was called with correct arguments
        call_args = mock_create_agent.call_args
        assert 'model' in call_args.kwargs
        assert 'tools' in call_args.kwargs
        assert 'prompt' in call_args.kwargs
        assert 'checkpointer' in call_args.kwargs
        assert call_args.kwargs['checkpointer'] == mock_memory_instance

    def test_run_method(self):
        """Test the run method with mocked graph."""
        mock_graph = Mock()
        mock_recipe = Mock()
        mock_recipe.id = "recipe_123"

        # Mock the graph.stream method with proper message format
        from langchain_core.messages import AIMessage
        mock_updates = [
            {"agent": {"messages": [AIMessage(content="Hello")]}},
            {"agent": {"messages": [AIMessage(content="World")]}}
        ]
        mock_graph.stream.return_value = mock_updates

        agent = MasterBrewerAgent(graph=mock_graph)

        # Call run method
        result = agent.run("test input", mock_recipe)

        # Convert generator to list to test
        events = list(result)

        # Verify graph.stream was called with correct parameters
        mock_graph.stream.assert_called_once()
        call_args = mock_graph.stream.call_args

        # Check input format
        input_data = call_args[0][0]
        assert "messages" in input_data
        assert "recipe_id" in input_data
        assert input_data["recipe_id"] == "recipe_123"
        assert input_data["messages"][0]["content"] == "test input"

        # Check config
        config = call_args[0][1]
        assert config["configurable"]["thread_id"] == "recipe_123"

        # Check stream_mode
        assert call_args[1]["stream_mode"] == "updates"

        # Should return mapped events
        assert len(events) == 2

    def test_graph_property_access(self):
        """Test that graph property is accessible."""
        mock_graph = Mock()
        agent = MasterBrewerAgent(graph=mock_graph)

        assert agent.graph is mock_graph

    def test_agent_integration_smoke_test(self):
        """Test basic agent creation and method access."""
        mock_graph = Mock()
        agent = MasterBrewerAgent(graph=mock_graph)

        # Basic smoke test - agent should be created successfully
        assert agent is not None
        assert hasattr(agent, 'run')
        assert hasattr(agent, 'graph')
        assert callable(agent.run)
