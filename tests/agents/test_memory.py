"""
Tests for agent memory system.
"""

import pytest
from unittest.mock import patch, Mock

from langgraph.checkpoint.memory import InMemorySaver

from core.agents.memory import provide_memory


class TestProvideMemory:
    """Test the provide_memory function."""

    @patch("core.agents.memory.InMemorySaver")
    def test_provide_memory_returns_in_memory_saver(self, mock_in_memory_saver):
        """Test that provide_memory returns an InMemorySaver instance."""
        mock_saver = Mock()
        mock_in_memory_saver.return_value = mock_saver

        result = provide_memory()

        assert result == mock_saver
        mock_in_memory_saver.assert_called_once_with()

    def test_provide_memory_integration(self):
        """Test actual integration with InMemorySaver."""
        result = provide_memory()

        assert isinstance(result, InMemorySaver)

    def test_provide_memory_multiple_calls(self):
        """Test that multiple calls return separate instances."""
        saver1 = provide_memory()
        saver2 = provide_memory()

        # Should be different instances
        assert saver1 is not saver2
        assert isinstance(saver1, InMemorySaver)
        assert isinstance(saver2, InMemorySaver)

    def test_memory_saver_functionality(self):
        """Test basic functionality of the returned memory saver."""
        saver = provide_memory()

        # Test that it has the expected interface
        assert hasattr(saver, 'put')
        assert hasattr(saver, 'get')
        assert hasattr(saver, 'list')

        # Test basic get functionality (put requires more complex setup)
        config = {"configurable": {"thread_id": "test_thread"}}

        # Should be able to call get (will return None for non-existent thread)
        result = saver.get(config)
        assert result is None  # No checkpoint exists yet
