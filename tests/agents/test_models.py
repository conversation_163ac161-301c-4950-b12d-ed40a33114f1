"""
Tests for agent model configurations and language model providers.
"""

import pytest
from unittest.mock import patch, Mock
from os import environ

from core.agents.models import (
    ModelConfiguration,
    ChatModelProvider,
)


class TestChatModelProvider:
    """Test the ChatModelProvider enum."""

    def test_enum_values(self):
        """Test that enum has expected values."""
        assert ChatModelProvider.OLLAMA.value == "ollama"
        assert ChatModelProvider.OPENAI.value == "openai"

    def test_enum_membership(self):
        """Test enum membership checks."""
        assert ChatModelProvider.OLLAMA in ChatModelProvider
        assert ChatModelProvider.OPENAI in ChatModelProvider


class TestModelConfiguration:
    """Test the ModelConfiguration dataclass."""

    def test_initialization(self):
        """Test basic initialization of ModelConfiguration."""
        config = ModelConfiguration(
            base_url="http://localhost:11434",
            api_key="test-key",
            model_name="llama2",
            model_provider=ChatModelProvider.OLLAMA,
            temperature=0.5,
        )

        assert config.base_url == "http://localhost:11434"
        assert config.api_key == "test-key"
        assert config.model_name == "llama2"
        assert config.model_provider == ChatModelProvider.OLLAMA
        assert config.temperature == 0.5

    def test_frozen_dataclass(self):
        """Test that ModelConfiguration is frozen (immutable)."""
        config = ModelConfiguration(
            base_url=None,
            api_key="test-key",
            model_name="gpt-4o",
            model_provider=ChatModelProvider.OPENAI,
            temperature=0.2,
        )

        with pytest.raises(AttributeError):
            config.temperature = 0.8

    @patch.dict(environ, {"OPENAI_API_KEY": "test-openai-key"})
    def test_from_settings_default(self):
        """Test creating configuration from default settings."""
        config = ModelConfiguration.from_settings()

        assert config.model_provider == ChatModelProvider.OPENAI
        assert config.base_url is None
        assert config.api_key == "test-openai-key"
        assert config.model_name == "gpt-4o"
        assert config.temperature == 0.2

    @patch.dict(environ, {}, clear=True)
    def test_from_settings_no_api_key(self):
        """Test creating configuration when no API key is set."""
        config = ModelConfiguration.from_settings()

        assert config.api_key == ""

    @patch("core.agents.models.ChatOllama")
    def test_make_model_ollama(self, mock_chat_ollama):
        """Test creating Ollama model."""
        mock_model = Mock()
        mock_chat_ollama.return_value = mock_model

        config = ModelConfiguration(
            base_url="http://localhost:11434",
            api_key="",
            model_name="llama2",
            model_provider=ChatModelProvider.OLLAMA,
            temperature=0.3,
        )

        result = config.make_model()

        assert result == mock_model
        mock_chat_ollama.assert_called_once_with(
            model="llama2",
            base_url="http://localhost:11434",
            temperature=0.3,
        )

    @patch("core.agents.models.ChatOpenAI")
    def test_make_model_openai(self, mock_chat_openai):
        """Test creating OpenAI model."""
        mock_model = Mock()
        mock_chat_openai.return_value = mock_model

        config = ModelConfiguration(
            base_url=None,
            api_key="test-key",
            model_name="gpt-4o",
            model_provider=ChatModelProvider.OPENAI,
            temperature=0.2,
        )

        result = config.make_model()

        assert result == mock_model
        mock_chat_openai.assert_called_once_with(
            model="gpt-4o",
            temperature=0.2,
            openai_api_key="test-key",
            openai_api_base=None,
        )

    @patch("core.agents.models.ChatOpenAI")
    def test_make_model_openai_with_base_url(self, mock_chat_openai):
        """Test creating OpenAI model with custom base URL."""
        mock_model = Mock()
        mock_chat_openai.return_value = mock_model

        config = ModelConfiguration(
            base_url="https://custom-api.example.com",
            api_key="test-key",
            model_name="gpt-3.5-turbo",
            model_provider=ChatModelProvider.OPENAI,
            temperature=0.7,
        )

        result = config.make_model()

        assert result == mock_model
        mock_chat_openai.assert_called_once_with(
            model="gpt-3.5-turbo",
            temperature=0.7,
            openai_api_key="test-key",
            openai_api_base="https://custom-api.example.com",
        )

    def test_make_model_unknown_provider(self):
        """Test that unknown provider raises ValueError."""
        # Create a mock provider that's not in the enum
        config = ModelConfiguration(
            base_url=None,
            api_key="test-key",
            model_name="test-model",
            model_provider="unknown",  # This will be a string, not enum
            temperature=0.5,
        )

        with pytest.raises(ValueError, match="Unknown model provider: unknown"):
            config.make_model()

    def test_slots_optimization(self):
        """Test that the dataclass uses slots for memory optimization."""
        config = ModelConfiguration(
            base_url=None,
            api_key="test-key",
            model_name="gpt-4o",
            model_provider=ChatModelProvider.OPENAI,
            temperature=0.2,
        )

        # Should not be able to add arbitrary attributes due to slots
        # The exact exception type may vary, but it should prevent attribute assignment
        with pytest.raises((AttributeError, TypeError)):
            config.new_attribute = "test"

    def test_equality(self):
        """Test equality comparison between configurations."""
        config1 = ModelConfiguration(
            base_url=None,
            api_key="test-key",
            model_name="gpt-4o",
            model_provider=ChatModelProvider.OPENAI,
            temperature=0.2,
        )

        config2 = ModelConfiguration(
            base_url=None,
            api_key="test-key",
            model_name="gpt-4o",
            model_provider=ChatModelProvider.OPENAI,
            temperature=0.2,
        )

        config3 = ModelConfiguration(
            base_url=None,
            api_key="different-key",
            model_name="gpt-4o",
            model_provider=ChatModelProvider.OPENAI,
            temperature=0.2,
        )

        assert config1 == config2
        assert config1 != config3

    def test_hash(self):
        """Test that configurations are hashable."""
        config = ModelConfiguration(
            base_url=None,
            api_key="test-key",
            model_name="gpt-4o",
            model_provider=ChatModelProvider.OPENAI,
            temperature=0.2,
        )

        # Should be able to use as dict key or in set
        config_set = {config}
        config_dict = {config: "test"}

        assert len(config_set) == 1
        assert config_dict[config] == "test"
