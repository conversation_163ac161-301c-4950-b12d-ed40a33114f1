"""
Tests for web scraper functionality and schemas.
"""

import pytest
from unittest.mock import Mock, patch
from pydantic import ValidationError

from core.agents.scraper.schemas import (
    FermentableData,
    HopData,
    YeastData,
    ScrapingResult,
    IngredientType,
    FermentableType,
    YeastType,
    YeastForm,
)
from core.agents.scraper.simple_scraper import SimpleScraper


class TestFermentableData:
    """Test the FermentableData schema."""

    def test_valid_fermentable(self):
        """Test creating a valid fermentable data."""
        fermentable = FermentableData(
            name="Pale Malt",
            country_of_origin="United States",
            fermentable_type=FermentableType.BASE_MALT,
            extract_potential_ppg=37,
            color_lovibond=2.0
        )

        assert fermentable.name == "Pale Malt"
        assert fermentable.country_of_origin == "United States"
        assert fermentable.fermentable_type == FermentableType.BASE_MALT
        assert fermentable.extract_potential_ppg == 37
        assert fermentable.color_lovibond == 2.0

    def test_fermentable_defaults(self):
        """Test fermentable with default values."""
        fermentable = FermentableData(name="Crystal Malt")

        assert fermentable.name == "Crystal Malt"
        assert fermentable.country_of_origin == ""
        assert fermentable.fermentable_type == FermentableType.BASE_MALT
        assert fermentable.extract_potential_ppg == 37.0
        assert fermentable.color_lovibond == 2.0

    def test_fermentable_ingredient_type(self):
        """Test that fermentable returns correct ingredient type."""
        fermentable = FermentableData(name="Test Malt")

        assert fermentable.get_ingredient_type() == IngredientType.FERMENTABLES

    def test_fermentable_validation_errors(self):
        """Test validation errors for invalid fermentable data."""
        with pytest.raises(ValidationError):
            FermentableData(name="Test", extract_potential_ppg=60.0)  # Too high PPG

        with pytest.raises(ValidationError):
            FermentableData(name="Test", color_lovibond=-1.0)  # Negative color


class TestHopData:
    """Test the HopData schema."""

    def test_valid_hop(self):
        """Test creating a valid hop data."""
        hop = HopData(
            name="Cascade",
            country_of_origin="United States",
            alpha_acid=5.5,
            notes="Citrusy and floral"
        )

        assert hop.name == "Cascade"
        assert hop.country_of_origin == "United States"
        assert hop.alpha_acid == 5.5
        assert hop.notes == "Citrusy and floral"

    def test_hop_defaults(self):
        """Test hop with default values."""
        hop = HopData(name="Centennial")

        assert hop.name == "Centennial"
        assert hop.country_of_origin == ""
        assert hop.alpha_acid == 0.0
        assert hop.notes == ""

    def test_hop_ingredient_type(self):
        """Test that hop returns correct ingredient type."""
        hop = HopData(name="Test Hop")

        assert hop.get_ingredient_type() == IngredientType.HOPS

    def test_hop_validation_errors(self):
        """Test validation errors for invalid hop data."""
        # HopData doesn't have strict validation, so just test it can be created
        hop = HopData(name="")
        assert hop.name == ""


class TestYeastData:
    """Test the YeastData schema."""

    def test_valid_yeast(self):
        """Test creating a valid yeast data."""
        yeast = YeastData(
            name="Safale US-05",
            laboratory="Fermentis",
            product_id="US-05",
            yeast_type=YeastType.ALE,
            yeast_form=YeastForm.DRY,
            min_temperature_fahrenheit=60.0,
            max_temperature_fahrenheit=78.0,
            attenuation_percent=81.0
        )

        assert yeast.name == "Safale US-05"
        assert yeast.laboratory == "Fermentis"
        assert yeast.product_id == "US-05"
        assert yeast.yeast_type == YeastType.ALE
        assert yeast.yeast_form == YeastForm.DRY
        assert yeast.min_temperature_fahrenheit == 60.0
        assert yeast.max_temperature_fahrenheit == 78.0
        assert yeast.attenuation_percent == 81.0

    def test_yeast_defaults(self):
        """Test yeast with default values."""
        yeast = YeastData(name="Generic Ale Yeast")

        assert yeast.name == "Generic Ale Yeast"
        assert yeast.laboratory == ""
        assert yeast.product_id == ""
        assert yeast.yeast_type == YeastType.ALE
        assert yeast.yeast_form == YeastForm.DRY
        assert yeast.min_temperature_fahrenheit == 65.0
        assert yeast.max_temperature_fahrenheit == 75.0
        assert yeast.attenuation_percent == 75.0

    def test_yeast_ingredient_type(self):
        """Test that yeast returns correct ingredient type."""
        yeast = YeastData(name="Test Yeast")

        assert yeast.get_ingredient_type() == IngredientType.YEASTS

    def test_yeast_validation_errors(self):
        """Test validation errors for invalid yeast data."""
        # YeastData doesn't have strict validation, so just test it can be created
        yeast = YeastData(name="")
        assert yeast.name == ""


class TestScrapingResult:
    """Test the ScrapingResult schema."""

    def test_valid_scraping_result(self):
        """Test creating a valid scraping result."""
        fermentable = FermentableData(name="Pale Malt")
        hop = HopData(name="Cascade")

        result = ScrapingResult(
            ingredient_type=IngredientType.FERMENTABLES,
            source_url="https://example.com/ingredients",
            ingredients=[fermentable, hop]
        )

        assert result.ingredient_type == IngredientType.FERMENTABLES
        assert result.source_url == "https://example.com/ingredients"
        assert len(result.ingredients) == 2
        assert result.ingredients[0].name == "Pale Malt"
        assert result.ingredients[1].name == "Cascade"

    def test_scraping_result_empty_ingredients(self):
        """Test scraping result with no ingredients."""
        result = ScrapingResult(
            ingredient_type=IngredientType.HOPS,
            source_url="https://example.com/hops"
        )

        assert result.ingredient_type == IngredientType.HOPS
        assert result.source_url == "https://example.com/hops"
        assert len(result.ingredients) == 0

    def test_scraping_result_validation_errors(self):
        """Test validation errors for invalid scraping result."""
        # ScrapingResult doesn't validate empty URLs, so just test it can be created
        result = ScrapingResult(
            ingredient_type=IngredientType.FERMENTABLES,
            source_url=""  # Empty URL
        )
        assert result.source_url == ""


class TestSimpleScraper:
    """Test the SimpleScraper class."""

    def test_initialization(self):
        """Test basic initialization of SimpleScraper."""
        scraper = SimpleScraper()

        assert isinstance(scraper, SimpleScraper)
        assert hasattr(scraper, 'scrape_ingredients')
        assert hasattr(scraper, 'model_config')
        assert hasattr(scraper, 'session')

    def test_initialization_with_custom_config(self):
        """Test initialization with custom model configuration."""
        from core.agents.models import ModelConfiguration, ChatModelProvider

        custom_config = ModelConfiguration(
            base_url="http://localhost:11434",
            api_key="",
            model_name="llama2",
            model_provider=ChatModelProvider.OLLAMA,
            temperature=0.3
        )

        scraper = SimpleScraper(model_config=custom_config)

        assert scraper.model_config == custom_config

    @patch("core.agents.scraper.simple_scraper.requests.Session.get")
    def test_scrape_ingredients_success(self, mock_get):
        """Test successful ingredient scraping."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = "<html><body>Test content with ingredients</body></html>"
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response

        scraper = SimpleScraper()

        # Mock the LLM extraction to return empty list
        with patch.object(scraper, '_llm_extract_all_ingredients', return_value=[]):
            result = scraper.scrape_ingredients("https://example.com", IngredientType.HOPS)

        assert isinstance(result, ScrapingResult)
        assert result.ingredient_type == IngredientType.HOPS
        assert result.source_url == "https://example.com"
        mock_get.assert_called()

    @patch("core.agents.scraper.simple_scraper.requests.Session.get")
    def test_scrape_ingredients_failure(self, mock_get):
        """Test ingredient scraping failure handling."""
        mock_get.side_effect = Exception("Network error")

        scraper = SimpleScraper()
        result = scraper.scrape_ingredients("https://example.com", IngredientType.FERMENTABLES)

        assert isinstance(result, ScrapingResult)
        assert result.success == False
        assert len(result.errors) > 0

    def test_get_schema_example(self):
        """Test schema example generation."""
        scraper = SimpleScraper()

        fermentable_example = scraper._get_schema_example(IngredientType.FERMENTABLES)
        assert "Pilsner Malt" in fermentable_example
        assert "extract_potential_ppg" in fermentable_example

        yeast_example = scraper._get_schema_example(IngredientType.YEASTS)
        assert "WLP001" in yeast_example
        assert "attenuation_percent" in yeast_example

        hop_example = scraper._get_schema_example(IngredientType.HOPS)
        assert "Cascade" in hop_example
        assert "alpha_acid" in hop_example

    def test_export_to_csv_no_ingredients(self):
        """Test CSV export with no ingredients."""
        scraper = SimpleScraper()
        result = ScrapingResult(
            ingredient_type=IngredientType.HOPS,
            source_url="https://example.com"
        )

        # Should not raise an exception
        scraper.export_to_csv(result, "test.csv")

    def test_scraper_interface(self):
        """Test that scraper implements expected interface."""
        scraper = SimpleScraper()

        # Should have required methods
        assert hasattr(scraper, 'scrape_ingredients')
        assert hasattr(scraper, 'export_to_csv')
        assert hasattr(scraper, '_get_schema_example')
        assert hasattr(scraper, 'model_config')
        assert hasattr(scraper, 'session')
        assert callable(scraper.scrape_ingredients)
        assert callable(scraper.export_to_csv)
