"""
Tests for agent tools factory and tool creation.
"""

import pytest
from unittest.mock import Mock, patch

from core.agents.tools.factory import ServiceToolFactory, agent_accessible, ToolMetadata


class TestAgentAccessibleDecorator:
    """Test the agent_accessible decorator."""

    def test_decorator_with_description(self):
        """Test decorator with explicit description."""
        @agent_accessible(description="Test tool description")
        def test_method(self):
            """Original docstring."""
            return "test result"

        assert hasattr(test_method, '_tool_metadata')
        metadata = test_method._tool_metadata
        assert isinstance(metadata, ToolMetadata)
        assert metadata.description == "Test tool description"
        assert metadata.serializer is None

    def test_decorator_without_description_uses_docstring(self):
        """Test decorator uses docstring when no description provided."""
        @agent_accessible()
        def test_method(self):
            """This is the docstring."""
            return "test result"

        metadata = test_method._tool_metadata
        assert metadata.description == "This is the docstring."

    def test_decorator_with_serializer(self):
        """Test decorator with serializer."""
        mock_serializer = Mock()

        @agent_accessible(serializer=mock_serializer)
        def test_method(self):
            return "test result"

        metadata = test_method._tool_metadata
        assert metadata.serializer == mock_serializer

    def test_decorator_preserves_function(self):
        """Test that decorator preserves original function behavior."""
        @agent_accessible(description="Test")
        def test_method(self, arg1, arg2="default"):
            return f"{arg1}-{arg2}"

        # Function should still work normally
        result = test_method(None, "hello", "world")
        assert result == "hello-world"


class TestServiceToolFactory:
    """Test the ServiceToolFactory class."""

    def test_initialization(self):
        """Test basic initialization of ServiceToolFactory."""
        class TestService:
            pass

        # Mock the DI container to return a test service instance
        mock_container = Mock()
        test_service_instance = TestService()
        mock_container.resolve.return_value = test_service_instance

        with patch('core.di.get_container', return_value=mock_container):
            factory = ServiceToolFactory(TestService)

        assert factory.service_class == TestService
        assert factory.service_instance is test_service_instance
        mock_container.resolve.assert_called_once_with(TestService)

    def test_bindable_methods_empty_service(self):
        """Test bindable_methods with service that has no decorated methods."""
        class EmptyService:
            def regular_method(self):
                return "not decorated"

        # Mock the DI container
        mock_container = Mock()
        mock_container.resolve.return_value = EmptyService()

        with patch('core.di.get_container', return_value=mock_container):
            factory = ServiceToolFactory(EmptyService)
            methods = factory.bindable_methods()

        assert methods == []

    def test_bindable_methods_with_decorated_methods(self):
        """Test bindable_methods with service that has decorated methods."""
        class TestService:
            @agent_accessible(description="Test method 1")
            def method1(self):
                return "result1"

            @agent_accessible(description="Test method 2")
            def method2(self):
                return "result2"

            def regular_method(self):
                return "not decorated"

        # Mock the DI container
        mock_container = Mock()
        mock_container.resolve.return_value = TestService()

        with patch('core.di.get_container', return_value=mock_container):
            factory = ServiceToolFactory(TestService)
            methods = factory.bindable_methods()

        assert len(methods) == 2
        assert "method1" in methods
        assert "method2" in methods
        assert "regular_method" not in methods

    def test_generate_tools_empty_service(self):
        """Test generate_tools with service that has no decorated methods."""
        class EmptyService:
            def regular_method(self):
                return "not decorated"

        # Mock the DI container
        mock_container = Mock()
        mock_container.resolve.return_value = EmptyService()

        with patch('core.di.get_container', return_value=mock_container):
            factory = ServiceToolFactory(EmptyService)
            tools = factory.generate_tools()

        assert tools == []

    def test_generate_tools_with_decorated_methods(self):
        """Test generate_tools with service that has decorated methods."""
        class TestService:
            @agent_accessible(description="Test method 1")
            def method1(self):
                return "result1"

            @agent_accessible(description="Test method 2")
            def method2(self, param1: str):
                return f"result2: {param1}"

        # Mock the DI container
        mock_container = Mock()
        mock_container.resolve.return_value = TestService()

        with patch('core.di.get_container', return_value=mock_container):
            factory = ServiceToolFactory(TestService)
            tools = factory.generate_tools()

        assert len(tools) == 2
        # Tools should be StructuredTool instances
        for tool in tools:
            assert hasattr(tool, 'name')
            assert hasattr(tool, 'description')

    def test_create_service_tools_static_method(self):
        """Test the static create_service_tools method."""
        class TestService:
            @agent_accessible(description="Static test method")
            def test_method(self):
                return "static result"

        # Mock the DI container
        mock_container = Mock()
        mock_container.resolve.return_value = TestService()

        with patch('core.di.get_container', return_value=mock_container):
            tools = ServiceToolFactory.create_service_tools(TestService)

        assert len(tools) == 1
        assert tools[0].name == "test_method"
        assert "Static test method" in tools[0].description

    def test_tool_metadata_build_tool(self):
        """Test ToolMetadata build_tool method."""
        def test_method(param1: str, param2: int = 42):
            """Test method docstring."""
            return f"{param1}-{param2}"

        metadata = ToolMetadata(description="Test tool")
        tool = metadata.build_tool(test_method)

        assert tool.name == "test_method"
        assert "Test tool" in tool.description
        assert hasattr(tool, 'args_schema')

    def test_tool_with_recipe_id_parameter(self):
        """Test tool generation with recipe_id parameter."""
        class TestService:
            @agent_accessible(description="Method with recipe_id")
            def method_with_recipe_id(self, recipe_id: str, other_param: str):
                return f"recipe: {recipe_id}, param: {other_param}"

        # Mock the DI container
        mock_container = Mock()
        mock_container.resolve.return_value = TestService()

        with patch('core.di.get_container', return_value=mock_container):
            factory = ServiceToolFactory(TestService)
            tools = factory.generate_tools()

        assert len(tools) == 1
        tool = tools[0]

        # recipe_id should not be in the args schema (it's injected from context)
        if hasattr(tool, 'args_schema'):
            schema_fields = tool.args_schema.__fields__ if hasattr(tool.args_schema, '__fields__') else {}
            assert 'recipe_id' not in schema_fields
            assert 'other_param' in schema_fields

    def test_tool_with_serializer(self):
        """Test tool generation with serializer."""
        mock_serializer = Mock()
        mock_serializer.return_value.data = {"test": "data"}

        class TestService:
            @agent_accessible(description="Method with serializer", serializer=mock_serializer)
            def method_with_serializer(self):
                return {"raw": "data"}

        # Mock the DI container
        mock_container = Mock()
        mock_container.resolve.return_value = TestService()

        with patch('core.di.get_container', return_value=mock_container):
            factory = ServiceToolFactory(TestService)
            tools = factory.generate_tools()

        assert len(tools) == 1
        # The tool should be created successfully
        assert tools[0].name == "method_with_serializer"
