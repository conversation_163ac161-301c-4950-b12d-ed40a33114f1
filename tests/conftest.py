"""
Pytest configuration and fixtures for hoplogic tests.

This module contains shared fixtures and configuration for all tests.
"""

import os
import django
from django.conf import settings

# Configure Django settings before importing models
if not settings.configured:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hoplogic.test_settings')
    django.setup()

import pytest
from unittest.mock import Mock, patch
from django.contrib.auth import get_user_model
from django.test import RequestFactory

from core.models import (
    Recipe, Hop, Fermentable, FermentableType,
    BoilHopInclusion, FirstWortHopInclusion, FlameoutHopInclusion,
    WhirlpoolHopInclusion, DryHopInclusion, FermentableInclusion,
    WaterProfile, Yeast, YeastType, YeastForm, YeastInclusion,
    MashStep, FermentationPhase
)
from .factories import (
    UserFactory, RecipeFactory, HopFactory, FermentableFactory,
    BoilHopInclusionFactory, FermentableInclusionFactory,
    WaterProfileFactory, YeastFactory
)

User = get_user_model()


# Database fixtures
@pytest.fixture
def db_user():
    """Create a test user in the database."""
    return UserFactory.create_in_db()


@pytest.fixture
def db_recipe(db_user):
    """Create a test recipe in the database."""
    return RecipeFactory.create_in_db(user=db_user)


@pytest.fixture
def db_hop():
    """Create a test hop in the database."""
    return HopFactory.create_in_db()


@pytest.fixture
def db_fermentable():
    """Create a test fermentable in the database."""
    return FermentableFactory.create_in_db()


# Mock fixtures
@pytest.fixture
def user():
    """Create a mock user object."""
    return UserFactory.create()


@pytest.fixture
def recipe():
    """Create a mock recipe object."""
    return RecipeFactory.create()


@pytest.fixture
def hop():
    """Create a mock hop object."""
    return HopFactory.create()


@pytest.fixture
def fermentable():
    """Create a mock fermentable object."""
    return FermentableFactory.create()


@pytest.fixture
def water_profile():
    """Create a mock water profile object."""
    return WaterProfileFactory.create()


@pytest.fixture
def yeast():
    """Create a mock yeast object."""
    return YeastFactory.create()


# Complex fixtures
@pytest.fixture
def recipe_with_fermentables(recipe, fermentable):
    """Create a recipe with fermentable inclusions."""
    inclusion = FermentableInclusionFactory.create(
        recipe=recipe,
        fermentable=fermentable,
        quantity=10.0,
        efficiency_percent=75.0
    )
    # Mock the relationship
    recipe.fermentableinclusion_set = Mock()
    recipe.fermentableinclusion_set.all.return_value = [inclusion]
    recipe.fermentableinclusion_set.exists.return_value = True
    return recipe


@pytest.fixture
def recipe_with_hops(recipe, hop):
    """Create a recipe with hop inclusions."""
    boil_hop = BoilHopInclusionFactory.create(
        recipe=recipe,
        hop=hop,
        quantity=1.0,
        time_minutes=60
    )
    # Mock the BoilHopInclusion.objects.filter call
    with patch('core.models.hop_inclusions.BoilHopInclusion.objects') as mock_objects:
        mock_objects.filter.return_value = [boil_hop]
        yield recipe


# Request fixtures
@pytest.fixture
def request_factory():
    """Django request factory."""
    return RequestFactory()


@pytest.fixture
def authenticated_request(request_factory, user):
    """Create an authenticated request."""
    request = request_factory.get('/')
    request.user = user
    return request


# Agent fixtures
@pytest.fixture
def mock_llm():
    """Mock LLM for agent testing."""
    with patch('core.agents.models.ChatOpenAI') as mock:
        mock_instance = Mock()
        mock_instance.invoke.return_value = Mock(content="Mocked response")
        mock.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def mock_embeddings():
    """Mock embeddings for vector store testing."""
    with patch('core.agents.embedding.OpenAIEmbeddings') as mock:
        mock_instance = Mock()
        mock_instance.embed_documents.return_value = [[0.1, 0.2, 0.3]]
        mock_instance.embed_query.return_value = [0.1, 0.2, 0.3]
        mock.return_value = mock_instance
        yield mock_instance


# Repository fixtures
@pytest.fixture
def mock_repository():
    """Generic mock repository."""
    mock = Mock()
    mock.get_by_id.return_value = None
    mock.get_by_id_or_raise.side_effect = lambda id: Mock(id=id)
    mock.get_all.return_value = []
    mock.filter.return_value = []
    mock.create.side_effect = lambda **kwargs: Mock(**kwargs)
    mock.update.side_effect = lambda obj, **kwargs: Mock(**{**obj.__dict__, **kwargs})
    mock.delete.return_value = None
    return mock


# Service fixtures
@pytest.fixture
def mock_service():
    """Generic mock service."""
    mock = Mock()
    return mock


# Pytest configuration
@pytest.fixture(autouse=True)
def enable_db_access_for_all_tests(db):
    """
    Grant database access to all tests.

    This fixture is automatically used for all tests, allowing them to access
    the database without needing to be marked with @pytest.mark.django_db.
    """
    pass


# Django settings override
@pytest.fixture(autouse=True)
def django_settings(settings):
    """Override Django settings for tests."""
    settings.DEBUG = True
    settings.SECRET_KEY = 'test-secret-key'
    settings.DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': ':memory:',
        }
    }
    return settings
