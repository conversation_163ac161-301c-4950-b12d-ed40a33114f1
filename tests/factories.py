"""
Test data factories for hoplogic tests.

This module contains factory classes for creating test data objects.
Factories provide both mock objects (for unit tests) and database objects (for integration tests).
"""

from unittest.mock import Mock
from django.contrib.auth import get_user_model
from core.models import (
    Recipe, Hop, Fermentable, FermentableType, BeerStyle,
    BoilHopInclusion, FirstWortHopInclusion, FlameoutHopInclusion,
    WhirlpoolHopInclusion, DryHopInclusion, FermentableInclusion,
    WaterProfile, Yeast, YeastType, YeastForm, YeastInclusion,
    MashStep, FermentationPhase
)
from core.models.units import QuantityUnit

User = get_user_model()


class BaseFactory:
    """Base factory class with common functionality."""

    @classmethod
    def create(cls, **kwargs):
        """Create a mock object with default values."""
        defaults = cls.get_defaults()
        defaults.update(kwargs)
        mock_obj = Mock()
        for key, value in defaults.items():
            setattr(mock_obj, key, value)
        return mock_obj

    @classmethod
    def create_in_db(cls, **kwargs):
        """Create a real database object."""
        defaults = cls.get_defaults()
        defaults.update(kwargs)
        return cls.model_class.objects.create(**defaults)

    @classmethod
    def get_defaults(cls):
        """Override in subclasses to provide default values."""
        return {}


class UserFactory(BaseFactory):
    """Factory for User objects."""
    model_class = User

    @classmethod
    def get_defaults(cls):
        return {
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User',
            'is_active': True,
        }


class BeerStyleFactory(BaseFactory):
    """Factory for BeerStyle objects."""
    model_class = BeerStyle

    @classmethod
    def get_defaults(cls):
        return {
            'name': 'American IPA',
            'description': 'A hoppy American beer style',
            'srm_min': 6.0,
            'srm_max': 14.0,
            'ibu_min': 40.0,
            'ibu_max': 70.0,
            'og_min': 1.056,
            'og_max': 1.070,
            'fg_min': 1.008,
            'fg_max': 1.014,
            'abv_min': 5.5,
            'abv_max': 7.5,
        }


class RecipeFactory(BaseFactory):
    """Factory for Recipe objects."""
    model_class = Recipe

    @classmethod
    def get_defaults(cls):
        return {
            'name': 'Test Recipe',
            'description': 'A test recipe for brewing',
            'batch_size_gallons': 5.0,
            'mash_efficiency_percent': 75.0,
            'target_original_gravity': 1.050,
        }

    @classmethod
    def create_with_calculations(cls, **kwargs):
        """Create a recipe with mocked calculation methods."""
        recipe = cls.create(**kwargs)
        recipe.calculated_original_gravity = 1.052
        recipe.original_gravity = 1.052
        recipe.calculated_srm = 8.5
        recipe.total_ibus = 35.0
        recipe.estimated_final_gravity = 1.012
        recipe.estimated_abv = 5.3
        return recipe


class HopFactory(BaseFactory):
    """Factory for Hop objects."""
    model_class = Hop

    @classmethod
    def get_defaults(cls):
        return {
            'name': 'Cascade',
            'alpha_acid': 5.5,
            'beta_acid': 4.5,
            'aroma': True,
            'bittering': False,
            'country_of_origin': 'US',
            'notes': 'Classic American aroma hop',
        }


class FermentableFactory(BaseFactory):
    """Factory for Fermentable objects."""
    model_class = Fermentable

    @classmethod
    def get_defaults(cls):
        return {
            'name': 'Pale 2-Row',
            'fermentable_type': FermentableType.BASE_MALT,
            'color_lovibond': 2.0,
            'extract_potential_ppg': 37.0,
            'requires_mashing': True,
            'country_of_origin': 'US',
            'notes': 'Base malt for most beer styles',
        }


class WaterProfileFactory(BaseFactory):
    """Factory for WaterProfile objects."""
    model_class = WaterProfile

    @classmethod
    def get_defaults(cls):
        return {
            'name': 'Balanced Profile',
            'calcium_ppm': 50.0,
            'magnesium_ppm': 10.0,
            'sodium_ppm': 15.0,
            'chloride_ppm': 50.0,
            'sulfate_ppm': 150.0,
            'bicarbonate_ppm': 100.0,
        }


class YeastFactory(BaseFactory):
    """Factory for Yeast objects."""
    model_class = Yeast

    @classmethod
    def get_defaults(cls):
        return {
            'name': 'Safale US-05',
            'yeast_type': YeastType.ALE,
            'yeast_form': YeastForm.DRY,
            'min_temperature_fahrenheit': 59.0,
            'max_temperature_fahrenheit': 75.0,
            'attenuation_percent': 75.0,
            'description': 'American ale yeast',
        }


# Inclusion Factories
class BoilHopInclusionFactory(BaseFactory):
    """Factory for BoilHopInclusion objects."""
    model_class = BoilHopInclusion

    @classmethod
    def get_defaults(cls):
        return {
            'quantity': 1.0,
            'quantity_unit': QuantityUnit.OUNCES,
            'time_minutes': 60,
            'notes': '',
        }

    @classmethod
    def create(cls, recipe=None, hop=None, **kwargs):
        """Create with recipe and hop relationships."""
        inclusion = super().create(**kwargs)
        inclusion.recipe = recipe or RecipeFactory.create()
        inclusion.hop = hop or HopFactory.create()

        # Mock calculation methods
        inclusion.quantity_in_ounces = kwargs.get('quantity', 1.0)
        inclusion.is_bittering_addition = kwargs.get('time_minutes', 60) >= 30
        inclusion.is_aroma_addition = kwargs.get('time_minutes', 60) < 15
        inclusion.ibu_contribution = 25.0  # Mock IBU calculation

        return inclusion

    @classmethod
    def create_in_db(cls, recipe=None, hop=None, **kwargs):
        """Create a real database object with relationships."""
        defaults = cls.get_defaults()
        defaults.update(kwargs)

        # Create related objects if not provided
        if recipe is None:
            recipe = RecipeFactory.create_in_db()
        if hop is None:
            hop = HopFactory.create_in_db()

        defaults['recipe'] = recipe
        defaults['hop'] = hop

        return cls.model_class.objects.create(**defaults)


class FirstWortHopInclusionFactory(BaseFactory):
    """Factory for FirstWortHopInclusion objects."""
    model_class = FirstWortHopInclusion

    @classmethod
    def get_defaults(cls):
        return {
            'quantity': 1.0,
            'quantity_unit': QuantityUnit.OUNCES,
            'notes': '',
        }

    @classmethod
    def create(cls, recipe=None, hop=None, **kwargs):
        inclusion = super().create(**kwargs)
        inclusion.recipe = recipe or RecipeFactory.create()
        inclusion.hop = hop or HopFactory.create()
        inclusion.quantity_in_ounces = kwargs.get('quantity', 1.0)
        inclusion.is_bittering_addition = True
        inclusion.is_aroma_addition = False
        inclusion.ibu_contribution = 30.0
        return inclusion

    @classmethod
    def create_in_db(cls, recipe=None, hop=None, **kwargs):
        """Create a real database object with relationships."""
        defaults = cls.get_defaults()
        defaults.update(kwargs)

        # Create related objects if not provided
        if recipe is None:
            recipe = RecipeFactory.create_in_db()
        if hop is None:
            hop = HopFactory.create_in_db()

        defaults['recipe'] = recipe
        defaults['hop'] = hop

        return cls.model_class.objects.create(**defaults)


class FlameoutHopInclusionFactory(BaseFactory):
    """Factory for FlameoutHopInclusion objects."""
    model_class = FlameoutHopInclusion

    @classmethod
    def get_defaults(cls):
        return {
            'quantity': 1.0,
            'quantity_unit': QuantityUnit.OUNCES,
            'notes': '',
        }

    @classmethod
    def create(cls, recipe=None, hop=None, **kwargs):
        inclusion = super().create(**kwargs)
        inclusion.recipe = recipe or RecipeFactory.create()
        inclusion.hop = hop or HopFactory.create()
        inclusion.quantity_in_ounces = kwargs.get('quantity', 1.0)
        inclusion.is_bittering_addition = False
        inclusion.is_aroma_addition = True
        inclusion.ibu_contribution = 0.0
        return inclusion

    @classmethod
    def create_in_db(cls, recipe=None, hop=None, **kwargs):
        """Create a real database object with relationships."""
        defaults = cls.get_defaults()
        defaults.update(kwargs)

        # Create related objects if not provided
        if recipe is None:
            recipe = RecipeFactory.create_in_db()
        if hop is None:
            hop = HopFactory.create_in_db()

        defaults['recipe'] = recipe
        defaults['hop'] = hop

        return cls.model_class.objects.create(**defaults)


class WhirlpoolHopInclusionFactory(BaseFactory):
    """Factory for WhirlpoolHopInclusion objects."""
    model_class = WhirlpoolHopInclusion

    @classmethod
    def get_defaults(cls):
        return {
            'quantity': 1.0,
            'quantity_unit': QuantityUnit.OUNCES,
            'time_minutes': 20,
            'temperature_f': 180,
            'notes': '',
        }

    @classmethod
    def create(cls, recipe=None, hop=None, **kwargs):
        inclusion = super().create(**kwargs)
        inclusion.recipe = recipe or RecipeFactory.create()
        inclusion.hop = hop or HopFactory.create()
        inclusion.quantity_in_ounces = kwargs.get('quantity', 1.0)
        inclusion.is_bittering_addition = False
        inclusion.is_aroma_addition = True
        inclusion.ibu_contribution = 5.0
        return inclusion

    @classmethod
    def create_in_db(cls, recipe=None, hop=None, **kwargs):
        """Create a real database object with relationships."""
        defaults = cls.get_defaults()
        defaults.update(kwargs)

        # Create related objects if not provided
        if recipe is None:
            recipe = RecipeFactory.create_in_db()
        if hop is None:
            hop = HopFactory.create_in_db()

        defaults['recipe'] = recipe
        defaults['hop'] = hop

        return cls.model_class.objects.create(**defaults)

    @classmethod
    def create_in_db(cls, recipe=None, hop=None, **kwargs):
        """Create a real database object with relationships."""
        defaults = cls.get_defaults()
        defaults.update(kwargs)

        # Create related objects if not provided
        if recipe is None:
            recipe = RecipeFactory.create_in_db()
        if hop is None:
            hop = HopFactory.create_in_db()

        defaults['recipe'] = recipe
        defaults['hop'] = hop

        return cls.model_class.objects.create(**defaults)


class DryHopInclusionFactory(BaseFactory):
    """Factory for DryHopInclusion objects."""
    model_class = DryHopInclusion

    @classmethod
    def get_defaults(cls):
        return {
            'quantity': 1.0,
            'quantity_unit': QuantityUnit.OUNCES,
            'time_days': 3,
            'notes': '',
        }

    @classmethod
    def create(cls, recipe=None, hop=None, **kwargs):
        inclusion = super().create(**kwargs)
        inclusion.recipe = recipe or RecipeFactory.create()
        inclusion.hop = hop or HopFactory.create()
        inclusion.quantity_in_ounces = kwargs.get('quantity', 1.0)
        inclusion.is_bittering_addition = False
        inclusion.is_aroma_addition = True
        inclusion.ibu_contribution = 0.0
        return inclusion

    @classmethod
    def create_in_db(cls, recipe=None, hop=None, **kwargs):
        """Create a real database object with relationships."""
        defaults = cls.get_defaults()
        defaults.update(kwargs)

        # Create related objects if not provided
        if recipe is None:
            recipe = RecipeFactory.create_in_db()
        if hop is None:
            hop = HopFactory.create_in_db()

        defaults['recipe'] = recipe
        defaults['hop'] = hop

        return cls.model_class.objects.create(**defaults)


class FermentableInclusionFactory(BaseFactory):
    """Factory for FermentableInclusion objects."""
    model_class = FermentableInclusion

    @classmethod
    def get_defaults(cls):
        return {
            'quantity': 10.0,
            'quantity_unit': QuantityUnit.POUNDS,
            'efficiency_percent': 75.0,
            'notes': '',
        }

    @classmethod
    def create(cls, recipe=None, fermentable=None, **kwargs):
        inclusion = super().create(**kwargs)
        inclusion.recipe = recipe or RecipeFactory.create()
        inclusion.fermentable = fermentable or FermentableFactory.create()

        # Mock calculation methods
        inclusion.quantity_in_pounds = kwargs.get('quantity', 10.0)
        inclusion.gravity_points_contribution = 30.0
        inclusion.srm_contribution = 2.0

        return inclusion

    @classmethod
    def create_in_db(cls, recipe=None, fermentable=None, **kwargs):
        """Create a real database object with relationships."""
        defaults = cls.get_defaults()
        defaults.update(kwargs)

        # Create related objects if not provided
        if recipe is None:
            recipe = RecipeFactory.create_in_db()
        if fermentable is None:
            fermentable = FermentableFactory.create_in_db()

        defaults['recipe'] = recipe
        defaults['fermentable'] = fermentable

        return cls.model_class.objects.create(**defaults)


class DryHopInclusionFactory(BaseFactory):
    """Factory for DryHopInclusion objects."""
    model_class = DryHopInclusion

    @classmethod
    def get_defaults(cls):
        return {
            'quantity': 1.0,
            'quantity_unit': QuantityUnit.OUNCES,
            'time_days': 3,
            'notes': '',
        }

    @classmethod
    def create(cls, recipe=None, hop=None, **kwargs):
        inclusion = super().create(**kwargs)
        inclusion.recipe = recipe or RecipeFactory.create()
        inclusion.hop = hop or HopFactory.create()
        inclusion.quantity_in_ounces = kwargs.get('quantity', 1.0)
        inclusion.is_bittering_addition = False
        inclusion.is_aroma_addition = True
        inclusion.ibu_contribution = 0.0
        return inclusion

    @classmethod
    def create_in_db(cls, recipe=None, hop=None, **kwargs):
        """Create a real database object with relationships."""
        defaults = cls.get_defaults()
        defaults.update(kwargs)

        # Create related objects if not provided
        if recipe is None:
            recipe = RecipeFactory.create_in_db()
        if hop is None:
            hop = HopFactory.create_in_db()

        defaults['recipe'] = recipe
        defaults['hop'] = hop

        return cls.model_class.objects.create(**defaults)


class YeastInclusionFactory(BaseFactory):
    """Factory for YeastInclusion objects."""
    model_class = YeastInclusion

    @classmethod
    def get_defaults(cls):
        return {
            'quantity': 1.0,
            'quantity_unit': 'PACKET',  # Use the actual choice value
            'notes': '',
        }

    @classmethod
    def create_in_db(cls, recipe=None, yeast=None, **kwargs):
        """Create a real database object with relationships."""
        defaults = cls.get_defaults()
        defaults.update(kwargs)

        # Create related objects if not provided
        if recipe is None:
            recipe = RecipeFactory.create_in_db()
        if yeast is None:
            yeast = YeastFactory.create_in_db()

        defaults['recipe'] = recipe
        defaults['yeast'] = yeast

        return cls.model_class.objects.create(**defaults)


class MashStepFactory(BaseFactory):
    """Factory for MashStep objects."""
    model_class = MashStep

    @classmethod
    def get_defaults(cls):
        return {
            'name': 'Single Infusion',
            'temperature_fahrenheit': 152.0,
            'duration_minutes': 60,
            'step_order': 1,
            'description': 'Single infusion mash step',
        }

    @classmethod
    def create_in_db(cls, recipe=None, **kwargs):
        """Create a real database object with relationships."""
        defaults = cls.get_defaults()
        defaults.update(kwargs)

        # Create related objects if not provided
        if recipe is None:
            recipe = RecipeFactory.create_in_db()

        defaults['recipe'] = recipe

        return cls.model_class.objects.create(**defaults)


class FermentationPhaseFactory(BaseFactory):
    """Factory for FermentationPhase objects."""
    model_class = FermentationPhase

    @classmethod
    def get_defaults(cls):
        return {
            'name': 'Primary Fermentation',
            'temperature_fahrenheit': 68.0,
            'duration_days': 7,
            'phase_order': 1,
            'description': 'Primary fermentation phase',
        }

    @classmethod
    def create_in_db(cls, recipe=None, **kwargs):
        """Create a real database object with relationships."""
        defaults = cls.get_defaults()
        defaults.update(kwargs)

        # Create related objects if not provided
        if recipe is None:
            recipe = RecipeFactory.create_in_db()

        defaults['recipe'] = recipe

        return cls.model_class.objects.create(**defaults)
