"""
Tests for BeerStyle data migration.

This module tests the data migration that creates the default beer style
and assigns it to existing recipes.
"""

import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model


class TestBeerStyleMigration(TestCase):
    """Test the beer style data migration functions."""

    def setUp(self):
        """Set up test data."""
        from core.models import Recipe

        User = get_user_model()

        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            first_name='Test',
            last_name='User'
        )

        # Create some recipes without beer styles
        from core.models import Recipe

        self.recipe1 = Recipe.objects.create(
            name='Recipe 1',
            description='First test recipe',
            batch_size_gallons=5.0,
            user=self.user
        )

        self.recipe2 = Recipe.objects.create(
            name='Recipe 2',
            description='Second test recipe',
            batch_size_gallons=10.0,
            user=self.user
        )

    def test_migration_creates_default_beer_style(self):
        """Test that the migration function creates the default 'Custom Style' beer style."""
        from core.models import BeerStyle
        from django.apps import apps

        # Simulate the migration function
        import importlib
        migration_module = importlib.import_module('core.migrations.0003_auto_20250721_2043')
        create_default_beer_style = migration_module.create_default_beer_style

        # Run the migration function
        create_default_beer_style(apps, None)

        # Check that the default style was created
        default_style = BeerStyle.objects.get(name='Custom Style')
        assert default_style.name == 'Custom Style'
        assert default_style.description == 'A custom beer style with no specific guidelines. Perfect for experimental and unique recipes.'

    def test_migration_assigns_default_style_to_existing_recipes(self):
        """Test that the migration assigns the default style to existing recipes."""
        from core.models import Recipe, BeerStyle
        from django.apps import apps

        # Simulate the migration function
        import importlib
        migration_module = importlib.import_module('core.migrations.0003_auto_20250721_2043')
        create_default_beer_style = migration_module.create_default_beer_style

        # Run the migration function
        create_default_beer_style(apps, None)

        # Get the default style
        default_style = BeerStyle.objects.get(name='Custom Style')

        # Check that both recipes were assigned the default style
        recipe1 = Recipe.objects.get(name='Recipe 1')
        recipe2 = Recipe.objects.get(name='Recipe 2')

        assert recipe1.beer_style == default_style
        assert recipe2.beer_style == default_style

    def test_migration_handles_no_existing_recipes(self):
        """Test that the migration works correctly when no recipes exist."""
        from core.models import Recipe, BeerStyle
        from django.apps import apps

        # Delete the recipes we created in setUp
        Recipe.objects.all().delete()

        # Simulate the migration function
        import importlib
        migration_module = importlib.import_module('core.migrations.0003_auto_20250721_2043')
        create_default_beer_style = migration_module.create_default_beer_style

        # Run the migration function
        create_default_beer_style(apps, None)

        # The default style should still be created
        default_style = BeerStyle.objects.get(name='Custom Style')
        assert default_style.name == 'Custom Style'

    def test_migration_idempotent(self):
        """Test that running the migration multiple times doesn't create duplicates."""
        from core.models import BeerStyle
        from django.apps import apps

        # First run the migration function
        import importlib
        migration_module = importlib.import_module('core.migrations.0003_auto_20250721_2043')
        create_default_beer_style = migration_module.create_default_beer_style
        create_default_beer_style(apps, None)

        # Count default styles after first run
        initial_count = BeerStyle.objects.filter(name='Custom Style').count()
        assert initial_count == 1

        # Manually run the migration function again
        create_default_beer_style(apps, None)

        # Should still only have one default style
        final_count = BeerStyle.objects.filter(name='Custom Style').count()
        assert final_count == 1

    def test_reverse_migration(self):
        """Test that the reverse migration works correctly."""
        from core.models import BeerStyle, Recipe
        from django.apps import apps

        # First run the forward migration
        import importlib
        migration_module = importlib.import_module('core.migrations.0003_auto_20250721_2043')
        create_default_beer_style = migration_module.create_default_beer_style
        create_default_beer_style(apps, None)

        # Verify the forward migration worked
        default_style = BeerStyle.objects.get(name='Custom Style')
        recipes_with_style = Recipe.objects.filter(beer_style=default_style).count()
        assert recipes_with_style == 2

        # Now test the reverse migration
        reverse_default_beer_style = migration_module.reverse_default_beer_style
        reverse_default_beer_style(apps, None)

        # The default style should be deleted
        assert not BeerStyle.objects.filter(name='Custom Style').exists()

        # Recipes should have beer_style set to None
        recipes_without_style = Recipe.objects.filter(beer_style__isnull=True).count()
        assert recipes_without_style == 2

    def test_reverse_migration_handles_missing_style(self):
        """Test that reverse migration handles case where default style doesn't exist."""
        from core.models import BeerStyle
        from django.apps import apps

        # Make sure no default style exists
        BeerStyle.objects.filter(name='Custom Style').delete()

        # Running reverse migration should not raise an error
        import importlib
        migration_module = importlib.import_module('core.migrations.0003_auto_20250721_2043')
        reverse_default_beer_style = migration_module.reverse_default_beer_style
        try:
            reverse_default_beer_style(apps, None)
        except Exception as e:
            pytest.fail(f"Reverse migration raised an exception: {e}")


class TestBeerStyleMigrationIntegration:
    """Integration tests for the beer style migration using Django's migration framework."""

    @pytest.fixture(autouse=True)
    def setup_default_style(self, db):
        """Ensure the default style exists for integration tests."""
        from core.models import BeerStyle

        # Create the default style if it doesn't exist
        BeerStyle.objects.get_or_create(
            name="Custom Style",
            defaults={
                'description': "A custom beer style with no specific guidelines. Perfect for experimental and unique recipes.",
            }
        )

    @pytest.mark.django_db
    def test_migration_applied_correctly(self):
        """Test that the migration was applied correctly in the test database."""
        from core.models import BeerStyle, Recipe

        # The default style should exist
        default_style = BeerStyle.objects.get(name='Custom Style')
        assert default_style.name == 'Custom Style'
        assert 'custom beer style' in default_style.description.lower()

        # Create a new recipe and verify it can be assigned the default style
        recipe = Recipe.objects.create(
            name='Test Recipe',
            batch_size_gallons=5.0,
        )

        recipe.beer_style = default_style
        recipe.save()

        assert recipe.beer_style == default_style

    @pytest.mark.django_db
    def test_default_style_properties(self):
        """Test that the default style has the expected properties."""
        from core.models import BeerStyle

        default_style = BeerStyle.objects.get(name='Custom Style')

        # Should have no defined ranges (all None)
        assert default_style.srm_min is None
        assert default_style.srm_max is None
        assert default_style.ibu_min is None
        assert default_style.ibu_max is None
        assert default_style.og_min is None
        assert default_style.og_max is None
        assert default_style.fg_min is None
        assert default_style.fg_max is None
        assert default_style.abv_min is None
        assert default_style.abv_max is None

        # Display properties should indicate no ranges
        assert 'No SRM range specified' in default_style.srm_range_display
        assert 'No IBU range specified' in default_style.ibu_range_display
        assert 'No OG range specified' in default_style.og_range_display
        assert 'No FG range specified' in default_style.fg_range_display
        assert 'No ABV range specified' in default_style.abv_range_display
