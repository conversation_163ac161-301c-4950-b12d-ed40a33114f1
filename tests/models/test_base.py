"""
Tests for base model functionality.

This module tests the BaseModel class and its core functionality including
ID generation, timestamps, and soft delete functionality.
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime
from django.utils import timezone

from core.models.base import BaseModel, TestModel, SoftDeleteQuerySet, SoftDeleteManager
from tests.base import BaseModelTest
from tests.factories import BaseFactory


class TestModelFactory(BaseFactory):
    """Factory for TestModel objects."""
    model_class = TestModel

    @classmethod
    def get_defaults(cls):
        return {
            'name': 'Test Model Instance',
        }


class TestBaseModel(BaseModelTest):
    """Test BaseModel functionality."""

    factory = TestModelFactory
    model_class = TestModel

    def test_id_generation_with_prefix(self):
        """Test that ID is generated with correct prefix."""
        instance = self.factory.create()
        # Mock the ID generation
        instance._get_id_prefix = Mock(return_value="tes")
        instance.id = "tes_1234567890_abcdef1234567890"

        assert instance.id.startswith("tes_")
        parts = instance.id.split('_')
        assert len(parts) == 3
        assert parts[0] == "tes"
        assert parts[1].isdigit()  # timestamp
        assert len(parts[2]) == 16  # random part

    def test_id_prefix_method(self):
        """Test that _get_id_prefix returns correct prefix."""
        instance = self.factory.create()
        instance._get_id_prefix = Mock(return_value="tes")
        assert instance._get_id_prefix() == "tes"

    @pytest.mark.django_db
    def test_soft_delete_functionality(self):
        """Test soft delete sets deleted_at timestamp."""
        instance = TestModel.objects.create(name="Test Delete")
        assert instance.deleted_at is None

        instance.delete()
        assert instance.deleted_at is not None
        assert instance.is_deleted

    @pytest.mark.django_db
    def test_hard_delete_functionality(self):
        """Test hard delete actually removes the object."""
        instance = TestModel.objects.create(name="Test Hard Delete")
        instance_id = instance.id

        instance.hard_delete()

        # Should not exist in database at all
        with pytest.raises(TestModel.DoesNotExist):
            TestModel.objects.get(id=instance_id)

    @pytest.mark.django_db
    def test_restore_functionality(self):
        """Test restore clears deleted_at timestamp."""
        instance = TestModel.objects.create(name="Test Restore")

        # Soft delete then restore
        instance.delete()
        assert instance.is_deleted

        instance.restore()
        assert not instance.is_deleted
        assert instance.deleted_at is None

    def test_is_deleted_property(self):
        """Test is_deleted property returns correct value."""
        instance = TestModel(name="Test")

        instance.deleted_at = None
        assert not instance.is_deleted

        instance.deleted_at = timezone.now()
        assert instance.is_deleted

    def test_save_generates_id_if_not_set(self):
        """Test that save generates ID if not already set."""
        instance = TestModel(name="Test")
        instance.id = None

        with patch.object(instance, '_generate_id', return_value="tes_123_abc") as mock_gen:
            with patch('django.db.models.Model.save') as mock_save:
                instance.save()
                mock_gen.assert_called_once()
                assert instance.id == "tes_123_abc"

    def test_save_does_not_regenerate_existing_id(self):
        """Test that save does not regenerate existing ID."""
        instance = TestModel(name="Test")
        instance.id = "existing_id"

        with patch.object(instance, '_generate_id') as mock_gen:
            with patch('django.db.models.Model.save') as mock_save:
                instance.save()
                mock_gen.assert_not_called()
                assert instance.id == "existing_id"


class TestSoftDeleteQuerySet:
    """Test SoftDeleteQuerySet functionality."""

    def setup_method(self):
        """Set up test method."""
        self.queryset = Mock(spec=SoftDeleteQuerySet)
        # Mock the methods we're testing
        self.queryset.update = Mock()
        self.queryset.filter = Mock(return_value=self.queryset)

    def test_delete_sets_deleted_at(self):
        """Test that delete sets deleted_at timestamp."""
        # Create a real instance to test the method
        qs = SoftDeleteQuerySet()
        qs.update = Mock()

        qs.delete()
        qs.update.assert_called_once()
        # Check that update was called with deleted_at
        call_args = qs.update.call_args[1]
        assert 'deleted_at' in call_args
        assert call_args['deleted_at'] is not None

    def test_alive_filters_non_deleted(self):
        """Test that alive() filters non-deleted objects."""
        qs = SoftDeleteQuerySet()
        qs.filter = Mock(return_value=qs)

        result = qs.alive()
        qs.filter.assert_called_once_with(deleted_at__isnull=True)
        assert result == qs

    def test_dead_filters_deleted(self):
        """Test that dead() filters deleted objects."""
        qs = SoftDeleteQuerySet()
        qs.filter = Mock(return_value=qs)

        result = qs.dead()
        qs.filter.assert_called_once_with(deleted_at__isnull=False)
        assert result == qs


class TestSoftDeleteManager:
    """Test SoftDeleteManager functionality."""

    def test_get_queryset_returns_soft_delete_queryset(self):
        """Test that get_queryset returns SoftDeleteQuerySet."""
        manager = SoftDeleteManager()

        with patch('core.models.base.SoftDeleteQuerySet') as mock_qs_class:
            mock_qs = Mock()
            mock_qs_class.return_value = mock_qs

            # Mock the model to avoid database calls
            manager.model = Mock()

            result = manager.get_queryset()
            mock_qs_class.assert_called_once_with(manager.model, using=manager._db)


class TestTestModel:
    """Test the TestModel implementation."""

    def test_test_model_id_prefix(self):
        """Test that TestModel has correct ID prefix."""
        model = TestModel()
        assert model._get_id_prefix() == "tes"

    def test_test_model_str_representation(self):
        """Test TestModel string representation."""
        model = TestModel(name="Test Name")
        assert str(model) == "Test Name"

    @pytest.mark.django_db
    def test_test_model_can_be_created_in_db(self):
        """Test that TestModel can be created in database."""
        model = TestModel.objects.create(name="Database Test")
        assert model.id is not None
        assert model.name == "Database Test"
        assert model.created_at is not None
        assert model.updated_at is not None
        assert model.deleted_at is None

    @pytest.mark.django_db
    def test_test_model_soft_delete_in_db(self):
        """Test soft delete functionality with database."""
        model = TestModel.objects.create(name="To Delete")
        model_id = model.id

        # Soft delete
        model.delete()

        # Should still exist in database but be marked as deleted
        # Need to use all_with_deleted() to bypass the default manager filtering
        all_models = TestModel.objects.all_with_deleted()
        deleted_model = None
        for m in all_models:
            if m.id == model_id:
                deleted_model = m
                break

        assert deleted_model is not None
        assert deleted_model.deleted_at is not None
        assert deleted_model.is_deleted

        # Should not appear in alive() queryset
        alive_models = list(TestModel.objects.alive())
        assert deleted_model not in alive_models

        # Should appear in dead() queryset
        dead_models = list(TestModel.objects.dead())
        assert deleted_model in dead_models

    @pytest.mark.django_db
    def test_test_model_restore_in_db(self):
        """Test restore functionality with database."""
        model = TestModel.objects.create(name="To Restore")

        # Soft delete then restore
        model.delete()
        assert model.is_deleted

        model.restore()
        assert not model.is_deleted
        assert model.deleted_at is None

        # Should appear in alive() queryset again
        alive_models = TestModel.objects.alive()
        assert model in alive_models
