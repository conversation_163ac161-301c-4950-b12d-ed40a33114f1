"""
Tests for BeerStyle model functionality.

This module tests the BeerStyle model including field validation,
property methods, recipe matching logic, and BaseModel inheritance.
"""

import pytest
from django.core.exceptions import ValidationError
from unittest.mock import Mock, patch

from core.models import BeerStyle, Recipe
from tests.base import BaseModelTest
from tests.factories import BeerStyleFactory, RecipeFactory


class TestBeerStyle(BaseModelTest):
    """Test the BeerStyle model class."""

    factory = BeerStyleFactory
    model_class = BeerStyle

    def setup_method(self):
        """Set up test method."""
        self.beer_style = BeerStyleFactory.create_in_db()

    def test_beer_style_creation(self):
        """Test basic BeerStyle creation."""
        assert self.beer_style.name == "American IPA"
        assert self.beer_style.description == "A hoppy American beer style"
        assert self.beer_style.srm_min == 6.0
        assert self.beer_style.srm_max == 14.0
        assert self.beer_style.ibu_min == 40.0
        assert self.beer_style.ibu_max == 70.0

    def test_beer_style_str_representation(self):
        """Test string representation of BeerStyle."""
        assert str(self.beer_style) == "American IPA"

    def test_beer_style_id_prefix(self):
        """Test that BeerStyle uses correct ID prefix."""
        assert self.beer_style.id.startswith("bst_")

    def test_beer_style_ordering(self):
        """Test that beer styles are ordered by name."""
        style2 = BeerStyle.objects.create(name="Stout")
        style3 = BeerStyle.objects.create(name="Lager")

        styles = list(BeerStyle.objects.all())
        style_names = [style.name for style in styles]

        assert style_names == ["American IPA", "Lager", "Stout"]

    def test_srm_range_display(self):
        """Test SRM range display property."""
        assert self.beer_style.srm_range_display == "6.0-14.0 SRM"

        # Test with only min value
        style_min_only = BeerStyle.objects.create(
            name="Test Min", srm_min=5.0
        )
        assert style_min_only.srm_range_display == "5.0+ SRM"

        # Test with only max value
        style_max_only = BeerStyle.objects.create(
            name="Test Max", srm_max=10.0
        )
        assert style_max_only.srm_range_display == "≤10.0 SRM"

        # Test with no values
        style_no_range = BeerStyle.objects.create(name="Test None")
        assert style_no_range.srm_range_display == "No SRM range specified"

    def test_ibu_range_display(self):
        """Test IBU range display property."""
        assert self.beer_style.ibu_range_display == "40.0-70.0 IBU"

        # Test with only min value
        style_min_only = BeerStyle.objects.create(
            name="Test Min IBU", ibu_min=20.0
        )
        assert style_min_only.ibu_range_display == "20.0+ IBU"

        # Test with only max value
        style_max_only = BeerStyle.objects.create(
            name="Test Max IBU", ibu_max=30.0
        )
        assert style_max_only.ibu_range_display == "≤30.0 IBU"

        # Test with no values
        style_no_range = BeerStyle.objects.create(name="Test None IBU")
        assert style_no_range.ibu_range_display == "No IBU range specified"

    def test_og_range_display(self):
        """Test original gravity range display property."""
        assert self.beer_style.og_range_display == "1.056-1.070 OG"

        # Test with only min value
        style_min_only = BeerStyle.objects.create(
            name="Test Min OG", og_min=1.040
        )
        assert style_min_only.og_range_display == "1.040+ OG"

        # Test with only max value
        style_max_only = BeerStyle.objects.create(
            name="Test Max OG", og_max=1.050
        )
        assert style_max_only.og_range_display == "≤1.050 OG"

        # Test with no values
        style_no_range = BeerStyle.objects.create(name="Test None OG")
        assert style_no_range.og_range_display == "No OG range specified"

    def test_fg_range_display(self):
        """Test final gravity range display property."""
        assert self.beer_style.fg_range_display == "1.008-1.014 FG"

        # Test with only min value
        style_min_only = BeerStyle.objects.create(
            name="Test Min FG", fg_min=1.005
        )
        assert style_min_only.fg_range_display == "1.005+ FG"

        # Test with only max value
        style_max_only = BeerStyle.objects.create(
            name="Test Max FG", fg_max=1.020
        )
        assert style_max_only.fg_range_display == "≤1.020 FG"

        # Test with no values
        style_no_range = BeerStyle.objects.create(name="Test None FG")
        assert style_no_range.fg_range_display == "No FG range specified"

    def test_abv_range_display(self):
        """Test ABV range display property."""
        assert self.beer_style.abv_range_display == "5.5-7.5% ABV"

        # Test with only min value
        style_min_only = BeerStyle.objects.create(
            name="Test Min ABV", abv_min=4.0
        )
        assert style_min_only.abv_range_display == "4.0+ % ABV"

        # Test with only max value
        style_max_only = BeerStyle.objects.create(
            name="Test Max ABV", abv_max=8.0
        )
        assert style_max_only.abv_range_display == "≤8.0% ABV"

        # Test with no values
        style_no_range = BeerStyle.objects.create(name="Test None ABV")
        assert style_no_range.abv_range_display == "No ABV range specified"


    def test_beer_style_inherits_from_base_model(self):
        """Test that BeerStyle inherits BaseModel functionality."""
        # Test soft delete
        style_id = self.beer_style.id
        self.beer_style.delete()

        # Should be soft deleted
        assert self.beer_style.is_deleted
        assert self.beer_style.deleted_at is not None

        # Should not appear in default queryset
        assert not BeerStyle.objects.filter(id=style_id).exists()

        # Should appear in all_with_deleted queryset
        assert BeerStyle.objects.all_with_deleted().filter(id=style_id).exists()

        # Test restore
        self.beer_style.restore()
        assert not self.beer_style.is_deleted
        assert self.beer_style.deleted_at is None
        assert BeerStyle.objects.filter(id=style_id).exists()

    def test_beer_style_timestamps(self):
        """Test that BeerStyle has proper timestamps."""
        assert self.beer_style.created_at is not None
        assert self.beer_style.updated_at is not None

        original_updated = self.beer_style.updated_at

        # Update the style
        self.beer_style.description = "Updated description"
        self.beer_style.save()

        # updated_at should change
        assert self.beer_style.updated_at > original_updated
