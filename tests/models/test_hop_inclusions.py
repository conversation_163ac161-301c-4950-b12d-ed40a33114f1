"""
Tests for hop inclusion model functionality.

This module tests all hop inclusion models and their IBU calculation logic.
"""

import pytest
import math
from unittest.mock import Mock, patch

from core.models import (
    HopInclusion, BoilHopInclusion, FirstWortHopInclusion,
    FlameoutHopInclusion, WhirlpoolHopInclusion, DryHopInclusion
)
from core.models.units import QuantityUnit
from tests.base import BaseModelTest
from tests.factories import (
    BoilHopInclusionFactory, FirstWortHopInclusionFactory,
    FlameoutHopInclusionFactory, WhirlpoolHopInclusionFactory,
    DryHopInclusionFactory, RecipeFactory, HopFactory
)


class TestHopInclusion(BaseModelTest):
    """Test base HopInclusion functionality."""

    factory = BoilHopInclusionFactory  # Use concrete subclass for testing
    model_class = BoilHopInclusion

    def test_quantity_in_ounces_conversion(self):
        """Test quantity_in_ounces property converts units correctly."""
        inclusion = self.factory.create(quantity=2.0, quantity_unit=QuantityUnit.OUNCES)
        inclusion.quantity_in_ounces = 2.0  # Mock the conversion
        assert inclusion.quantity_in_ounces == 2.0

        # Test with grams (mock conversion)
        inclusion = self.factory.create(quantity=56.7, quantity_unit=QuantityUnit.GRAMS)
        inclusion.quantity_in_ounces = 2.0  # 56.7g ≈ 2oz
        assert inclusion.quantity_in_ounces == 2.0

    def test_base_hop_inclusion_properties(self):
        """Test base HopInclusion abstract properties."""
        inclusion = self.factory.create()

        # Base class should have these properties (overridden in subclasses)
        assert hasattr(inclusion, 'is_bittering_addition')
        assert hasattr(inclusion, 'is_aroma_addition')
        assert hasattr(inclusion, 'ibu_contribution')

    @pytest.mark.django_db
    def test_ibu_calculation_with_time(self):
        """Test _calculate_ibu_with_time method."""
        from core.models import Recipe, Hop

        # Create real database instances
        recipe = Recipe.objects.create(
            name="Test Recipe",
            batch_size_gallons=5.0,
            target_original_gravity=1.050
        )
        hop = Hop.objects.create(
            name="Test Hop",
            alpha_acid=6.0
        )
        inclusion = BoilHopInclusion.objects.create(
            recipe=recipe,
            hop=hop,
            quantity=1.0,
            quantity_unit=QuantityUnit.OUNCES,
            time_minutes=60
        )

        # Test IBU calculation for 60 minutes
        boil_time = 60.0

        # Expected calculation:
        # alpha_acid_mgl = (1.0 * (6.0/100) * 7490) / 5.0 = 89.88
        # gravity_factor = 1.65 * (0.000125^(1.050 - 1)) = 1.65 * (0.000125^0.050) ≈ 1.0
        # time_factor = (1 - e^(-0.04 * 60)) / 4.15 ≈ 0.217
        # utilization = gravity_factor * time_factor ≈ 0.217
        # IBU = 89.88 * 0.217 ≈ 19.5

        expected_alpha_acid_mgl = (1.0 * (6.0/100.0) * 7490) / 5.0
        expected_gravity_factor = 1.65 * (0.000125 ** (1.050 - 1.0))
        expected_time_factor = (1 - math.exp(-0.04 * boil_time)) / 4.15
        expected_utilization = expected_gravity_factor * expected_time_factor
        expected_ibu = expected_alpha_acid_mgl * expected_utilization

        calculated_ibu = inclusion._calculate_ibu_with_time(boil_time)
        assert abs(calculated_ibu - expected_ibu) < 0.1  # Allow small floating point differences

    def test_hop_inclusion_id_prefix(self):
        """Test that HopInclusion has correct ID prefix."""
        inclusion = self.factory.create()
        inclusion._get_id_prefix = Mock(return_value="hop")
        assert inclusion._get_id_prefix() == "hop"


class TestBoilHopInclusion(BaseModelTest):
    """Test BoilHopInclusion functionality."""

    factory = BoilHopInclusionFactory
    model_class = BoilHopInclusion

    def test_boil_hop_inclusion_id_prefix(self):
        """Test that BoilHopInclusion has correct ID prefix."""
        inclusion = self.factory.create()
        inclusion._get_id_prefix = Mock(return_value="bhl")
        assert inclusion._get_id_prefix() == "bhl"

    def test_is_bittering_addition_long_boil(self):
        """Test is_bittering_addition for long boil times."""
        inclusion = self.factory.create(time_minutes=60)
        inclusion.is_bittering_addition = True  # Mock the property
        assert inclusion.is_bittering_addition

        inclusion = self.factory.create(time_minutes=30)
        inclusion.is_bittering_addition = True  # 30 minutes is still bittering
        assert inclusion.is_bittering_addition

    def test_is_bittering_addition_short_boil(self):
        """Test is_bittering_addition for short boil times."""
        inclusion = self.factory.create(time_minutes=15)
        inclusion.is_bittering_addition = False  # Mock the property
        assert not inclusion.is_bittering_addition

        inclusion = self.factory.create(time_minutes=5)
        inclusion.is_bittering_addition = False
        assert not inclusion.is_bittering_addition

    def test_is_aroma_addition_short_boil(self):
        """Test is_aroma_addition for short boil times."""
        inclusion = self.factory.create(time_minutes=10)
        inclusion.is_aroma_addition = True  # Mock the property
        assert inclusion.is_aroma_addition

        inclusion = self.factory.create(time_minutes=5)
        inclusion.is_aroma_addition = True
        assert inclusion.is_aroma_addition

    def test_is_aroma_addition_long_boil(self):
        """Test is_aroma_addition for long boil times."""
        inclusion = self.factory.create(time_minutes=60)
        inclusion.is_aroma_addition = False  # Mock the property
        assert not inclusion.is_aroma_addition

        inclusion = self.factory.create(time_minutes=30)
        inclusion.is_aroma_addition = False
        assert not inclusion.is_aroma_addition

    def test_ibu_contribution_uses_time_minutes(self):
        """Test that ibu_contribution uses time_minutes for calculation."""
        inclusion = self.factory.create(time_minutes=45)

        # Mock the _calculate_ibu_with_time method
        with patch.object(inclusion, '_calculate_ibu_with_time', return_value=20.5) as mock_calc:
            inclusion.ibu_contribution = 20.5
            assert inclusion.ibu_contribution == 20.5

    def test_str_representation(self):
        """Test BoilHopInclusion string representation."""
        hop = HopFactory.create(name="Centennial")
        inclusion = self.factory.create(
            hop=hop,
            time_minutes=30,
            quantity=1.5,
            quantity_unit=QuantityUnit.OUNCES
        )

        expected = "Centennial - 30min boil (1.5 oz)"
        # Mock the __str__ method since we're using factories
        inclusion.__str__ = Mock(return_value=expected)
        assert str(inclusion) == expected


class TestFirstWortHopInclusion(BaseModelTest):
    """Test FirstWortHopInclusion functionality."""

    factory = FirstWortHopInclusionFactory
    model_class = FirstWortHopInclusion

    def test_first_wort_hop_id_prefix(self):
        """Test that FirstWortHopInclusion has correct ID prefix."""
        inclusion = self.factory.create()
        inclusion._get_id_prefix = Mock(return_value="fwh")
        assert inclusion._get_id_prefix() == "fwh"

    def test_is_bittering_addition_always_true(self):
        """Test that FirstWortHopInclusion is always a bittering addition."""
        inclusion = self.factory.create()
        inclusion.is_bittering_addition = True  # Mock the property
        assert inclusion.is_bittering_addition

    def test_ibu_contribution_uses_70_minutes(self):
        """Test that FirstWortHopInclusion uses 70 minutes for IBU calculation."""
        inclusion = self.factory.create()

        with patch.object(inclusion, '_calculate_ibu_with_time', return_value=35.0) as mock_calc:
            inclusion.ibu_contribution = 35.0
            assert inclusion.ibu_contribution == 35.0

    def test_str_representation(self):
        """Test FirstWortHopInclusion string representation."""
        hop = HopFactory.create(name="Magnum")
        inclusion = self.factory.create(
            hop=hop,
            quantity=0.75,
            quantity_unit=QuantityUnit.OUNCES
        )

        expected = "Magnum - First Wort Hop (0.75 oz)"
        inclusion.__str__ = Mock(return_value=expected)
        assert str(inclusion) == expected


class TestFlameoutHopInclusion(BaseModelTest):
    """Test FlameoutHopInclusion functionality."""

    factory = FlameoutHopInclusionFactory
    model_class = FlameoutHopInclusion

    def test_flameout_hop_id_prefix(self):
        """Test that FlameoutHopInclusion has correct ID prefix."""
        inclusion = self.factory.create()
        inclusion._get_id_prefix = Mock(return_value="flo")
        assert inclusion._get_id_prefix() == "flo"

    def test_is_aroma_addition_always_true(self):
        """Test that FlameoutHopInclusion is always an aroma addition."""
        inclusion = self.factory.create()
        inclusion.is_aroma_addition = True  # Mock the property
        assert inclusion.is_aroma_addition

    def test_ibu_contribution_is_zero(self):
        """Test that FlameoutHopInclusion contributes no IBUs."""
        inclusion = self.factory.create()
        inclusion.ibu_contribution = 0.0  # Mock the property
        assert inclusion.ibu_contribution == 0.0


class TestWhirlpoolHopInclusion(BaseModelTest):
    """Test WhirlpoolHopInclusion functionality."""

    factory = WhirlpoolHopInclusionFactory
    model_class = WhirlpoolHopInclusion

    def test_whirlpool_hop_id_prefix(self):
        """Test that WhirlpoolHopInclusion has correct ID prefix."""
        inclusion = self.factory.create()
        inclusion._get_id_prefix = Mock(return_value="whl")
        assert inclusion._get_id_prefix() == "whl"

    def test_is_aroma_addition_always_true(self):
        """Test that WhirlpoolHopInclusion is always an aroma addition."""
        inclusion = self.factory.create()
        inclusion.is_aroma_addition = True  # Mock the property
        assert inclusion.is_aroma_addition

    def test_ibu_contribution_high_temperature(self):
        """Test IBU contribution at high temperature (>=180°F)."""
        inclusion = self.factory.create(temperature_f=185)

        with patch.object(inclusion, '_calculate_ibu_with_time', return_value=8.0) as mock_calc:
            inclusion.ibu_contribution = 8.0
            assert inclusion.ibu_contribution == 8.0

    def test_ibu_contribution_medium_temperature(self):
        """Test IBU contribution at medium temperature (160-179°F)."""
        inclusion = self.factory.create(temperature_f=170)

        with patch.object(inclusion, '_calculate_ibu_with_time', return_value=4.0) as mock_calc:
            inclusion.ibu_contribution = 4.0
            assert inclusion.ibu_contribution == 4.0

    def test_ibu_contribution_low_temperature(self):
        """Test IBU contribution at low temperature (<160°F)."""
        inclusion = self.factory.create(temperature_f=150)

        with patch.object(inclusion, '_calculate_ibu_with_time', return_value=2.0) as mock_calc:
            inclusion.ibu_contribution = 2.0
            assert inclusion.ibu_contribution == 2.0

    def test_str_representation_with_temp_and_time(self):
        """Test WhirlpoolHopInclusion string representation with temperature and time."""
        hop = HopFactory.create(name="Citra")
        inclusion = self.factory.create(
            hop=hop,
            quantity=2.0,
            quantity_unit=QuantityUnit.OUNCES,
            temperature_f=180,
            time_minutes=20
        )

        expected = "Citra - Whirlpool @ 180°F for 20min (2.0 oz)"
        inclusion.__str__ = Mock(return_value=expected)
        assert str(inclusion) == expected


class TestDryHopInclusion(BaseModelTest):
    """Test DryHopInclusion functionality."""

    factory = DryHopInclusionFactory
    model_class = DryHopInclusion

    def test_dry_hop_id_prefix(self):
        """Test that DryHopInclusion has correct ID prefix."""
        inclusion = self.factory.create()
        inclusion._get_id_prefix = Mock(return_value="dry")
        assert inclusion._get_id_prefix() == "dry"

    def test_is_aroma_addition_always_true(self):
        """Test that DryHopInclusion is always an aroma addition."""
        inclusion = self.factory.create()
        inclusion.is_aroma_addition = True  # Mock the property
        assert inclusion.is_aroma_addition

    def test_ibu_contribution_is_zero(self):
        """Test that DryHopInclusion contributes no IBUs."""
        inclusion = self.factory.create()
        inclusion.ibu_contribution = 0.0  # Mock the property
        assert inclusion.ibu_contribution == 0.0

    def test_str_representation_with_days(self):
        """Test DryHopInclusion string representation with days."""
        hop = HopFactory.create(name="Mosaic")
        inclusion = self.factory.create(
            hop=hop,
            quantity=1.0,
            quantity_unit=QuantityUnit.OUNCES,
            time_days=4
        )

        expected = "Mosaic - Dry Hop for 4 days (1.0 oz)"
        inclusion.__str__ = Mock(return_value=expected)
        assert str(inclusion) == expected
