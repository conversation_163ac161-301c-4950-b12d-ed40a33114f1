"""
Tests for ingredient model functionality.

This module tests the Hop and Fermentable models and their business logic.
"""

import pytest
from unittest.mock import Mock

from core.models import Hop, Fermentable, FermentableType
from tests.base import BaseModelTest
from tests.factories import HopFactory, FermentableFactory


class TestHop(BaseModelTest):
    """Test Hop model functionality."""
    
    factory = HopFactory
    model_class = Hop
    
    def test_hop_id_prefix(self):
        """Test that Hop has correct ID prefix."""
        hop = Hop(name="Test Hop")
        assert hop._get_id_prefix() == "hop"
    
    def test_hop_str_representation(self):
        """Test Hop string representation."""
        hop = Hop(name="Cascade")
        assert str(hop) == "Cascade"
    
    @pytest.mark.django_db
    def test_hop_creation_in_db(self):
        """Test Hop creation in database."""
        hop = Hop.objects.create(
            name="Centennial",
            country_of_origin="USA",
            alpha_acid=10.0,
            beta_acid=3.5,
            aroma=True,
            bittering=True,
            notes="Citrusy hop with floral notes"
        )
        
        assert hop.id is not None
        assert hop.id.startswith("hop_")
        assert hop.name == "Centennial"
        assert hop.country_of_origin == "USA"
        assert hop.alpha_acid == 10.0
        assert hop.beta_acid == 3.5
        assert hop.aroma is True
        assert hop.bittering is True
        assert hop.notes == "Citrusy hop with floral notes"
    
    def test_hop_default_values(self):
        """Test Hop model default values."""
        hop = Hop(name="Test Hop")
        
        assert hop.country_of_origin == ""
        assert hop.notes == ""
        assert hop.alpha_acid == 0.0
        assert hop.beta_acid == 0.0
        assert hop.aroma is False
        assert hop.bittering is False
    
    @pytest.mark.django_db
    def test_hop_alpha_acid_validation(self):
        """Test hop alpha acid values."""
        # Test normal alpha acid values
        hop = Hop.objects.create(
            name="Test Hop",
            alpha_acid=12.5
        )
        assert hop.alpha_acid == 12.5
        
        # Test zero alpha acid (valid for some hops)
        hop_zero = Hop.objects.create(
            name="Zero Alpha Hop",
            alpha_acid=0.0
        )
        assert hop_zero.alpha_acid == 0.0
    
    @pytest.mark.django_db
    def test_hop_beta_acid_validation(self):
        """Test hop beta acid values."""
        hop = Hop.objects.create(
            name="Test Hop",
            beta_acid=4.2
        )
        assert hop.beta_acid == 4.2
    
    @pytest.mark.django_db
    def test_hop_aroma_bittering_flags(self):
        """Test hop aroma and bittering boolean flags."""
        # Aroma hop
        aroma_hop = Hop.objects.create(
            name="Aroma Hop",
            aroma=True,
            bittering=False
        )
        assert aroma_hop.aroma is True
        assert aroma_hop.bittering is False
        
        # Bittering hop
        bittering_hop = Hop.objects.create(
            name="Bittering Hop",
            aroma=False,
            bittering=True
        )
        assert bittering_hop.aroma is False
        assert bittering_hop.bittering is True
        
        # Dual purpose hop
        dual_hop = Hop.objects.create(
            name="Dual Purpose Hop",
            aroma=True,
            bittering=True
        )
        assert dual_hop.aroma is True
        assert dual_hop.bittering is True


class TestFermentable(BaseModelTest):
    """Test Fermentable model functionality."""
    
    factory = FermentableFactory
    model_class = Fermentable
    
    def test_fermentable_id_prefix(self):
        """Test that Fermentable has correct ID prefix."""
        fermentable = Fermentable(name="Test Malt")
        assert fermentable._get_id_prefix() == "frm"
    
    def test_fermentable_str_representation(self):
        """Test Fermentable string representation."""
        fermentable = Fermentable(name="Pilsner Malt")
        assert str(fermentable) == "Pilsner Malt"
    
    @pytest.mark.django_db
    def test_fermentable_creation_in_db(self):
        """Test Fermentable creation in database."""
        fermentable = Fermentable.objects.create(
            name="Munich Malt",
            country_of_origin="Germany",
            fermentable_type=FermentableType.SPECIALTY_MALT,
            extract_potential_ppg=37.0,
            color_lovibond=10.0,
            requires_mashing=True,
            notes="Rich malty flavor"
        )
        
        assert fermentable.id is not None
        assert fermentable.id.startswith("frm_")
        assert fermentable.name == "Munich Malt"
        assert fermentable.country_of_origin == "Germany"
        assert fermentable.fermentable_type == FermentableType.SPECIALTY_MALT
        assert fermentable.extract_potential_ppg == 37.0
        assert fermentable.color_lovibond == 10.0
        assert fermentable.requires_mashing is True
        assert fermentable.notes == "Rich malty flavor"
    
    def test_fermentable_default_values(self):
        """Test Fermentable model default values."""
        fermentable = Fermentable(name="Test Malt")
        
        assert fermentable.country_of_origin == ""
        assert fermentable.notes == ""
        assert fermentable.fermentable_type == FermentableType.BASE_MALT
        assert fermentable.extract_potential_ppg == 37.0
        assert fermentable.color_lovibond == 2.0
        assert fermentable.requires_mashing is True
    
    @pytest.mark.django_db
    def test_fermentable_type_choices(self):
        """Test fermentable type choices."""
        # Test each fermentable type
        types_to_test = [
            FermentableType.BASE_MALT,
            FermentableType.SPECIALTY_MALT,
            FermentableType.CRYSTAL_CARAMEL,
            FermentableType.ROASTED,
            FermentableType.ADJUNCT,
            FermentableType.EXTRACT,
        ]
        
        for fermentable_type in types_to_test:
            fermentable = Fermentable.objects.create(
                name=f"Test {fermentable_type}",
                fermentable_type=fermentable_type
            )
            assert fermentable.fermentable_type == fermentable_type
    
    @pytest.mark.django_db
    def test_extract_potential_values(self):
        """Test extract potential values."""
        # Base malt
        base_malt = Fermentable.objects.create(
            name="Base Malt",
            extract_potential_ppg=37.0
        )
        assert base_malt.extract_potential_ppg == 37.0
        
        # Extract
        extract = Fermentable.objects.create(
            name="Liquid Extract",
            extract_potential_ppg=36.0,
            fermentable_type=FermentableType.EXTRACT
        )
        assert extract.extract_potential_ppg == 36.0
    
    @pytest.mark.django_db
    def test_color_lovibond_values(self):
        """Test color Lovibond values."""
        # Light malt
        light_malt = Fermentable.objects.create(
            name="Pilsner Malt",
            color_lovibond=1.8
        )
        assert light_malt.color_lovibond == 1.8
        
        # Dark malt
        dark_malt = Fermentable.objects.create(
            name="Chocolate Malt",
            color_lovibond=350.0
        )
        assert dark_malt.color_lovibond == 350.0
    
    @pytest.mark.django_db
    def test_requires_mashing_flag(self):
        """Test requires mashing boolean flag."""
        # Malt that requires mashing
        base_malt = Fermentable.objects.create(
            name="Base Malt",
            requires_mashing=True
        )
        assert base_malt.requires_mashing is True
        
        # Extract that doesn't require mashing
        extract = Fermentable.objects.create(
            name="Liquid Extract",
            requires_mashing=False,
            fermentable_type=FermentableType.EXTRACT
        )
        assert extract.requires_mashing is False


class TestFermentableType:
    """Test FermentableType choices."""
    
    def test_fermentable_type_choices_exist(self):
        """Test that all expected fermentable type choices exist."""
        expected_choices = [
            FermentableType.BASE_MALT,
            FermentableType.SPECIALTY_MALT,
            FermentableType.CRYSTAL_CARAMEL,
            FermentableType.ROASTED,
            FermentableType.ADJUNCT,
            FermentableType.EXTRACT,
        ]
        
        for choice in expected_choices:
            assert choice in FermentableType.values
    
    def test_fermentable_type_labels(self):
        """Test fermentable type choice labels."""
        assert FermentableType.BASE_MALT.label == "Base Malt"
        assert FermentableType.SPECIALTY_MALT.label == "Specialty Malt"
        assert FermentableType.CRYSTAL_CARAMEL.label == "Crystal/Caramel Malt"
        assert FermentableType.ROASTED.label == "Roasted Malt"
        assert FermentableType.ADJUNCT.label == "Adjunct"
        assert FermentableType.EXTRACT.label == "Malt Extract"
