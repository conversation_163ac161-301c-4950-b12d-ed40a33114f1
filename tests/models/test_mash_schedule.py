"""
Tests for mash schedule model functionality.

This module tests the MashStep model and its business logic.
"""

import pytest
from unittest.mock import Mock

from core.models import MashStep, Recipe
from tests.base import BaseModelTest
from tests.factories import MashStepFactory


class TestMashStep(BaseModelTest):
    """Test MashStep model functionality."""

    factory = MashStepFactory
    model_class = MashStep

    def test_mash_step_id_prefix(self):
        """Test that MashStep has correct ID prefix."""
        step = MashStep(name="Test Step")
        assert step._get_id_prefix() == "msh"

    def test_mash_step_str_representation(self):
        """Test MashStep string representation."""
        step = MashStep(
            name="Protein Rest",
            temperature_fahrenheit=122.0,
            duration_minutes=20
        )
        expected = "Protein Rest - 122.0°F for 20 min"
        assert str(step) == expected

    @pytest.mark.django_db
    def test_mash_step_creation_in_db(self):
        """Test MashStep creation in database."""
        recipe = Recipe.objects.create(name="Test Recipe")

        step = MashStep.objects.create(
            recipe=recipe,
            name="Saccharification Rest",
            step_type="INFUSION",
            temperature_fahrenheit=152.0,
            duration_minutes=60,
            step_order=1,
            description="Main conversion step"
        )

        assert step.id is not None
        assert step.id.startswith("msh_")
        assert step.recipe == recipe
        assert step.name == "Saccharification Rest"
        assert step.step_type == "INFUSION"
        assert step.temperature_fahrenheit == 152.0
        assert step.duration_minutes == 60
        assert step.step_order == 1
        assert step.description == "Main conversion step"

    def test_mash_step_default_values(self):
        """Test MashStep model default values."""
        step = MashStep(name="Test Step")

        assert step.step_type == "INFUSION"
        # temperature_fahrenheit and duration_minutes have no defaults, so they'll be None
        assert step.temperature_fahrenheit is None
        assert step.duration_minutes is None
        assert step.step_order is None
        assert step.description == ""

    @pytest.mark.django_db
    def test_step_type_choices(self):
        """Test mash step type choices."""
        recipe = Recipe.objects.create(name="Test Recipe")

        # Test different step types
        step_types = [
            "INFUSION",
            "TEMPERATURE",
            "DECOCTION"
        ]

        for i, step_type in enumerate(step_types, 1):
            step = MashStep.objects.create(
                recipe=recipe,
                name=f"Test {step_type} Step",
                step_type=step_type,
                temperature_fahrenheit=150.0,
                duration_minutes=30,
                step_order=i
            )
            assert step.step_type == step_type

    @pytest.mark.django_db
    def test_temperature_fahrenheit_field(self):
        """Test temperature_fahrenheit field."""
        recipe = Recipe.objects.create(name="Test Recipe")

        step = MashStep.objects.create(
            recipe=recipe,
            name="Test Step",
            temperature_fahrenheit=152.0,
            duration_minutes=60,
            step_order=1
        )

        assert step.temperature_fahrenheit == 152.0

    @pytest.mark.django_db
    def test_duration_minutes_field(self):
        """Test duration_minutes field."""
        recipe = Recipe.objects.create(name="Test Recipe")

        step = MashStep.objects.create(
            recipe=recipe,
            name="Test Step",
            temperature_fahrenheit=152.0,
            duration_minutes=45,
            step_order=1
        )

        assert step.duration_minutes == 45

    @pytest.mark.django_db
    def test_mash_step_ordering(self):
        """Test mash step ordering by step_order."""
        recipe = Recipe.objects.create(name="Test Recipe")

        # Create steps out of order
        step3 = MashStep.objects.create(
            recipe=recipe,
            name="Mash Out",
            temperature_fahrenheit=168.0,
            duration_minutes=10,
            step_order=3
        )

        step1 = MashStep.objects.create(
            recipe=recipe,
            name="Protein Rest",
            temperature_fahrenheit=122.0,
            duration_minutes=20,
            step_order=1
        )

        step2 = MashStep.objects.create(
            recipe=recipe,
            name="Saccharification",
            temperature_fahrenheit=152.0,
            duration_minutes=60,
            step_order=2
        )

        # Get steps ordered by step_order
        ordered_steps = recipe.mashstep_set.order_by('step_order')

        assert list(ordered_steps) == [step1, step2, step3]
        assert ordered_steps[0].name == "Protein Rest"
        assert ordered_steps[1].name == "Saccharification"
        assert ordered_steps[2].name == "Mash Out"

    @pytest.mark.django_db
    def test_mash_step_temperature_ranges(self):
        """Test various mash step temperature ranges."""
        recipe = Recipe.objects.create(name="Test Recipe")

        # Protein rest temperature
        protein_step = MashStep.objects.create(
            recipe=recipe,
            name="Protein Rest",
            temperature_fahrenheit=122.0,
            duration_minutes=20,
            step_order=1
        )
        assert protein_step.temperature_fahrenheit == 122.0

        # Beta amylase temperature
        beta_step = MashStep.objects.create(
            recipe=recipe,
            name="Beta Amylase Rest",
            temperature_fahrenheit=145.0,
            duration_minutes=30,
            step_order=2
        )
        assert beta_step.temperature_fahrenheit == 145.0

        # Alpha amylase temperature
        alpha_step = MashStep.objects.create(
            recipe=recipe,
            name="Alpha Amylase Rest",
            temperature_fahrenheit=158.0,
            duration_minutes=30,
            step_order=3
        )
        assert alpha_step.temperature_fahrenheit == 158.0

        # Mash out temperature
        mashout_step = MashStep.objects.create(
            recipe=recipe,
            name="Mash Out",
            temperature_fahrenheit=168.0,
            duration_minutes=10,
            step_order=4
        )
        assert mashout_step.temperature_fahrenheit == 168.0

    @pytest.mark.django_db
    def test_mash_step_time_ranges(self):
        """Test various mash step time durations."""
        recipe = Recipe.objects.create(name="Test Recipe")

        # Short step
        short_step = MashStep.objects.create(
            recipe=recipe,
            name="Short Step",
            temperature_fahrenheit=152.0,
            duration_minutes=15,
            step_order=1
        )
        assert short_step.duration_minutes == 15

        # Standard step
        standard_step = MashStep.objects.create(
            recipe=recipe,
            name="Standard Step",
            temperature_fahrenheit=152.0,
            duration_minutes=60,
            step_order=2
        )
        assert standard_step.duration_minutes == 60

        # Long step
        long_step = MashStep.objects.create(
            recipe=recipe,
            name="Long Step",
            temperature_fahrenheit=152.0,
            duration_minutes=90,
            step_order=3
        )
        assert long_step.duration_minutes == 90

    @pytest.mark.django_db
    def test_mash_step_relationships(self):
        """Test MashStep model relationships."""
        recipe = Recipe.objects.create(name="Test Recipe")

        step = MashStep.objects.create(
            recipe=recipe,
            name="Test Step",
            temperature_fahrenheit=152.0,
            duration_minutes=60,
            step_order=1
        )

        # Test forward relationship
        assert step.recipe == recipe

        # Test reverse relationship
        assert step in recipe.mashstep_set.all()

    @pytest.mark.django_db
    def test_mash_step_cascade_delete(self):
        """Test that mash steps are deleted when recipe is deleted."""
        recipe = Recipe.objects.create(name="Test Recipe")

        step = MashStep.objects.create(
            recipe=recipe,
            name="Test Step",
            temperature_fahrenheit=152.0,
            duration_minutes=60,
            step_order=1
        )

        step_id = step.id

        # Delete the recipe
        recipe.delete()

        # The foreign key has CASCADE, but soft delete doesn't cascade automatically
        # The step should still exist and not be soft-deleted
        # (This is expected behavior - foreign key CASCADE only applies to hard deletes)
        step_after_delete = MashStep.objects.all_with_deleted().get(id=step_id)
        assert not step_after_delete.is_deleted

        # The step should still be accessible through normal queries
        assert MashStep.objects.filter(id=step_id).exists()

    @pytest.mark.django_db
    def test_mash_step_notes_field(self):
        """Test mash step notes field."""
        recipe = Recipe.objects.create(name="Test Recipe")

        step = MashStep.objects.create(
            recipe=recipe,
            name="Test Step",
            temperature_fahrenheit=152.0,
            duration_minutes=60,
            step_order=1,
            description="This is a detailed note about the mash step procedure"
        )

        assert step.description == "This is a detailed note about the mash step procedure"

        # Test empty description
        step_no_description = MashStep.objects.create(
            recipe=recipe,
            name="No Description Step",
            temperature_fahrenheit=152.0,
            duration_minutes=60,
            step_order=2
        )
        assert step_no_description.description == ""

    @pytest.mark.django_db
    def test_complex_mash_schedule(self):
        """Test a complex multi-step mash schedule."""
        recipe = Recipe.objects.create(name="Complex Recipe")

        # Create a complex mash schedule
        steps_data = [
            {"name": "Acid Rest", "temp": 95.0, "time": 15, "order": 1},
            {"name": "Protein Rest", "temp": 122.0, "time": 20, "order": 2},
            {"name": "Beta Amylase", "temp": 145.0, "time": 30, "order": 3},
            {"name": "Alpha Amylase", "temp": 158.0, "time": 45, "order": 4},
            {"name": "Mash Out", "temp": 168.0, "time": 10, "order": 5},
        ]

        created_steps = []
        for step_data in steps_data:
            step = MashStep.objects.create(
                recipe=recipe,
                name=step_data["name"],
                temperature_fahrenheit=step_data["temp"],
                duration_minutes=step_data["time"],
                step_order=step_data["order"]
            )
            created_steps.append(step)

        # Verify all steps were created correctly
        assert recipe.mashstep_set.count() == 5

        # Verify ordering
        ordered_steps = recipe.mashstep_set.order_by('step_order')
        for i, step in enumerate(ordered_steps):
            expected_data = steps_data[i]
            assert step.name == expected_data["name"]
            assert step.temperature_fahrenheit == expected_data["temp"]
            assert step.duration_minutes == expected_data["time"]
            assert step.step_order == expected_data["order"]
