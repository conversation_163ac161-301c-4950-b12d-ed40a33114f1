"""
Tests for Recipe model functionality.

This module tests the Recipe model and its business logic calculations
including IBU, gravity, ABV, and SRM calculations.
"""

import pytest
from unittest.mock import Mock, patch
from django.contrib.auth import get_user_model

from core.models import Recipe, BeerStyle
from tests.base import BaseModelTest, create_mock_queryset
from tests.factories import (
    RecipeFactory, UserFactory, FermentableInclusionFactory,
    BoilHopInclusionFactory, FirstWortHopInclusionFactory,
    WhirlpoolHopInclusionFactory
)

User = get_user_model()


class TestRecipe(BaseModelTest):
    """Test Recipe model functionality."""

    factory = RecipeFactory
    model_class = Recipe

    def test_recipe_id_prefix(self):
        """Test that Recipe has correct ID prefix."""
        recipe = self.factory.create()
        recipe._get_id_prefix = Mock(return_value="rec")
        assert recipe._get_id_prefix() == "rec"

    def test_recipe_str_representation(self):
        """Test Recipe string representation."""
        # Create a real Recipe instance with a name
        recipe = Recipe(name="Test IPA")
        assert str(recipe) == "Test IPA"

    @pytest.mark.django_db
    def test_calculated_original_gravity_with_fermentables(self):
        """Test calculated_original_gravity property with fermentable inclusions."""
        from core.models import FermentableInclusion, Fermentable

        # Create real objects in database
        recipe = Recipe.objects.create(
            name="Test Recipe",
            batch_size_gallons=5.0
        )

        fermentable = Fermentable.objects.create(
            name="Test Malt",
            extract_potential_ppg=37.0
        )

        # Create fermentable inclusions that will contribute gravity points
        FermentableInclusion.objects.create(
            recipe=recipe,
            fermentable=fermentable,
            quantity=8.0,  # 8 lbs
            quantity_unit="lb",
            efficiency_percent=75.0
        )

        # Expected calculation: (8 lbs * 37 PPG * 0.75 efficiency) / 5 gallons = 44.4 points
        # Gravity = 1.000 + (44.4/1000) = 1.0444
        expected_gravity = 1.000 + (44.4 / 1000.0)
        actual_gravity = recipe.calculated_original_gravity

        assert abs(actual_gravity - expected_gravity) < 0.001  # Allow for floating point precision

    @pytest.mark.django_db
    def test_calculated_original_gravity_no_fermentables(self):
        """Test calculated_original_gravity with no fermentables."""
        recipe = Recipe.objects.create(
            name="Empty Recipe",
            batch_size_gallons=5.0
        )

        # No fermentables = 0 points = 1.000 gravity
        assert recipe.calculated_original_gravity == 1.000

    @pytest.mark.django_db
    def test_original_gravity_uses_calculated_when_fermentables_exist(self):
        """Test original_gravity property uses calculated value when fermentables exist."""
        from core.models import FermentableInclusion, Fermentable

        recipe = Recipe.objects.create(
            name="Test Recipe",
            target_original_gravity=1.060
        )

        # Create a fermentable inclusion to make fermentables exist
        fermentable = Fermentable.objects.create(
            name="Test Malt",
            extract_potential_ppg=37.0
        )
        FermentableInclusion.objects.create(
            recipe=recipe,
            fermentable=fermentable,
            quantity=8.0,
            quantity_unit="lb",
            efficiency_percent=75.0
        )

        # Should use calculated gravity when fermentables exist
        assert recipe.original_gravity == recipe.calculated_original_gravity

    @pytest.mark.django_db
    def test_original_gravity_uses_target_when_no_fermentables(self):
        """Test original_gravity property uses target value when no fermentables."""
        recipe = Recipe.objects.create(
            name="Test Recipe",
            target_original_gravity=1.060
        )

        # Should use target gravity when no fermentables exist
        assert recipe.original_gravity == 1.060

    @pytest.mark.django_db
    def test_calculated_srm_with_fermentables(self):
        """Test calculated_srm property with fermentable inclusions."""
        from core.models import FermentableInclusion, Fermentable

        recipe = Recipe.objects.create(name="Test Recipe")

        # Create fermentable inclusions with known SRM contributions
        fermentable1 = Fermentable.objects.create(
            name="Munich Malt",
            color_lovibond=10.0,
            extract_potential_ppg=37.0
        )
        fermentable2 = Fermentable.objects.create(
            name="Crystal 60",
            color_lovibond=60.0,
            extract_potential_ppg=35.0
        )

        FermentableInclusion.objects.create(
            recipe=recipe,
            fermentable=fermentable1,
            quantity=4.0,
            quantity_unit="lb",
            efficiency_percent=75.0
        )
        FermentableInclusion.objects.create(
            recipe=recipe,
            fermentable=fermentable2,
            quantity=1.0,
            quantity_unit="lb",
            efficiency_percent=75.0
        )

        # SRM should be calculated based on actual fermentable contributions
        calculated_srm = recipe.calculated_srm
        assert calculated_srm > 0.0
        assert isinstance(calculated_srm, float)

    @pytest.mark.django_db
    def test_calculated_srm_no_fermentables(self):
        """Test calculated_srm with no fermentables."""
        recipe = Recipe.objects.create(name="Test Recipe")
        assert recipe.calculated_srm == 0.0

    @pytest.mark.django_db
    def test_calculated_srm_zero_mcu(self):
        """Test calculated_srm with zero MCU."""
        from core.models import FermentableInclusion, Fermentable

        recipe = Recipe.objects.create(name="Test Recipe")

        # Create fermentable with zero color contribution
        fermentable = Fermentable.objects.create(
            name="White Sugar",
            color_lovibond=0.0,
            extract_potential_ppg=46.0
        )

        FermentableInclusion.objects.create(
            recipe=recipe,
            fermentable=fermentable,
            quantity=2.0,
            quantity_unit="lb",
            efficiency_percent=75.0
        )

        assert recipe.calculated_srm == 0.0

    @pytest.mark.django_db
    def test_total_ibus_calculation(self):
        """Test total_ibus property calculation."""
        from core.models import Hop, BoilHopInclusion, FirstWortHopInclusion, WhirlpoolHopInclusion

        recipe = Recipe.objects.create(name="Test Recipe")
        hop = Hop.objects.create(name="Test Hop", alpha_acid=10.0)

        # Create hop inclusions that will contribute IBUs
        BoilHopInclusion.objects.create(
            recipe=recipe,
            hop=hop,
            quantity=1.0,
            quantity_unit="oz",
            time_minutes=60
        )
        FirstWortHopInclusion.objects.create(
            recipe=recipe,
            hop=hop,
            quantity=0.5,
            quantity_unit="oz"
        )
        WhirlpoolHopInclusion.objects.create(
            recipe=recipe,
            hop=hop,
            quantity=1.0,
            quantity_unit="oz",
            time_minutes=20,
            temperature_f=180
        )

        # Total IBUs should be calculated from all hop additions
        total_ibus = recipe.total_ibus
        assert total_ibus > 0.0
        assert isinstance(total_ibus, float)

    @pytest.mark.django_db
    def test_total_ibus_no_hops(self):
        """Test total_ibus with no hop inclusions."""
        recipe = Recipe.objects.create(name="Test Recipe")
        assert recipe.total_ibus == 0.0

    def test_estimated_final_gravity_calculation(self):
        """Test estimated_final_gravity property calculation."""
        recipe = Recipe(
            name="Test Recipe",
            target_original_gravity=1.060
        )

        # FG = OG - (OG - 1.000) * 0.75
        # FG = 1.060 - (1.060 - 1.000) * 0.75 = 1.060 - 0.045 = 1.015
        expected_fg = 1.060 - ((1.060 - 1.000) * 0.75)
        assert recipe.estimated_final_gravity == round(expected_fg, 3)

    def test_estimated_abv_calculation(self):
        """Test estimated_abv property calculation."""
        recipe = Recipe(
            name="Test Recipe",
            target_original_gravity=1.060
        )

        # ABV calculation uses original_gravity and estimated_final_gravity
        # With target OG of 1.060, estimated FG should be around 1.015
        # ABV = (OG - FG) * 131.25
        expected_abv = (recipe.original_gravity - recipe.estimated_final_gravity) * 131.25
        assert recipe.estimated_abv == round(expected_abv, 1)

    def test_estimated_abv_with_low_gravity(self):
        """Test estimated_abv with low gravity beer."""
        recipe = Recipe(
            name="Test Recipe",
            target_original_gravity=1.035
        )

        # ABV calculation for low gravity beer
        expected_abv = (recipe.original_gravity - recipe.estimated_final_gravity) * 131.25
        assert recipe.estimated_abv == round(expected_abv, 1)

    @pytest.mark.django_db
    def test_recipe_creation_in_db(self):
        """Test Recipe creation in database."""
        user = User.objects.create_user(email='<EMAIL>')
        recipe = Recipe.objects.create(
            user=user,
            name="Test Recipe",
            description="A test recipe",
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

        assert recipe.id is not None
        assert recipe.id.startswith("rec_")
        assert recipe.name == "Test Recipe"
        assert recipe.user == user
        assert recipe.batch_size_gallons == 5.0
        assert recipe.mash_efficiency_percent == 75.0
        assert recipe.target_original_gravity == 1.050

    @pytest.mark.django_db
    def test_recipe_user_relationship(self):
        """Test Recipe user foreign key relationship."""
        user = User.objects.create_user(email='<EMAIL>')
        recipe = Recipe.objects.create(
            user=user,
            name="User Recipe",
            batch_size_gallons=5.0
        )

        assert recipe.user == user
        assert recipe in user.recipe_set.all()

    @pytest.mark.django_db
    def test_recipe_water_profile_relationship(self):
        """Test Recipe water profile foreign key relationship."""
        from core.models import WaterProfile

        water_profile = WaterProfile.objects.create(
            name="Test Profile",
            calcium_ppm=50.0,
            magnesium_ppm=10.0
        )

        recipe = Recipe.objects.create(
            name="Recipe with Water",
            water_profile=water_profile,
            batch_size_gallons=5.0
        )

        assert recipe.water_profile == water_profile

    @pytest.mark.django_db
    def test_recipe_beer_style_relationship(self):
        """Test Recipe beer style foreign key relationship."""
        beer_style = BeerStyle.objects.create(
            name="American IPA",
            description="A hoppy American beer style",
            srm_min=6.0,
            srm_max=14.0,
            ibu_min=40.0,
            ibu_max=70.0,
        )

        recipe = Recipe.objects.create(
            name="Recipe with Beer Style",
            beer_style=beer_style,
            batch_size_gallons=5.0
        )

        assert recipe.beer_style == beer_style
        assert recipe in beer_style.recipe_set.all()

    @pytest.mark.django_db
    def test_recipe_beer_style_null_allowed(self):
        """Test that Recipe can exist without a beer style."""
        recipe = Recipe.objects.create(
            name="Recipe without Style",
            batch_size_gallons=5.0
        )

        assert recipe.beer_style is None

    @pytest.mark.django_db
    def test_recipe_beer_style_set_null_on_delete(self):
        """Test that Recipe.beer_style is set to NULL when BeerStyle is deleted."""
        beer_style = BeerStyle.objects.create(
            name="Test Style",
            description="Test style for deletion"
        )

        recipe = Recipe.objects.create(
            name="Recipe with Style",
            beer_style=beer_style,
            batch_size_gallons=5.0
        )

        # Verify relationship is established
        assert recipe.beer_style == beer_style

        # Delete the beer style (soft delete)
        beer_style.delete()

        # Refresh recipe from database
        recipe.refresh_from_db()

        # beer_style should still reference the soft-deleted style
        # (SET_NULL only applies to hard deletes)
        assert recipe.beer_style == beer_style
        assert recipe.beer_style.is_deleted

    @pytest.mark.django_db
    def test_recipe_beer_style_hard_delete_sets_null(self):
        """Test that Recipe.beer_style is set to NULL when BeerStyle is hard deleted."""
        beer_style = BeerStyle.objects.create(
            name="Test Style Hard Delete",
            description="Test style for hard deletion"
        )

        recipe = Recipe.objects.create(
            name="Recipe with Style Hard Delete",
            beer_style=beer_style,
            batch_size_gallons=5.0
        )

        # Verify relationship is established
        assert recipe.beer_style == beer_style

        # Hard delete the beer style
        beer_style.hard_delete()

        # Refresh recipe from database
        recipe.refresh_from_db()

        # beer_style should be NULL after hard delete
        assert recipe.beer_style is None

    @pytest.mark.django_db
    def test_multiple_recipes_same_beer_style(self):
        """Test that multiple recipes can share the same beer style."""
        beer_style = BeerStyle.objects.create(
            name="Shared Style",
            description="Style shared by multiple recipes"
        )

        recipe1 = Recipe.objects.create(
            name="Recipe 1",
            beer_style=beer_style,
            batch_size_gallons=5.0
        )

        recipe2 = Recipe.objects.create(
            name="Recipe 2",
            beer_style=beer_style,
            batch_size_gallons=10.0
        )

        assert recipe1.beer_style == beer_style
        assert recipe2.beer_style == beer_style

        # Both recipes should be in the beer style's recipe set
        style_recipes = list(beer_style.recipe_set.all())
        assert recipe1 in style_recipes
        assert recipe2 in style_recipes
        assert len(style_recipes) == 2

    def test_recipe_default_values(self):
        """Test Recipe model default values."""
        recipe = self.factory.create()

        # Test that factory provides reasonable defaults
        assert recipe.batch_size_gallons == 5.0
        assert recipe.mash_efficiency_percent == 75.0
        assert recipe.target_original_gravity == 1.050
        assert recipe.description == "A test recipe for brewing"
