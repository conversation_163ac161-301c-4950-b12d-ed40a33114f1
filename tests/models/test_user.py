"""
Tests for user model functionality.

This module tests the User model and its business logic.
"""

import pytest
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError

from core.models import Recipe
from tests.base import BaseModelTest
from tests.factories import UserFactory

User = get_user_model()


class TestUser(BaseModelTest):
    """Test User model functionality."""

    factory = UserFactory
    model_class = User

    def test_user_id_prefix(self):
        """Test that User has correct ID prefix."""
        user = User(email="<EMAIL>")
        assert user._get_id_prefix() == "usr"

    def test_user_str_representation(self):
        """Test User string representation."""
        user = User(email="<EMAIL>")
        assert str(user) == "<EMAIL>"

    @pytest.mark.django_db
    def test_user_creation_in_db(self):
        """Test User creation in database."""
        user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123"
        )

        assert user.id is not None
        assert user.id.startswith("usr_")
        assert user.email == "<EMAIL>"
        assert user.check_password("testpass123")
        assert user.is_active is True
        assert user.is_staff is False
        assert user.is_superuser is False

    @pytest.mark.django_db
    def test_user_creation_with_create_user(self):
        """Test User creation using create_user method."""
        user = User.objects.create_user(
            email="<EMAIL>",
            password="password123"
        )

        assert user.email == "<EMAIL>"
        assert user.is_active is True
        assert user.is_staff is False
        assert user.is_superuser is False
        assert user.check_password("password123")

    @pytest.mark.django_db
    def test_superuser_creation(self):
        """Test superuser creation using create_superuser method."""
        superuser = User.objects.create_superuser(
            email="<EMAIL>",
            password="adminpass123"
        )

        assert superuser.email == "<EMAIL>"
        assert superuser.is_active is True
        assert superuser.is_staff is True
        assert superuser.is_superuser is True
        assert superuser.check_password("adminpass123")

    @pytest.mark.django_db
    def test_user_email_field(self):
        """Test user email field."""
        user = User.objects.create_user(

            email="<EMAIL>",
            password="password123"
        )

        assert user.email == "<EMAIL>"

        # Test email can be updated
        user.email = "<EMAIL>"
        user.save()

        user.refresh_from_db()
        assert user.email == "<EMAIL>"

    @pytest.mark.django_db
    def test_user_password_hashing(self):
        """Test that user passwords are properly hashed."""
        password = "testpassword123"
        user = User.objects.create_user(

            email="<EMAIL>",
            password=password
        )

        # Password should be hashed, not stored in plain text
        assert user.password != password
        # Password should start with a hash algorithm (could be pbkdf2_sha256, md5, etc.)
        assert '$' in user.password  # All Django password hashes contain '$'

        # But check_password should work
        assert user.check_password(password)
        assert not user.check_password("wrongpassword")

    @pytest.mark.django_db
    def test_user_recipe_relationship(self):
        """Test User relationship with Recipe."""
        user = User.objects.create_user(

            email="<EMAIL>",
            password="password123"
        )

        recipe = Recipe.objects.create(
            name="User's Recipe",
            user=user
        )

        # Test forward relationship
        assert recipe.user == user

        # Test reverse relationship
        assert recipe in user.recipe_set.all()
        assert user.recipe_set.count() == 1

    @pytest.mark.django_db
    def test_user_multiple_recipes(self):
        """Test User with multiple recipes."""
        user = User.objects.create_user(

            email="<EMAIL>",
            password="password123"
        )

        # Create multiple recipes for the user
        recipe1 = Recipe.objects.create(name="IPA", user=user)
        recipe2 = Recipe.objects.create(name="Stout", user=user)
        recipe3 = Recipe.objects.create(name="Lager", user=user)

        # User should have all recipes
        user_recipes = user.recipe_set.all()
        assert user_recipes.count() == 3
        assert recipe1 in user_recipes
        assert recipe2 in user_recipes
        assert recipe3 in user_recipes

    @pytest.mark.django_db
    def test_user_without_recipes(self):
        """Test User without any recipes."""
        user = User.objects.create_user(

            email="<EMAIL>",
            password="password123"
        )

        assert user.recipe_set.count() == 0
        assert list(user.recipe_set.all()) == []

    @pytest.mark.django_db
    def test_user_soft_delete_with_recipes(self):
        """Test User soft delete behavior with associated recipes."""
        user = User.objects.create_user(
            email="<EMAIL>",
            password="password123"
        )

        recipe = Recipe.objects.create(
            name="Recipe to be orphaned",
            user=user
        )

        user_id = user.id
        recipe_id = recipe.id

        # Soft delete the user
        user.delete()

        # User should be soft-deleted (marked with deleted_at)
        user.refresh_from_db()
        assert user.is_deleted
        assert user.deleted_at is not None

        # Note: UserManager doesn't inherit from SoftDeleteManager, so it doesn't
        # filter out soft-deleted users automatically. The user will still appear
        # in normal queries, but the is_deleted property will be True.

        # Recipe should still exist and still reference the user
        recipe.refresh_from_db()
        assert recipe.user == user

    @pytest.mark.django_db
    def test_user_authentication(self):
        """Test user authentication methods."""
        user = User.objects.create_user(

            email="<EMAIL>",
            password="correctpassword"
        )

        # Test correct password
        assert user.check_password("correctpassword")

        # Test incorrect password
        assert not user.check_password("wrongpassword")
        assert not user.check_password("")
        assert not user.check_password(None)

    @pytest.mark.django_db
    def test_user_active_status(self):
        """Test user active status."""
        user = User.objects.create_user(

            email="<EMAIL>",
            password="password123"
        )

        # User should be active by default
        assert user.is_active is True

        # Test deactivating user
        user.is_active = False
        user.save()

        user.refresh_from_db()
        assert user.is_active is False

    @pytest.mark.django_db
    def test_user_staff_status(self):
        """Test user staff status."""
        # Regular user
        regular_user = User.objects.create_user(

            email="<EMAIL>",
            password="password123"
        )
        assert regular_user.is_staff is False

        # Staff user
        staff_user = User.objects.create_user(

            email="<EMAIL>",
            password="password123"
        )
        staff_user.is_staff = True
        staff_user.save()

        assert staff_user.is_staff is True
        assert staff_user.is_superuser is False  # Staff but not superuser

    @pytest.mark.django_db
    def test_user_email_uniqueness(self):
        """Test that emails must be unique."""
        User.objects.create_user(
            email="<EMAIL>",
            password="password123"
        )

        # Attempting to create another user with the same email should fail
        with pytest.raises(Exception):  # Could be IntegrityError or ValidationError
            User.objects.create_user(
                email="<EMAIL>",
                password="password123"
            )

    @pytest.mark.django_db
    def test_user_different_emails_allowed(self):
        """Test that different emails are allowed."""
        user1 = User.objects.create_user(
            email="<EMAIL>",
            password="password123"
        )

        # Should be able to create another user with a different email
        user2 = User.objects.create_user(
            email="<EMAIL>",
            password="password123"
        )

        assert user1.email == "<EMAIL>"
        assert user2.email == "<EMAIL>"

    @pytest.mark.django_db
    def test_user_password_change(self):
        """Test user password change functionality."""
        user = User.objects.create_user(

            email="<EMAIL>",
            password="oldpassword"
        )

        # Verify old password works
        assert user.check_password("oldpassword")

        # Change password
        user.set_password("newpassword")
        user.save()

        # Verify new password works and old doesn't
        assert user.check_password("newpassword")
        assert not user.check_password("oldpassword")

    @pytest.mark.django_db
    def test_user_full_name_property(self):
        """Test user full_name property."""
        user = User.objects.create_user(
            email="<EMAIL>",
            password="password123"
        )

        # Test with both first and last name
        user.first_name = "John"
        user.last_name = "Doe"
        user.save()

        assert user.full_name == "John Doe"
        assert str(user) == "John Doe"

        # Test with only first name
        user.last_name = ""
        user.save()

        assert user.full_name == "John"
        assert str(user) == "John"

        # Test with no names
        user.first_name = ""
        user.save()

        assert user.full_name == "<EMAIL>"
        assert str(user) == "<EMAIL>"

    @pytest.mark.django_db
    def test_user_short_name_property(self):
        """Test user short_name property."""
        user = User.objects.create_user(
            email="<EMAIL>",
            password="password123"
        )

        # Test with first name
        user.first_name = "Jane"
        user.save()

        assert user.short_name == "Jane"

        # Test without first name
        user.first_name = ""
        user.save()

        assert user.short_name == "<EMAIL>"
