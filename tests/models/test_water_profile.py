"""
Tests for water profile model functionality.

This module tests the WaterProfile model and its business logic.
"""

import pytest
from unittest.mock import Mock

from core.models import WaterProfile, Recipe
from tests.base import BaseModelTest
from tests.factories import WaterProfileFactory


class TestWaterProfile(BaseModelTest):
    """Test WaterProfile model functionality."""

    factory = WaterProfileFactory
    model_class = WaterProfile

    def test_water_profile_id_prefix(self):
        """Test that WaterProfile has correct ID prefix."""
        profile = WaterProfile(name="Test Profile")
        assert profile._get_id_prefix() == "wtr"

    def test_water_profile_str_representation(self):
        """Test WaterProfile string representation."""
        profile = WaterProfile(
            name="Burton on Trent",
            calcium_ppm=295.0,
            sulfate_ppm=725.0,
            chloride_ppm=25.0
        )
        # The actual str representation includes mineral info
        str_repr = str(profile)
        assert "Burton on Trent" in str_repr
        assert "Ca: 295.0" in str_repr
        assert "SO₄: 725.0" in str_repr
        assert "Cl: 25.0" in str_repr

    @pytest.mark.django_db
    def test_water_profile_creation_in_db(self):
        """Test WaterProfile creation in database."""
        profile = WaterProfile.objects.create(
            name="Burton on Trent",
            description="Classic English brewing water",
            calcium_ppm=295.0,
            magnesium_ppm=45.0,
            sodium_ppm=55.0,
            sulfate_ppm=725.0,
            chloride_ppm=25.0,
            bicarbonate_ppm=300.0
        )

        assert profile.id is not None
        assert profile.id.startswith("wtr_")
        assert profile.name == "Burton on Trent"
        assert profile.description == "Classic English brewing water"
        assert profile.calcium_ppm == 295.0
        assert profile.magnesium_ppm == 45.0
        assert profile.sodium_ppm == 55.0
        assert profile.sulfate_ppm == 725.0
        assert profile.chloride_ppm == 25.0
        assert profile.bicarbonate_ppm == 300.0

    def test_water_profile_default_values(self):
        """Test WaterProfile model default values."""
        profile = WaterProfile(name="Test Profile")

        assert profile.description == ""
        assert profile.calcium_ppm == 0.0
        assert profile.magnesium_ppm == 0.0
        assert profile.sodium_ppm == 0.0
        assert profile.sulfate_ppm == 0.0
        assert profile.chloride_ppm == 0.0
        assert profile.bicarbonate_ppm == 0.0

    @pytest.mark.django_db
    def test_sulfate_to_chloride_ratio_calculation(self):
        """Test sulfate_to_chloride_ratio property calculation."""
        # Test normal ratio
        profile = WaterProfile.objects.create(
            name="Test Profile",
            sulfate_ppm=300.0,
            chloride_ppm=100.0
        )
        assert profile.sulfate_to_chloride_ratio == 3.0

        # Test with zero chloride (should return None or handle gracefully)
        profile_zero_chloride = WaterProfile.objects.create(
            name="Zero Chloride Profile",
            sulfate_ppm=300.0,
            chloride_ppm=0.0
        )
        # Should handle division by zero gracefully
        ratio = profile_zero_chloride.sulfate_to_chloride_ratio
        assert ratio is None or ratio == float('inf')

        # Test with zero sulfate
        profile_zero_sulfate = WaterProfile.objects.create(
            name="Zero Sulfate Profile",
            sulfate_ppm=0.0,
            chloride_ppm=100.0
        )
        assert profile_zero_sulfate.sulfate_to_chloride_ratio == 0.0

    @pytest.mark.django_db
    def test_total_hardness_ppm_calculation(self):
        """Test total_hardness_ppm property calculation."""
        profile = WaterProfile.objects.create(
            name="Test Profile",
            calcium_ppm=100.0,
            magnesium_ppm=24.0
        )

        # Total hardness = (Ca * 2.497) + (Mg * 4.118)
        expected_hardness = (100.0 * 2.497) + (24.0 * 4.118)
        assert abs(profile.total_hardness_ppm - expected_hardness) < 0.1

    @pytest.mark.django_db
    def test_sulfate_to_chloride_ratio_edge_cases(self):
        """Test sulfate to chloride ratio edge cases."""
        # Test with zero chloride (should return inf)
        profile_zero_chloride = WaterProfile.objects.create(
            name="Zero Chloride Profile",
            sulfate_ppm=300.0,
            chloride_ppm=0.0
        )
        assert profile_zero_chloride.sulfate_to_chloride_ratio == float('inf')

        # Test with zero sulfate
        profile_zero_sulfate = WaterProfile.objects.create(
            name="Zero Sulfate Profile",
            sulfate_ppm=0.0,
            chloride_ppm=100.0
        )
        assert profile_zero_sulfate.sulfate_to_chloride_ratio == 0.0

        # Test with both zero
        profile_both_zero = WaterProfile.objects.create(
            name="Both Zero Profile",
            sulfate_ppm=0.0,
            chloride_ppm=0.0
        )
        assert profile_both_zero.sulfate_to_chloride_ratio == 0.0

    @pytest.mark.django_db
    def test_water_profile_style_suitability(self):
        """Test water profile suitability for different beer styles."""
        # Burton water - good for IPAs
        burton_water = WaterProfile.objects.create(
            name="Burton on Trent",
            calcium_ppm=295.0,
            sulfate_ppm=725.0,
            chloride_ppm=25.0
        )

        # High sulfate to chloride ratio (good for hoppy beers)
        assert burton_water.sulfate_to_chloride_ratio > 2.0

        # Dublin water - good for stouts
        dublin_water = WaterProfile.objects.create(
            name="Dublin",
            calcium_ppm=115.0,
            sulfate_ppm=55.0,
            chloride_ppm=19.0,
            bicarbonate_ppm=319.0
        )

        # High bicarbonate (good for dark beers)
        assert dublin_water.bicarbonate_ppm > 200.0

        # Pilsen water - good for lagers
        pilsen_water = WaterProfile.objects.create(
            name="Pilsen",
            calcium_ppm=7.0,
            magnesium_ppm=2.0,
            sodium_ppm=2.0,
            sulfate_ppm=5.0,
            chloride_ppm=5.0,
            bicarbonate_ppm=15.0
        )

        # Very soft water (low hardness)
        assert pilsen_water.total_hardness_ppm < 50.0

    @pytest.mark.django_db
    def test_water_profile_recipe_relationship(self):
        """Test WaterProfile relationship with Recipe."""
        profile = WaterProfile.objects.create(
            name="Test Profile",
            calcium_ppm=100.0
        )

        recipe = Recipe.objects.create(
            name="Test Recipe",
            water_profile=profile
        )

        # Test forward relationship
        assert recipe.water_profile == profile

        # Test reverse relationship
        assert recipe in profile.recipe_set.all()

    @pytest.mark.django_db
    def test_water_profile_mineral_validation(self):
        """Test water profile mineral concentration validation."""
        # Test normal mineral concentrations
        profile = WaterProfile.objects.create(
            name="Normal Water",
            calcium_ppm=150.0,
            magnesium_ppm=30.0,
            sodium_ppm=20.0,
            sulfate_ppm=250.0,
            chloride_ppm=75.0,
            bicarbonate_ppm=200.0
        )

        assert profile.calcium_ppm == 150.0
        assert profile.magnesium_ppm == 30.0
        assert profile.sodium_ppm == 20.0
        assert profile.sulfate_ppm == 250.0
        assert profile.chloride_ppm == 75.0
        assert profile.bicarbonate_ppm == 200.0

    @pytest.mark.django_db
    def test_water_profile_zero_minerals(self):
        """Test water profile with zero mineral concentrations."""
        profile = WaterProfile.objects.create(
            name="Distilled Water",
            calcium_ppm=0.0,
            magnesium_ppm=0.0,
            sodium_ppm=0.0,
            sulfate_ppm=0.0,
            chloride_ppm=0.0,
            bicarbonate_ppm=0.0
        )

        assert profile.total_hardness_ppm == 0.0
        assert profile.sulfate_to_chloride_ratio == 0.0

    @pytest.mark.django_db
    def test_water_profile_high_mineral_content(self):
        """Test water profile with high mineral concentrations."""
        profile = WaterProfile.objects.create(
            name="Very Hard Water",
            calcium_ppm=500.0,
            magnesium_ppm=100.0,
            sodium_ppm=200.0,
            sulfate_ppm=1000.0,
            chloride_ppm=300.0,
            bicarbonate_ppm=400.0
        )

        # Should handle high concentrations without issues
        assert profile.total_hardness_ppm > 1000.0  # Very hard water
        assert profile.sulfate_to_chloride_ratio > 3.0  # High sulfate to chloride ratio

    @pytest.mark.django_db
    def test_water_profile_description_field(self):
        """Test water profile description field."""
        profile = WaterProfile.objects.create(
            name="Custom Profile",
            description="A custom water profile for brewing IPAs with enhanced hop character"
        )

        assert profile.description == "A custom water profile for brewing IPAs with enhanced hop character"

        # Test empty description
        profile_no_desc = WaterProfile.objects.create(
            name="No Description Profile"
        )
        assert profile_no_desc.description == ""

    @pytest.mark.django_db
    def test_water_profile_unique_names(self):
        """Test that water profile names can be duplicated (no unique constraint)."""
        # Create two profiles with the same name (should be allowed)
        profile1 = WaterProfile.objects.create(
            name="Test Profile",
            calcium_ppm=100.0
        )

        profile2 = WaterProfile.objects.create(
            name="Test Profile",
            calcium_ppm=200.0
        )

        # Both should exist with different IDs
        assert profile1.id != profile2.id
        assert profile1.calcium_ppm != profile2.calcium_ppm
        assert WaterProfile.objects.filter(name="Test Profile").count() == 2
