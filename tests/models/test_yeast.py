"""
Tests for yeast model functionality.

This module tests the Yeast and YeastInclusion models and their business logic.
"""

import pytest
from unittest.mock import Mock

from core.models import Yeast, YeastInclusion, YeastType, YeastForm, Recipe
from tests.base import BaseModelTest
from tests.factories import YeastFactory


class TestYeast(BaseModelTest):
    """Test Yeast model functionality."""

    factory = YeastFactory
    model_class = Yeast

    def test_yeast_id_prefix(self):
        """Test that Ye<PERSON> has correct ID prefix."""
        yeast = Yeast(name="Test Yeast")
        assert yeast._get_id_prefix() == "yst"

    def test_yeast_str_representation(self):
        """Test Yeast string representation."""
        yeast = Yeast(name="Safale US-05")
        assert str(yeast) == "Safale US-05"

    @pytest.mark.django_db
    def test_yeast_creation_in_db(self):
        """Test Yeast creation in database."""
        yeast = Yeast.objects.create(
            name="Wyeast 1056",
            laboratory="Wyeast",
            product_id="1056",
            yeast_type=YeastType.ALE,
            yeast_form=YeastForm.LIQUID,
            min_temperature_fahrenheit=60.0,
            max_temperature_fahrenheit=72.0,
            attenuation_percent=75.0,
            description="American Ale yeast"
        )

        assert yeast.id is not None
        assert yeast.id.startswith("yst_")
        assert yeast.name == "Wyeast 1056"
        assert yeast.laboratory == "Wyeast"
        assert yeast.product_id == "1056"
        assert yeast.yeast_type == YeastType.ALE
        assert yeast.yeast_form == YeastForm.LIQUID
        assert yeast.min_temperature_fahrenheit == 60.0
        assert yeast.max_temperature_fahrenheit == 72.0
        assert yeast.attenuation_percent == 75.0
        assert yeast.description == "American Ale yeast"

    def test_yeast_default_values(self):
        """Test Yeast model default values."""
        yeast = Yeast(name="Test Yeast")

        assert yeast.laboratory == ""
        assert yeast.product_id == ""
        assert yeast.yeast_type == YeastType.ALE
        assert yeast.yeast_form == YeastForm.DRY
        assert yeast.flocculation == "MEDIUM"
        # These fields have no defaults, so they'll be None until set
        assert yeast.min_temperature_fahrenheit is None
        assert yeast.max_temperature_fahrenheit is None
        assert yeast.attenuation_percent is None
        assert yeast.alcohol_tolerance_percent is None
        assert yeast.description == ""

    @pytest.mark.django_db
    def test_yeast_type_choices(self):
        """Test yeast type choices."""
        # Test each yeast type
        types_to_test = [
            YeastType.ALE,
            YeastType.LAGER,
            YeastType.WILD,
            YeastType.BRETT,
            YeastType.BACTERIA,
        ]

        for yeast_type in types_to_test:
            yeast = Yeast.objects.create(
                name=f"Test {yeast_type} Yeast",
                yeast_type=yeast_type,
                min_temperature_fahrenheit=60.0,
                max_temperature_fahrenheit=75.0,
                attenuation_percent=75.0
            )
            assert yeast.yeast_type == yeast_type

    @pytest.mark.django_db
    def test_yeast_form_choices(self):
        """Test yeast form choices."""
        # Test each yeast form
        forms_to_test = [
            YeastForm.DRY,
            YeastForm.LIQUID,
            YeastForm.SLANT,
            YeastForm.CULTURE,
        ]

        for yeast_form in forms_to_test:
            yeast = Yeast.objects.create(
                name=f"Test {yeast_form} Yeast",
                yeast_form=yeast_form,
                min_temperature_fahrenheit=60.0,
                max_temperature_fahrenheit=75.0,
                attenuation_percent=75.0
            )
            assert yeast.yeast_form == yeast_form

    @pytest.mark.django_db
    def test_temperature_range_validation(self):
        """Test yeast temperature range."""
        # Ale yeast temperature range
        ale_yeast = Yeast.objects.create(
            name="Ale Yeast",
            yeast_type=YeastType.ALE,
            min_temperature_fahrenheit=62.0,
            max_temperature_fahrenheit=75.0,
            attenuation_percent=75.0
        )
        assert ale_yeast.min_temperature_fahrenheit == 62.0
        assert ale_yeast.max_temperature_fahrenheit == 75.0

        # Lager yeast temperature range
        lager_yeast = Yeast.objects.create(
            name="Lager Yeast",
            yeast_type=YeastType.LAGER,
            min_temperature_fahrenheit=45.0,
            max_temperature_fahrenheit=55.0,
            attenuation_percent=75.0
        )
        assert lager_yeast.min_temperature_fahrenheit == 45.0
        assert lager_yeast.max_temperature_fahrenheit == 55.0

    @pytest.mark.django_db
    def test_attenuation_percent_validation(self):
        """Test yeast attenuation percentage."""
        # High attenuation yeast
        high_att_yeast = Yeast.objects.create(
            name="High Attenuation Yeast",
            min_temperature_fahrenheit=60.0,
            max_temperature_fahrenheit=75.0,
            attenuation_percent=85.0
        )
        assert high_att_yeast.attenuation_percent == 85.0

        # Low attenuation yeast
        low_att_yeast = Yeast.objects.create(
            name="Low Attenuation Yeast",
            min_temperature_fahrenheit=60.0,
            max_temperature_fahrenheit=75.0,
            attenuation_percent=65.0
        )
        assert low_att_yeast.attenuation_percent == 65.0

    @pytest.mark.django_db
    def test_yeast_laboratory_and_product_id(self):
        """Test yeast laboratory and product ID fields."""
        yeast = Yeast.objects.create(
            name="White Labs WLP001",
            laboratory="White Labs",
            product_id="WLP001",
            min_temperature_fahrenheit=60.0,
            max_temperature_fahrenheit=75.0,
            attenuation_percent=75.0
        )

        assert yeast.laboratory == "White Labs"
        assert yeast.product_id == "WLP001"


class TestYeastInclusion(BaseModelTest):
    """Test YeastInclusion model functionality."""

    factory = None  # We'll create instances manually since this requires relationships
    model_class = YeastInclusion

    @pytest.mark.django_db
    def test_model_has_stripe_like_id(self):
        """Test that model generates stripe-like IDs."""
        recipe = Recipe.objects.create(name="Test Recipe")
        yeast = Yeast.objects.create(
            name="Test Yeast",
            min_temperature_fahrenheit=60.0,
            max_temperature_fahrenheit=75.0,
            attenuation_percent=75.0
        )
        instance = YeastInclusion.objects.create(
            recipe=recipe,
            yeast=yeast,
            quantity=1.0
        )
        assert instance.id is not None
        assert len(instance.id) > 10
        assert "_" in instance.id

    @pytest.mark.django_db
    def test_model_has_timestamps(self):
        """Test that model has created_at and updated_at fields."""
        recipe = Recipe.objects.create(name="Test Recipe")
        yeast = Yeast.objects.create(
            name="Test Yeast",
            min_temperature_fahrenheit=60.0,
            max_temperature_fahrenheit=75.0,
            attenuation_percent=75.0
        )
        instance = YeastInclusion.objects.create(
            recipe=recipe,
            yeast=yeast,
            quantity=1.0
        )
        assert hasattr(instance, 'created_at')
        assert hasattr(instance, 'updated_at')
        assert instance.created_at is not None
        assert instance.updated_at is not None

    @pytest.mark.django_db
    def test_model_has_soft_delete_methods(self):
        """Test that model has soft delete functionality."""
        recipe = Recipe.objects.create(name="Test Recipe")
        yeast = Yeast.objects.create(
            name="Test Yeast",
            min_temperature_fahrenheit=60.0,
            max_temperature_fahrenheit=75.0,
            attenuation_percent=75.0
        )
        instance = YeastInclusion.objects.create(
            recipe=recipe,
            yeast=yeast,
            quantity=1.0
        )
        assert hasattr(instance, 'delete')
        assert hasattr(instance, 'is_deleted')
        assert not instance.is_deleted

    @pytest.mark.django_db
    def test_model_str_representation(self):
        """Test that model has a meaningful string representation."""
        recipe = Recipe.objects.create(name="Test Recipe")
        yeast = Yeast.objects.create(
            name="Test Yeast",
            min_temperature_fahrenheit=60.0,
            max_temperature_fahrenheit=75.0,
            attenuation_percent=75.0
        )
        instance = YeastInclusion.objects.create(
            recipe=recipe,
            yeast=yeast,
            quantity=1.0
        )
        str_repr = str(instance)
        assert str_repr is not None
        assert len(str_repr) > 0
        assert "Test Yeast" in str_repr

    def test_yeast_inclusion_id_prefix(self):
        """Test that YeastInclusion has correct ID prefix."""
        inclusion = YeastInclusion()
        assert inclusion._get_id_prefix() == "ysi"

    @pytest.mark.django_db
    def test_yeast_inclusion_str_representation(self):
        """Test YeastInclusion string representation."""
        recipe = Recipe.objects.create(name="Test Recipe")
        yeast = Yeast.objects.create(
            name="Safale US-05",
            min_temperature_fahrenheit=60.0,
            max_temperature_fahrenheit=75.0,
            attenuation_percent=75.0
        )
        inclusion = YeastInclusion.objects.create(
            recipe=recipe,
            yeast=yeast,
            quantity=1.0,
            quantity_unit="PACKET"
        )

        expected = "Safale US-05 - 1.0 PACKET"
        assert str(inclusion) == expected

    @pytest.mark.django_db
    def test_yeast_inclusion_creation_in_db(self):
        """Test YeastInclusion creation in database."""
        recipe = Recipe.objects.create(name="Test Recipe")
        yeast = Yeast.objects.create(
            name="Safale US-05",
            yeast_type=YeastType.ALE,
            yeast_form=YeastForm.DRY,
            min_temperature_fahrenheit=60.0,
            max_temperature_fahrenheit=75.0,
            attenuation_percent=75.0
        )

        inclusion = YeastInclusion.objects.create(
            recipe=recipe,
            yeast=yeast,
            quantity=1.0,
            quantity_unit="PACKET",
            starter_made=True,
            starter_size_ml=500.0
        )

        assert inclusion.id is not None
        assert inclusion.id.startswith("ysi_")
        assert inclusion.recipe == recipe
        assert inclusion.yeast == yeast
        assert inclusion.quantity == 1.0
        assert inclusion.quantity_unit == "PACKET"
        assert inclusion.starter_made is True
        assert inclusion.starter_size_ml == 500.0

    def test_yeast_inclusion_default_values(self):
        """Test YeastInclusion model default values."""
        inclusion = YeastInclusion()

        # quantity has no default, so it will be None until set
        assert inclusion.quantity is None
        assert inclusion.quantity_unit == "PACKET"
        assert inclusion.starter_made is False
        assert inclusion.starter_size_ml is None
        assert inclusion.notes == ""

    @pytest.mark.django_db
    def test_quantity_unit_choices(self):
        """Test yeast inclusion quantity unit choices."""
        recipe = Recipe.objects.create(name="Test Recipe")
        yeast = Yeast.objects.create(
            name="Test Yeast",
            min_temperature_fahrenheit=60.0,
            max_temperature_fahrenheit=75.0,
            attenuation_percent=75.0
        )

        # Test different quantity units
        units_to_test = ["PACKET", "VIAL", "ML", "GRAMS"]

        for unit in units_to_test:
            inclusion = YeastInclusion.objects.create(
                recipe=recipe,
                yeast=yeast,
                quantity=1.0,
                quantity_unit=unit
            )
            assert inclusion.quantity_unit == unit

    @pytest.mark.django_db
    def test_starter_functionality(self):
        """Test yeast starter functionality."""
        recipe = Recipe.objects.create(name="Test Recipe")
        yeast = Yeast.objects.create(
            name="Liquid Yeast",
            yeast_form=YeastForm.LIQUID,
            min_temperature_fahrenheit=60.0,
            max_temperature_fahrenheit=75.0,
            attenuation_percent=75.0
        )

        # Test with starter
        inclusion_with_starter = YeastInclusion.objects.create(
            recipe=recipe,
            yeast=yeast,
            quantity=1.0,
            quantity_unit="VIAL",
            starter_made=True,
            starter_size_ml=1000.0
        )

        assert inclusion_with_starter.starter_made is True
        assert inclusion_with_starter.starter_size_ml == 1000.0

        # Test without starter
        inclusion_no_starter = YeastInclusion.objects.create(
            recipe=recipe,
            yeast=yeast,
            quantity=1.0,
            quantity_unit="VIAL",
            starter_made=False,
            starter_size_ml=None
        )

        assert inclusion_no_starter.starter_made is False
        assert inclusion_no_starter.starter_size_ml is None

    @pytest.mark.django_db
    def test_yeast_inclusion_relationships(self):
        """Test YeastInclusion model relationships."""
        recipe = Recipe.objects.create(name="Test Recipe")
        yeast = Yeast.objects.create(
            name="Test Yeast",
            min_temperature_fahrenheit=60.0,
            max_temperature_fahrenheit=75.0,
            attenuation_percent=75.0
        )

        inclusion = YeastInclusion.objects.create(
            recipe=recipe,
            yeast=yeast,
            quantity=1.0,
            quantity_unit="PACKET"
        )

        # Test forward relationships
        assert inclusion.recipe == recipe
        assert inclusion.yeast == yeast

        # Test reverse relationships
        assert inclusion in recipe.yeastinclusion_set.all()

    @pytest.mark.django_db
    def test_yeast_inclusion_cascade_delete(self):
        """Test that yeast inclusions are deleted when recipe is deleted."""
        recipe = Recipe.objects.create(name="Test Recipe")
        yeast = Yeast.objects.create(
            name="Test Yeast",
            min_temperature_fahrenheit=60.0,
            max_temperature_fahrenheit=75.0,
            attenuation_percent=75.0
        )

        inclusion = YeastInclusion.objects.create(
            recipe=recipe,
            yeast=yeast,
            quantity=1.0,
            quantity_unit="PACKET"
        )

        inclusion_id = inclusion.id

        # Delete the recipe
        recipe.delete()

        # The foreign key has CASCADE, but soft delete doesn't cascade automatically
        # The inclusion should still exist and not be soft-deleted
        # (This is expected behavior - foreign key CASCADE only applies to hard deletes)
        inclusion_after_delete = YeastInclusion.objects.all_with_deleted().get(id=inclusion_id)
        assert not inclusion_after_delete.is_deleted

        # The inclusion should still be accessible through normal queries
        assert YeastInclusion.objects.filter(id=inclusion_id).exists()


class TestYeastType:
    """Test YeastType choices."""

    def test_yeast_type_choices_exist(self):
        """Test that all expected yeast type choices exist."""
        expected_choices = [
            YeastType.ALE,
            YeastType.LAGER,
            YeastType.WILD,
            YeastType.BRETT,
            YeastType.BACTERIA,
        ]

        for choice in expected_choices:
            assert choice in YeastType.values

    def test_yeast_type_labels(self):
        """Test yeast type choice labels."""
        assert YeastType.ALE.label == "Ale Yeast"
        assert YeastType.LAGER.label == "Lager Yeast"
        assert YeastType.WILD.label == "Wild Yeast"
        assert YeastType.BRETT.label == "Brettanomyces"
        assert YeastType.BACTERIA.label == "Bacteria"


class TestYeastForm:
    """Test YeastForm choices."""

    def test_yeast_form_choices_exist(self):
        """Test that all expected yeast form choices exist."""
        expected_choices = [
            YeastForm.DRY,
            YeastForm.LIQUID,
            YeastForm.SLANT,
            YeastForm.CULTURE,
        ]

        for choice in expected_choices:
            assert choice in YeastForm.values

    def test_yeast_form_labels(self):
        """Test yeast form choice labels."""
        assert YeastForm.DRY.label == "Dry"
        assert YeastForm.LIQUID.label == "Liquid"
        assert YeastForm.SLANT.label == "Slant"
        assert YeastForm.CULTURE.label == "Culture"
