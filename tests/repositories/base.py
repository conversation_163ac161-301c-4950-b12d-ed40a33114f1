"""
Base test utilities for repository layer tests.

This module contains base classes and utilities for testing repository classes
with proper database integration and factory usage.
"""

import pytest
from typing import Type, Any, List, Optional
from django.core.exceptions import ObjectDoesNotExist
from django.db import models

from core.repositories.base import BaseRepository
from tests.factories import (
    UserFactory, RecipeFactory, HopFactory, FermentableFactory,
    YeastFactory, WaterProfileFactory, MashStepFactory, FermentationPhaseFactory,
    BoilHopInclusionFactory, FirstWortHopInclusionFactory, FlameoutHopInclusionFactory,
    WhirlpoolHopInclusionFactory, DryHopInclusionFactory, FermentableInclusionFactory,
    YeastInclusionFactory
)


class BaseRepositoryTest:
    """Base class for repository tests with common functionality."""
    
    # Override in subclasses
    repository_class = None
    model_class = None
    factory_class = None
    
    def setup_method(self):
        """Set up test method - override in subclasses if needed."""
        if self.repository_class:
            self.repository = self.repository_class()
    
    def create_test_instance(self, **kwargs) -> Any:
        """Create a test instance using the factory."""
        if self.factory_class:
            return self.factory_class.create_in_db(**kwargs)
        else:
            # Fallback to direct model creation
            return self.model_class.objects.create(**kwargs)
    
    def create_multiple_instances(self, count: int, **kwargs) -> List[Any]:
        """Create multiple test instances."""
        instances = []
        for i in range(count):
            instance_kwargs = kwargs.copy()
            # Add unique identifiers to avoid conflicts
            if 'name' in instance_kwargs:
                instance_kwargs['name'] = f"{instance_kwargs['name']} {i+1}"
            elif hasattr(self.model_class, 'name'):
                instance_kwargs['name'] = f"Test Instance {i+1}"
            instances.append(self.create_test_instance(**instance_kwargs))
        return instances
    
    @pytest.mark.django_db
    def test_repository_initialization(self):
        """Test that repository initializes correctly."""
        assert self.repository is not None
        assert self.repository.model_class == self.model_class
    
    @pytest.mark.django_db
    def test_get_by_id_found(self):
        """Test getting an instance by ID when it exists."""
        # Arrange
        instance = self.create_test_instance()
        
        # Act
        result = self.repository.get_by_id(instance.id)
        
        # Assert
        assert result is not None
        assert result.id == instance.id
    
    @pytest.mark.django_db
    def test_get_by_id_not_found(self):
        """Test getting an instance by ID when it doesn't exist."""
        # Act
        result = self.repository.get_by_id("nonexistent_id")
        
        # Assert
        assert result is None
    
    @pytest.mark.django_db
    def test_get_by_id_or_raise_found(self):
        """Test getting an instance by ID or raising exception when found."""
        # Arrange
        instance = self.create_test_instance()
        
        # Act
        result = self.repository.get_by_id_or_raise(instance.id)
        
        # Assert
        assert result is not None
        assert result.id == instance.id
    
    @pytest.mark.django_db
    def test_get_by_id_or_raise_not_found(self):
        """Test getting an instance by ID or raising exception when not found."""
        # Act & Assert
        with pytest.raises(ObjectDoesNotExist):
            self.repository.get_by_id_or_raise("nonexistent_id")
    
    @pytest.mark.django_db
    def test_get_all_empty(self):
        """Test getting all instances when none exist."""
        # Act
        result = self.repository.get_all()
        
        # Assert
        assert result == []
    
    @pytest.mark.django_db
    def test_get_all_with_data(self):
        """Test getting all instances when data exists."""
        # Arrange
        instances = self.create_multiple_instances(3)
        
        # Act
        result = self.repository.get_all()
        
        # Assert
        assert len(result) == 3
        result_ids = [instance.id for instance in result]
        for instance in instances:
            assert instance.id in result_ids
    
    @pytest.mark.django_db
    def test_filter_no_matches(self):
        """Test filtering with criteria that match nothing."""
        # Arrange
        self.create_test_instance()
        
        # Act
        result = self.repository.filter(id="nonexistent_id")
        
        # Assert
        assert result == []
    
    @pytest.mark.django_db
    def test_filter_with_matches(self):
        """Test filtering with criteria that match instances."""
        # Arrange
        instance = self.create_test_instance()
        
        # Act
        result = self.repository.filter(id=instance.id)
        
        # Assert
        assert len(result) == 1
        assert result[0].id == instance.id
    
    @pytest.mark.django_db
    def test_create_instance(self):
        """Test creating a new instance."""
        # Arrange
        create_data = self.get_create_data()
        
        # Act
        result = self.repository.create(**create_data)
        
        # Assert
        assert result is not None
        assert result.id is not None
        for key, value in create_data.items():
            assert getattr(result, key) == value
    
    @pytest.mark.django_db
    def test_update_instance(self):
        """Test updating an existing instance."""
        # Arrange
        instance = self.create_test_instance()
        update_data = self.get_update_data()
        
        # Act
        result = self.repository.update(instance, **update_data)
        
        # Assert
        assert result is not None
        assert result.id == instance.id
        for key, value in update_data.items():
            assert getattr(result, key) == value
    
    @pytest.mark.django_db
    def test_delete_instance(self):
        """Test deleting an instance (soft delete)."""
        # Arrange
        instance = self.create_test_instance()
        instance_id = instance.id
        
        # Act
        self.repository.delete(instance)
        
        # Assert
        # For soft delete, instance should not be found in normal queries
        result = self.repository.get_by_id(instance_id)
        assert result is None
        
        # But should exist in all_with_deleted queries
        if hasattr(self.model_class.objects, 'all_with_deleted'):
            deleted_instance = self.model_class.objects.all_with_deleted().filter(id=instance_id).first()
            assert deleted_instance is not None
            assert deleted_instance.is_deleted
    
    @pytest.mark.django_db
    def test_exists_true(self):
        """Test exists method when instance exists."""
        # Arrange
        instance = self.create_test_instance()
        
        # Act
        result = self.repository.exists(id=instance.id)
        
        # Assert
        assert result is True
    
    @pytest.mark.django_db
    def test_exists_false(self):
        """Test exists method when instance doesn't exist."""
        # Act
        result = self.repository.exists(id="nonexistent_id")
        
        # Assert
        assert result is False
    
    @pytest.mark.django_db
    def test_count_empty(self):
        """Test count method when no instances exist."""
        # Act
        result = self.repository.count()
        
        # Assert
        assert result == 0
    
    @pytest.mark.django_db
    def test_count_with_data(self):
        """Test count method when instances exist."""
        # Arrange
        self.create_multiple_instances(3)
        
        # Act
        result = self.repository.count()
        
        # Assert
        assert result == 3
    
    @pytest.mark.django_db
    def test_count_with_filter(self):
        """Test count method with filtering criteria."""
        # Arrange
        instance = self.create_test_instance()
        self.create_multiple_instances(2)  # Create additional instances
        
        # Act
        result = self.repository.count(id=instance.id)
        
        # Assert
        assert result == 1
    
    def get_create_data(self) -> dict:
        """Get data for creating instances - override in subclasses."""
        if self.factory_class:
            return self.factory_class.get_defaults()
        return {}
    
    def get_update_data(self) -> dict:
        """Get data for updating instances - override in subclasses."""
        # Default update data - override in subclasses for specific fields
        return {}


# Pytest fixtures for repository testing
@pytest.fixture
def user_instance():
    """Create a user instance for testing."""
    return UserFactory.create_in_db()


@pytest.fixture
def recipe_instance():
    """Create a recipe instance for testing."""
    return RecipeFactory.create_in_db()


@pytest.fixture
def hop_instance():
    """Create a hop instance for testing."""
    return HopFactory.create_in_db()


@pytest.fixture
def fermentable_instance():
    """Create a fermentable instance for testing."""
    return FermentableFactory.create_in_db()


@pytest.fixture
def yeast_instance():
    """Create a yeast instance for testing."""
    return YeastFactory.create_in_db()


@pytest.fixture
def water_profile_instance():
    """Create a water profile instance for testing."""
    return WaterProfileFactory.create_in_db()
