"""
Tests for BaseRepository.

This module contains comprehensive tests for the BaseRepository class,
testing all CRUD operations and common functionality.
"""

import pytest
from django.core.exceptions import ObjectDoesNotExist

from core.repositories.base import BaseRepository
from core.models import Recipe
from tests.factories import RecipeFactory
from .base import BaseRepositoryTest


class TestBaseRepository(BaseRepositoryTest):
    """Test BaseRepository functionality using Recipe model."""
    
    repository_class = BaseRepository
    model_class = Recipe
    factory_class = RecipeFactory
    
    def setup_method(self):
        """Set up test method with Recipe-specific repository."""
        self.repository = BaseRepository(Recipe)
    
    def get_create_data(self) -> dict:
        """Get data for creating recipe instances."""
        return {
            'name': 'Test Recipe',
            'description': 'A test recipe for brewing',
            'batch_size_gallons': 5.0,
            'mash_efficiency_percent': 75.0,
            'target_original_gravity': 1.050,
        }
    
    def get_update_data(self) -> dict:
        """Get data for updating recipe instances."""
        return {
            'name': 'Updated Recipe',
            'description': 'An updated test recipe',
            'batch_size_gallons': 10.0,
        }
    
    @pytest.mark.django_db
    def test_repository_initialization_with_model_class(self):
        """Test that repository initializes with correct model class."""
        repository = BaseRepository(Recipe)
        assert repository.model_class == Recipe
    
    @pytest.mark.django_db
    def test_get_by_id_with_valid_id(self):
        """Test getting instance by valid ID."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        
        # Act
        result = self.repository.get_by_id(recipe.id)
        
        # Assert
        assert result is not None
        assert result.id == recipe.id
        assert result.name == "Test Recipe"
    
    @pytest.mark.django_db
    def test_get_by_id_with_invalid_id(self):
        """Test getting instance by invalid ID."""
        # Act
        result = self.repository.get_by_id("invalid_id_123")
        
        # Assert
        assert result is None
    
    @pytest.mark.django_db
    def test_get_by_id_or_raise_with_valid_id(self):
        """Test getting instance by valid ID or raising exception."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        
        # Act
        result = self.repository.get_by_id_or_raise(recipe.id)
        
        # Assert
        assert result is not None
        assert result.id == recipe.id
        assert result.name == "Test Recipe"
    
    @pytest.mark.django_db
    def test_get_by_id_or_raise_with_invalid_id(self):
        """Test getting instance by invalid ID raises exception."""
        # Act & Assert
        with pytest.raises(ObjectDoesNotExist):
            self.repository.get_by_id_or_raise("invalid_id_123")
    
    @pytest.mark.django_db
    def test_get_all_returns_all_instances(self):
        """Test that get_all returns all instances."""
        # Arrange
        recipe1 = RecipeFactory.create_in_db(name="Recipe 1")
        recipe2 = RecipeFactory.create_in_db(name="Recipe 2")
        recipe3 = RecipeFactory.create_in_db(name="Recipe 3")
        
        # Act
        result = self.repository.get_all()
        
        # Assert
        assert len(result) == 3
        result_ids = [r.id for r in result]
        assert recipe1.id in result_ids
        assert recipe2.id in result_ids
        assert recipe3.id in result_ids
    
    @pytest.mark.django_db
    def test_filter_by_single_criteria(self):
        """Test filtering by single criteria."""
        # Arrange
        recipe1 = RecipeFactory.create_in_db(name="IPA Recipe", batch_size_gallons=5.0)
        recipe2 = RecipeFactory.create_in_db(name="Stout Recipe", batch_size_gallons=10.0)
        
        # Act
        result = self.repository.filter(batch_size_gallons=5.0)
        
        # Assert
        assert len(result) == 1
        assert result[0].id == recipe1.id
        assert result[0].name == "IPA Recipe"
    
    @pytest.mark.django_db
    def test_filter_by_multiple_criteria(self):
        """Test filtering by multiple criteria."""
        # Arrange
        recipe1 = RecipeFactory.create_in_db(
            name="IPA Recipe", 
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0
        )
        recipe2 = RecipeFactory.create_in_db(
            name="Stout Recipe", 
            batch_size_gallons=5.0,
            mash_efficiency_percent=80.0
        )
        
        # Act
        result = self.repository.filter(
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0
        )
        
        # Assert
        assert len(result) == 1
        assert result[0].id == recipe1.id
        assert result[0].name == "IPA Recipe"
    
    @pytest.mark.django_db
    def test_filter_no_matches(self):
        """Test filtering with criteria that match nothing."""
        # Arrange
        RecipeFactory.create_in_db(name="IPA Recipe", batch_size_gallons=5.0)
        
        # Act
        result = self.repository.filter(batch_size_gallons=20.0)
        
        # Assert
        assert result == []
    
    @pytest.mark.django_db
    def test_create_new_instance(self):
        """Test creating a new instance."""
        # Arrange
        create_data = {
            'name': 'New Recipe',
            'description': 'A newly created recipe',
            'batch_size_gallons': 5.0,
            'mash_efficiency_percent': 75.0,
            'target_original_gravity': 1.050,
        }
        
        # Act
        result = self.repository.create(**create_data)
        
        # Assert
        assert result is not None
        assert result.id is not None
        assert result.name == 'New Recipe'
        assert result.description == 'A newly created recipe'
        assert result.batch_size_gallons == 5.0
        assert result.mash_efficiency_percent == 75.0
        assert result.target_original_gravity == 1.050
        
        # Verify it was saved to database
        saved_recipe = Recipe.objects.get(id=result.id)
        assert saved_recipe.name == 'New Recipe'
    
    @pytest.mark.django_db
    def test_update_existing_instance(self):
        """Test updating an existing instance."""
        # Arrange
        recipe = RecipeFactory.create_in_db(
            name="Original Recipe",
            description="Original description",
            batch_size_gallons=5.0
        )
        update_data = {
            'name': 'Updated Recipe',
            'description': 'Updated description',
            'batch_size_gallons': 10.0,
        }
        
        # Act
        result = self.repository.update(recipe, **update_data)
        
        # Assert
        assert result is not None
        assert result.id == recipe.id
        assert result.name == 'Updated Recipe'
        assert result.description == 'Updated description'
        assert result.batch_size_gallons == 10.0
        
        # Verify it was saved to database
        updated_recipe = Recipe.objects.get(id=recipe.id)
        assert updated_recipe.name == 'Updated Recipe'
        assert updated_recipe.description == 'Updated description'
        assert updated_recipe.batch_size_gallons == 10.0
    
    @pytest.mark.django_db
    def test_delete_instance_soft_delete(self):
        """Test deleting an instance (soft delete)."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Recipe to Delete")
        recipe_id = recipe.id
        
        # Act
        self.repository.delete(recipe)
        
        # Assert
        # Should not be found in normal queries
        result = self.repository.get_by_id(recipe_id)
        assert result is None
        
        # Should not appear in get_all
        all_recipes = self.repository.get_all()
        recipe_ids = [r.id for r in all_recipes]
        assert recipe_id not in recipe_ids
        
        # But should exist in all_with_deleted queries
        deleted_recipe = Recipe.objects.all_with_deleted().filter(id=recipe_id).first()
        assert deleted_recipe is not None
        assert deleted_recipe.is_deleted
        assert deleted_recipe.deleted_at is not None
    
    @pytest.mark.django_db
    def test_exists_with_existing_instance(self):
        """Test exists method with existing instance."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Existing Recipe")
        
        # Act
        result = self.repository.exists(id=recipe.id)
        
        # Assert
        assert result is True
    
    @pytest.mark.django_db
    def test_exists_with_non_existing_instance(self):
        """Test exists method with non-existing instance."""
        # Act
        result = self.repository.exists(id="non_existing_id")
        
        # Assert
        assert result is False
    
    @pytest.mark.django_db
    def test_exists_with_multiple_criteria(self):
        """Test exists method with multiple criteria."""
        # Arrange
        recipe = RecipeFactory.create_in_db(
            name="Test Recipe",
            batch_size_gallons=5.0
        )
        
        # Act
        result = self.repository.exists(
            name="Test Recipe",
            batch_size_gallons=5.0
        )
        
        # Assert
        assert result is True
    
    @pytest.mark.django_db
    def test_count_with_no_instances(self):
        """Test count method with no instances."""
        # Act
        result = self.repository.count()
        
        # Assert
        assert result == 0
    
    @pytest.mark.django_db
    def test_count_with_instances(self):
        """Test count method with instances."""
        # Arrange
        RecipeFactory.create_in_db(name="Recipe 1")
        RecipeFactory.create_in_db(name="Recipe 2")
        RecipeFactory.create_in_db(name="Recipe 3")
        
        # Act
        result = self.repository.count()
        
        # Assert
        assert result == 3
    
    @pytest.mark.django_db
    def test_count_with_filter_criteria(self):
        """Test count method with filter criteria."""
        # Arrange
        RecipeFactory.create_in_db(name="IPA Recipe", batch_size_gallons=5.0)
        RecipeFactory.create_in_db(name="Stout Recipe", batch_size_gallons=10.0)
        RecipeFactory.create_in_db(name="Lager Recipe", batch_size_gallons=5.0)
        
        # Act
        result = self.repository.count(batch_size_gallons=5.0)
        
        # Assert
        assert result == 2
