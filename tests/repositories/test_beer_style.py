"""
Tests for BeerStyleRepository.

This module contains comprehensive tests for the BeerStyleRepository class,
testing all data access operations with proper mocking.
"""

import pytest
from unittest.mock import Mock, patch

from core.models import BeerStyle
from core.repositories.beer_style import BeerStyleRepository


class TestBeerStyleRepository:
    """Test the BeerStyleRepository class."""

    def setUp(self):
        """Set up test case."""
        self.repository = BeerStyleRepository()

    @pytest.mark.django_db
    def test_repository_initialization(self):
        """Test that repository initializes correctly."""
        repo = BeerStyleRepository()
        assert repo.model == BeerStyle

    @pytest.mark.django_db
    def test_search_by_name(self):
        """Test searching beer styles by name."""
        # Create test beer styles
        BeerStyle.objects.create(name="American IPA", description="Hoppy beer")
        BeerStyle.objects.create(name="English IPA", description="Traditional IPA")
        BeerStyle.objects.create(name="Imperial Stout", description="Strong dark beer")

        repo = BeerStyleRepository()

        # Search for IPA styles
        ipa_styles = repo.search_by_name("IPA")
        assert len(ipa_styles) == 2
        style_names = [style.name for style in ipa_styles]
        assert "American IPA" in style_names
        assert "English IPA" in style_names

        # Search for specific style
        american_styles = repo.search_by_name("American")
        assert len(american_styles) == 1
        assert american_styles[0].name == "American IPA"

        # Case insensitive search
        lower_search = repo.search_by_name("imperial")
        assert len(lower_search) == 1
        assert lower_search[0].name == "Imperial Stout"

    @pytest.mark.django_db
    def test_get_default_style(self):
        """Test getting the default 'Custom Style' beer style."""
        # Create the default style
        default_style = BeerStyle.objects.create(
            name="Custom Style",
            description="Default custom style"
        )

        repo = BeerStyleRepository()
        result = repo.get_default_style()

        assert result is not None
        assert result.name == "Custom Style"
        assert result.id == default_style.id

    @pytest.mark.django_db
    def test_get_default_style_not_found(self):
        """Test getting default style when it doesn't exist."""
        repo = BeerStyleRepository()
        result = repo.get_default_style()

        assert result is None

    @pytest.mark.django_db
    def test_get_styles_for_recipe_stats(self):
        """Test finding styles that match recipe statistics."""
        # Create test styles with different ranges
        ipa_style = BeerStyle.objects.create(
            name="American IPA",
            srm_min=6.0, srm_max=14.0,
            ibu_min=40.0, ibu_max=70.0,
            og_min=1.056, og_max=1.070,
            fg_min=1.008, fg_max=1.014,
            abv_min=5.5, abv_max=7.5,
        )

        stout_style = BeerStyle.objects.create(
            name="Imperial Stout",
            srm_min=30.0, srm_max=40.0,
            ibu_min=50.0, ibu_max=90.0,
            og_min=1.075, og_max=1.115,
            fg_min=1.018, fg_max=1.030,
            abv_min=8.0, abv_max=12.0,
        )

        repo = BeerStyleRepository()

        # Test recipe stats that match IPA
        ipa_matches = repo.get_styles_for_recipe_stats(
            srm=10.0,  # Within IPA range
            ibu=55.0,  # Within IPA range
            og=1.062,  # Within IPA range
            fg=1.012,  # Within IPA range
            abv=6.5,   # Within IPA range
        )

        assert len(ipa_matches) == 1
        assert ipa_matches[0].name == "American IPA"

        # Test recipe stats that match Stout
        stout_matches = repo.get_styles_for_recipe_stats(
            srm=35.0,  # Within Stout range
            ibu=70.0,  # Within Stout range
            og=1.090,  # Within Stout range
            fg=1.024,  # Within Stout range
            abv=10.0,  # Within Stout range
        )

        assert len(stout_matches) == 1
        assert stout_matches[0].name == "Imperial Stout"

        # Test recipe stats that don't match any style
        no_matches = repo.get_styles_for_recipe_stats(
            srm=50.0,  # Too high for both styles
            ibu=100.0, # Too high for both styles
            og=1.200,  # Too high for both styles
            fg=1.050,  # Too high for both styles
            abv=15.0,  # Too high for both styles
        )

        assert len(no_matches) == 0

    @pytest.mark.django_db
    def test_get_styles_for_recipe_stats_partial_match(self):
        """Test finding styles with partial parameter matching."""
        # Create a style with only some ranges defined
        partial_style = BeerStyle.objects.create(
            name="Partial Style",
            srm_min=5.0, srm_max=15.0,
            # No IBU, OG, FG, or ABV ranges defined
        )

        repo = BeerStyleRepository()

        # Should match when SRM is in range, regardless of other parameters
        matches = repo.get_styles_for_recipe_stats(
            srm=10.0,  # Within range
            ibu=100.0, # Would be out of range if defined, but isn't
            og=1.200,  # Would be out of range if defined, but isn't
        )

        assert len(matches) == 1
        assert matches[0].name == "Partial Style"

    @pytest.mark.django_db
    def test_get_styles_with_ranges(self):
        """Test getting styles that have at least one defined range."""
        # Create styles with different range configurations
        full_style = BeerStyle.objects.create(
            name="Full Style",
            srm_min=5.0, srm_max=15.0,
            ibu_min=20.0, ibu_max=40.0,
        )

        partial_style = BeerStyle.objects.create(
            name="Partial Style",
            srm_min=10.0,  # Only min value
        )

        no_ranges_style = BeerStyle.objects.create(
            name="No Ranges Style",
            # No ranges defined
        )

        repo = BeerStyleRepository()
        styles_with_ranges = repo.get_styles_with_ranges()

        # Should return styles that have at least one range defined
        assert len(styles_with_ranges) == 2
        style_names = [style.name for style in styles_with_ranges]
        assert "Full Style" in style_names
        assert "Partial Style" in style_names
        assert "No Ranges Style" not in style_names

    @pytest.mark.django_db
    def test_get_styles_for_recipe_stats_none_parameters(self):
        """Test recipe stats matching with None parameters."""
        BeerStyle.objects.create(
            name="Test Style",
            srm_min=5.0, srm_max=15.0,
        )

        repo = BeerStyleRepository()

        # Should return all styles when no parameters are provided
        matches = repo.get_styles_for_recipe_stats()
        assert len(matches) == 1
        assert matches[0].name == "Test Style"

        # Should work with some None parameters
        matches = repo.get_styles_for_recipe_stats(
            srm=10.0,  # Within range
            ibu=None,  # Not specified
            og=None,   # Not specified
        )
        assert len(matches) == 1
        assert matches[0].name == "Test Style"

    @pytest.mark.django_db
    def test_repository_inherits_base_functionality(self):
        """Test that BeerStyleRepository inherits BaseRepository functionality."""
        style = BeerStyle.objects.create(name="Test Style")

        repo = BeerStyleRepository()

        # Test get_by_id
        retrieved = repo.get_by_id(style.id)
        assert retrieved is not None
        assert retrieved.name == "Test Style"

        # Test get_all
        all_styles = repo.get_all()
        assert len(all_styles) == 1
        assert all_styles[0].name == "Test Style"

        # Test create
        created = repo.create(name="New Style", description="New description")
        assert created.name == "New Style"
        assert created.description == "New description"

        # Test update
        created.description = "Updated description"
        updated = repo.update(created)
        assert updated.description == "Updated description"

        # Test delete (soft delete)
        repo.delete(created)
        assert created.is_deleted

        # Should not appear in default get_all
        all_styles = repo.get_all()
        assert len(all_styles) == 1  # Only the first style
        assert all_styles[0].name == "Test Style"
