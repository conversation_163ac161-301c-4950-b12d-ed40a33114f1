"""
Tests for FermentableRepository.

This module contains comprehensive tests for the FermentableRepository class,
testing all fermentable-specific operations and queries.
"""

import pytest

from core.repositories.fermentable import FermentableRepository
from core.models import Fermentable, FermentableType
from tests.factories import FermentableFactory
from .base import BaseRepositoryTest


class TestFermentableRepository(BaseRepositoryTest):
    """Test FermentableRepository functionality."""

    repository_class = FermentableRepository
    model_class = Fermentable
    factory_class = FermentableFactory

    def get_create_data(self) -> dict:
        """Get data for creating fermentable instances."""
        return {
            'name': 'Test Fermentable',
            'fermentable_type': FermentableType.BASE_MALT,
            'color_lovibond': 2.0,
            'extract_potential_ppg': 37.0,
            'requires_mashing': True,
            'country_of_origin': 'US',
            'notes': 'Test fermentable notes',
        }

    def get_update_data(self) -> dict:
        """Get data for updating fermentable instances."""
        return {
            'name': 'Updated Fermentable',
            'notes': 'Updated fermentable notes',
            'color_lovibond': 4.0,
        }

    @pytest.mark.django_db
    def test_get_by_type_base_malt(self):
        """Test getting fermentables by base malt type."""
        # Arrange
        base_malt1 = FermentableFactory.create_in_db(
            name="Pale 2-Row",
            fermentable_type=FermentableType.BASE_MALT
        )
        base_malt2 = FermentableFactory.create_in_db(
            name="Pilsner Malt",
            fermentable_type=FermentableType.BASE_MALT
        )
        specialty_malt = FermentableFactory.create_in_db(
            name="Crystal 60L",
            fermentable_type=FermentableType.CRYSTAL_CARAMEL
        )

        # Act
        result = self.repository.get_by_type(FermentableType.BASE_MALT)

        # Assert
        assert len(result) == 2
        result_ids = [f.id for f in result]
        assert base_malt1.id in result_ids
        assert base_malt2.id in result_ids
        assert specialty_malt.id not in result_ids

    @pytest.mark.django_db
    def test_get_by_type_crystal_caramel(self):
        """Test getting fermentables by crystal/caramel type."""
        # Arrange
        crystal_malt = FermentableFactory.create_in_db(
            name="Crystal 60L",
            fermentable_type=FermentableType.CRYSTAL_CARAMEL
        )
        base_malt = FermentableFactory.create_in_db(
            name="Pale 2-Row",
            fermentable_type=FermentableType.BASE_MALT
        )

        # Act
        result = self.repository.get_by_type(FermentableType.CRYSTAL_CARAMEL)

        # Assert
        assert len(result) == 1
        assert result[0].id == crystal_malt.id
        assert result[0].name == "Crystal 60L"

    @pytest.mark.django_db
    def test_get_by_type_no_matches(self):
        """Test getting fermentables by type with no matches."""
        # Arrange
        FermentableFactory.create_in_db(
            name="Pale 2-Row",
            fermentable_type=FermentableType.BASE_MALT
        )

        # Act
        result = self.repository.get_by_type(FermentableType.ROASTED)

        # Assert
        assert result == []

    @pytest.mark.django_db
    def test_get_base_malts(self):
        """Test getting all base malt fermentables."""
        # Arrange
        base_malt1 = FermentableFactory.create_in_db(
            name="Pale 2-Row",
            fermentable_type=FermentableType.BASE_MALT
        )
        base_malt2 = FermentableFactory.create_in_db(
            name="Pilsner Malt",
            fermentable_type=FermentableType.BASE_MALT
        )
        specialty_malt = FermentableFactory.create_in_db(
            name="Crystal 60L",
            fermentable_type=FermentableType.CRYSTAL_CARAMEL
        )

        # Act
        result = self.repository.get_base_malts()

        # Assert
        assert len(result) == 2
        result_ids = [f.id for f in result]
        assert base_malt1.id in result_ids
        assert base_malt2.id in result_ids
        assert specialty_malt.id not in result_ids

    @pytest.mark.django_db
    def test_get_specialty_malts(self):
        """Test getting all specialty malt fermentables."""
        # Arrange
        base_malt = FermentableFactory.create_in_db(
            name="Pale 2-Row",
            fermentable_type=FermentableType.BASE_MALT
        )
        specialty_malt = FermentableFactory.create_in_db(
            name="Munich Malt",
            fermentable_type=FermentableType.SPECIALTY_MALT
        )
        crystal_malt = FermentableFactory.create_in_db(
            name="Crystal 60L",
            fermentable_type=FermentableType.CRYSTAL_CARAMEL
        )
        roasted_malt = FermentableFactory.create_in_db(
            name="Chocolate Malt",
            fermentable_type=FermentableType.ROASTED
        )

        # Act
        result = self.repository.get_specialty_malts()

        # Assert
        assert len(result) == 3
        result_ids = [f.id for f in result]
        assert specialty_malt.id in result_ids
        assert crystal_malt.id in result_ids
        assert roasted_malt.id in result_ids
        assert base_malt.id not in result_ids

    @pytest.mark.django_db
    def test_search_by_name_partial_match(self):
        """Test searching fermentables by partial name match."""
        # Arrange
        fermentable1 = FermentableFactory.create_in_db(name="Pale Ale Malt")
        fermentable2 = FermentableFactory.create_in_db(name="Munich Pale")
        fermentable3 = FermentableFactory.create_in_db(name="Crystal 60L")

        # Act
        result = self.repository.search_by_name("pale")

        # Assert
        assert len(result) == 2
        result_ids = [f.id for f in result]
        assert fermentable1.id in result_ids
        assert fermentable2.id in result_ids
        assert fermentable3.id not in result_ids

    @pytest.mark.django_db
    def test_search_by_name_case_insensitive(self):
        """Test searching fermentables by name is case insensitive."""
        # Arrange
        fermentable1 = FermentableFactory.create_in_db(name="Pale Ale Malt")
        fermentable2 = FermentableFactory.create_in_db(name="MUNICH PALE")
        fermentable3 = FermentableFactory.create_in_db(name="Crystal 60L")

        # Act
        result = self.repository.search_by_name("PALE")

        # Assert
        assert len(result) == 2
        result_ids = [f.id for f in result]
        assert fermentable1.id in result_ids
        assert fermentable2.id in result_ids
        assert fermentable3.id not in result_ids

    @pytest.mark.django_db
    def test_search_by_name_no_matches(self):
        """Test searching fermentables by name with no matches."""
        # Arrange
        FermentableFactory.create_in_db(name="Pale Ale Malt")
        FermentableFactory.create_in_db(name="Crystal 60L")

        # Act
        result = self.repository.search_by_name("wheat")

        # Assert
        assert result == []

    @pytest.mark.django_db
    def test_get_by_country_exact_match(self):
        """Test getting fermentables by country with exact match."""
        # Arrange
        german_malt1 = FermentableFactory.create_in_db(
            name="Munich Malt",
            country_of_origin="Germany"
        )
        german_malt2 = FermentableFactory.create_in_db(
            name="Vienna Malt",
            country_of_origin="Germany"
        )
        us_malt = FermentableFactory.create_in_db(
            name="Pale 2-Row",
            country_of_origin="US"
        )

        # Act
        result = self.repository.get_by_country("Germany")

        # Assert
        assert len(result) == 2
        result_ids = [f.id for f in result]
        assert german_malt1.id in result_ids
        assert german_malt2.id in result_ids
        assert us_malt.id not in result_ids

    @pytest.mark.django_db
    def test_get_by_country_case_insensitive(self):
        """Test getting fermentables by country is case insensitive."""
        # Arrange
        german_malt = FermentableFactory.create_in_db(
            name="Munich Malt",
            country_of_origin="Germany"
        )
        us_malt = FermentableFactory.create_in_db(
            name="Pale 2-Row",
            country_of_origin="US"
        )

        # Act
        result = self.repository.get_by_country("germany")

        # Assert
        assert len(result) == 1
        assert result[0].id == german_malt.id

    @pytest.mark.django_db
    def test_get_by_country_no_matches(self):
        """Test getting fermentables by country with no matches."""
        # Arrange
        FermentableFactory.create_in_db(
            name="Munich Malt",
            country_of_origin="Germany"
        )

        # Act
        result = self.repository.get_by_country("France")

        # Assert
        assert result == []

    @pytest.mark.django_db
    def test_get_requiring_mashing(self):
        """Test getting fermentables that require mashing."""
        # Arrange
        malt1 = FermentableFactory.create_in_db(
            name="Pale 2-Row",
            requires_mashing=True
        )
        malt2 = FermentableFactory.create_in_db(
            name="Munich Malt",
            requires_mashing=True
        )
        extract = FermentableFactory.create_in_db(
            name="Light Malt Extract",
            requires_mashing=False
        )

        # Act
        result = self.repository.get_requiring_mashing()

        # Assert
        assert len(result) == 2
        result_ids = [f.id for f in result]
        assert malt1.id in result_ids
        assert malt2.id in result_ids
        assert extract.id not in result_ids

    @pytest.mark.django_db
    def test_get_extracts(self):
        """Test getting extract fermentables (don't require mashing)."""
        # Arrange
        malt = FermentableFactory.create_in_db(
            name="Pale 2-Row",
            requires_mashing=True
        )
        extract1 = FermentableFactory.create_in_db(
            name="Light Malt Extract",
            requires_mashing=False
        )
        extract2 = FermentableFactory.create_in_db(
            name="Dark Malt Extract",
            requires_mashing=False
        )

        # Act
        result = self.repository.get_extracts()

        # Assert
        assert len(result) == 2
        result_ids = [f.id for f in result]
        assert extract1.id in result_ids
        assert extract2.id in result_ids
        assert malt.id not in result_ids

    @pytest.mark.django_db
    def test_repository_inherits_base_functionality(self):
        """Test that FermentableRepository inherits all base repository functionality."""
        # Test that all base methods are available
        assert hasattr(self.repository, 'get_by_id')
        assert hasattr(self.repository, 'get_by_id_or_raise')
        assert hasattr(self.repository, 'get_all')
        assert hasattr(self.repository, 'filter')
        assert hasattr(self.repository, 'create')
        assert hasattr(self.repository, 'update')
        assert hasattr(self.repository, 'delete')
        assert hasattr(self.repository, 'exists')
        assert hasattr(self.repository, 'count')

        # Test that fermentable-specific methods are available
        assert hasattr(self.repository, 'get_by_type')
        assert hasattr(self.repository, 'get_base_malts')
        assert hasattr(self.repository, 'get_specialty_malts')
        assert hasattr(self.repository, 'search_by_name')
        assert hasattr(self.repository, 'get_by_country')
        assert hasattr(self.repository, 'get_requiring_mashing')
        assert hasattr(self.repository, 'get_extracts')

    @pytest.mark.django_db
    def test_model_class_is_fermentable(self):
        """Test that repository model class is Fermentable."""
        assert self.repository.model_class == Fermentable
