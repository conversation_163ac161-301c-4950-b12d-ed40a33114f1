"""
Tests for HopRepository.

This module contains comprehensive tests for the HopRepository class,
testing all hop-specific operations and queries.
"""

import pytest

from core.repositories.hop import HopRepository
from core.models import Hop
from tests.factories import HopFactory
from .base import BaseRepositoryTest


class TestHopRepository(BaseRepositoryTest):
    """Test HopRepository functionality."""

    repository_class = HopRepository
    model_class = Hop
    factory_class = HopFactory

    def get_create_data(self) -> dict:
        """Get data for creating hop instances."""
        return {
            'name': 'Test Hop',
            'alpha_acid': 5.5,
            'beta_acid': 4.5,
            'aroma': True,
            'bittering': False,
            'country_of_origin': 'US',
            'notes': 'Test hop notes',
        }

    def get_update_data(self) -> dict:
        """Get data for updating hop instances."""
        return {
            'name': 'Updated Hop',
            'alpha_acid': 7.0,
            'notes': 'Updated hop notes',
        }

    @pytest.mark.django_db
    def test_get_aroma_hops(self):
        """Test getting hops suitable for aroma."""
        # Arrange
        aroma_hop1 = HopFactory.create_in_db(name="Cascade", aroma=True, bittering=False)
        aroma_hop2 = HopFactory.create_in_db(name="Centennial", aroma=True, bittering=True)
        bittering_hop = HopFactory.create_in_db(name="Magnum", aroma=False, bittering=True)

        # Act
        result = self.repository.get_aroma_hops()

        # Assert
        assert len(result) == 2
        result_ids = [h.id for h in result]
        assert aroma_hop1.id in result_ids
        assert aroma_hop2.id in result_ids
        assert bittering_hop.id not in result_ids

    @pytest.mark.django_db
    def test_get_bittering_hops(self):
        """Test getting hops suitable for bittering."""
        # Arrange
        bittering_hop1 = HopFactory.create_in_db(name="Magnum", aroma=False, bittering=True)
        bittering_hop2 = HopFactory.create_in_db(name="Centennial", aroma=True, bittering=True)
        aroma_hop = HopFactory.create_in_db(name="Cascade", aroma=True, bittering=False)

        # Act
        result = self.repository.get_bittering_hops()

        # Assert
        assert len(result) == 2
        result_ids = [h.id for h in result]
        assert bittering_hop1.id in result_ids
        assert bittering_hop2.id in result_ids
        assert aroma_hop.id not in result_ids

    @pytest.mark.django_db
    def test_get_dual_purpose_hops(self):
        """Test getting hops suitable for both aroma and bittering."""
        # Arrange
        dual_purpose_hop1 = HopFactory.create_in_db(name="Centennial", aroma=True, bittering=True)
        dual_purpose_hop2 = HopFactory.create_in_db(name="Chinook", aroma=True, bittering=True)
        aroma_only_hop = HopFactory.create_in_db(name="Cascade", aroma=True, bittering=False)
        bittering_only_hop = HopFactory.create_in_db(name="Magnum", aroma=False, bittering=True)

        # Act
        result = self.repository.get_dual_purpose_hops()

        # Assert
        assert len(result) == 2
        result_ids = [h.id for h in result]
        assert dual_purpose_hop1.id in result_ids
        assert dual_purpose_hop2.id in result_ids
        assert aroma_only_hop.id not in result_ids
        assert bittering_only_hop.id not in result_ids

    @pytest.mark.django_db
    def test_search_by_name_partial_match(self):
        """Test searching hops by partial name match."""
        # Arrange
        hop1 = HopFactory.create_in_db(name="Cascade")
        hop2 = HopFactory.create_in_db(name="Centennial")
        hop3 = HopFactory.create_in_db(name="Magnum")

        # Act
        result = self.repository.search_by_name("C")

        # Assert
        assert len(result) == 2
        result_ids = [h.id for h in result]
        assert hop1.id in result_ids
        assert hop2.id in result_ids
        assert hop3.id not in result_ids

    @pytest.mark.django_db
    def test_search_by_name_case_insensitive(self):
        """Test searching hops by name is case insensitive."""
        # Arrange
        hop1 = HopFactory.create_in_db(name="Cascade")
        hop2 = HopFactory.create_in_db(name="CENTENNIAL")
        hop3 = HopFactory.create_in_db(name="Magnum")

        # Act
        result = self.repository.search_by_name("cascade")

        # Assert
        assert len(result) == 1
        assert result[0].id == hop1.id
        assert result[0].name == "Cascade"

    @pytest.mark.django_db
    def test_search_by_name_no_matches(self):
        """Test searching hops by name with no matches."""
        # Arrange
        HopFactory.create_in_db(name="Cascade")
        HopFactory.create_in_db(name="Centennial")

        # Act
        result = self.repository.search_by_name("Saaz")

        # Assert
        assert result == []

    @pytest.mark.django_db
    def test_get_by_country_exact_match(self):
        """Test getting hops by country with exact match."""
        # Arrange
        us_hop1 = HopFactory.create_in_db(name="Cascade", country_of_origin="US")
        us_hop2 = HopFactory.create_in_db(name="Centennial", country_of_origin="US")
        german_hop = HopFactory.create_in_db(name="Hallertau", country_of_origin="Germany")

        # Act
        result = self.repository.get_by_country("US")

        # Assert
        assert len(result) == 2
        result_ids = [h.id for h in result]
        assert us_hop1.id in result_ids
        assert us_hop2.id in result_ids
        assert german_hop.id not in result_ids

    @pytest.mark.django_db
    def test_get_by_country_case_insensitive(self):
        """Test getting hops by country is case insensitive."""
        # Arrange
        german_hop = HopFactory.create_in_db(name="Hallertau", country_of_origin="Germany")
        us_hop = HopFactory.create_in_db(name="Cascade", country_of_origin="US")

        # Act
        result = self.repository.get_by_country("germany")

        # Assert
        assert len(result) == 1
        assert result[0].id == german_hop.id

    @pytest.mark.django_db
    def test_get_by_country_no_matches(self):
        """Test getting hops by country with no matches."""
        # Arrange
        HopFactory.create_in_db(name="Cascade", country_of_origin="US")

        # Act
        result = self.repository.get_by_country("Czech Republic")

        # Assert
        assert result == []

    @pytest.mark.django_db
    def test_get_high_alpha_hops_default_threshold(self):
        """Test getting high alpha acid hops with default threshold."""
        # Arrange
        high_alpha_hop1 = HopFactory.create_in_db(name="Magnum", alpha_acid=12.0)
        high_alpha_hop2 = HopFactory.create_in_db(name="Warrior", alpha_acid=15.0)
        medium_alpha_hop = HopFactory.create_in_db(name="Centennial", alpha_acid=9.0)
        low_alpha_hop = HopFactory.create_in_db(name="Cascade", alpha_acid=5.5)

        # Act
        result = self.repository.get_high_alpha_hops()

        # Assert
        assert len(result) == 2
        result_ids = [h.id for h in result]
        assert high_alpha_hop1.id in result_ids
        assert high_alpha_hop2.id in result_ids
        assert medium_alpha_hop.id not in result_ids
        assert low_alpha_hop.id not in result_ids

    @pytest.mark.django_db
    def test_get_high_alpha_hops_custom_threshold(self):
        """Test getting high alpha acid hops with custom threshold."""
        # Arrange
        very_high_alpha_hop = HopFactory.create_in_db(name="Warrior", alpha_acid=15.0)
        high_alpha_hop = HopFactory.create_in_db(name="Magnum", alpha_acid=12.0)
        medium_alpha_hop = HopFactory.create_in_db(name="Centennial", alpha_acid=9.0)

        # Act
        result = self.repository.get_high_alpha_hops(min_alpha=13.0)

        # Assert
        assert len(result) == 1
        assert result[0].id == very_high_alpha_hop.id
        assert result[0].name == "Warrior"

    @pytest.mark.django_db
    def test_get_low_alpha_hops_default_threshold(self):
        """Test getting low alpha acid hops with default threshold."""
        # Arrange
        low_alpha_hop1 = HopFactory.create_in_db(name="Cascade", alpha_acid=5.5)
        low_alpha_hop2 = HopFactory.create_in_db(name="Willamette", alpha_acid=4.0)
        medium_alpha_hop = HopFactory.create_in_db(name="Centennial", alpha_acid=9.0)
        high_alpha_hop = HopFactory.create_in_db(name="Magnum", alpha_acid=12.0)

        # Act
        result = self.repository.get_low_alpha_hops()

        # Assert
        assert len(result) == 2
        result_ids = [h.id for h in result]
        assert low_alpha_hop1.id in result_ids
        assert low_alpha_hop2.id in result_ids
        assert medium_alpha_hop.id not in result_ids
        assert high_alpha_hop.id not in result_ids

    @pytest.mark.django_db
    def test_get_low_alpha_hops_custom_threshold(self):
        """Test getting low alpha acid hops with custom threshold."""
        # Arrange
        very_low_alpha_hop = HopFactory.create_in_db(name="Willamette", alpha_acid=4.0)
        low_alpha_hop = HopFactory.create_in_db(name="Cascade", alpha_acid=5.5)
        medium_alpha_hop = HopFactory.create_in_db(name="Centennial", alpha_acid=9.0)

        # Act
        result = self.repository.get_low_alpha_hops(max_alpha=5.0)

        # Assert
        assert len(result) == 1
        assert result[0].id == very_low_alpha_hop.id
        assert result[0].name == "Willamette"

    @pytest.mark.django_db
    def test_get_by_alpha_acid_range(self):
        """Test getting hops within a specific alpha acid range."""
        # Arrange
        low_alpha_hop = HopFactory.create_in_db(name="Willamette", alpha_acid=4.0)
        medium_alpha_hop1 = HopFactory.create_in_db(name="Cascade", alpha_acid=5.5)
        medium_alpha_hop2 = HopFactory.create_in_db(name="Centennial", alpha_acid=9.0)
        high_alpha_hop = HopFactory.create_in_db(name="Magnum", alpha_acid=12.0)

        # Act
        result = self.repository.get_by_alpha_acid_range(5.0, 10.0)

        # Assert
        assert len(result) == 2
        result_ids = [h.id for h in result]
        assert medium_alpha_hop1.id in result_ids
        assert medium_alpha_hop2.id in result_ids
        assert low_alpha_hop.id not in result_ids
        assert high_alpha_hop.id not in result_ids

    @pytest.mark.django_db
    def test_repository_inherits_base_functionality(self):
        """Test that HopRepository inherits all base repository functionality."""
        # Test that all base methods are available
        assert hasattr(self.repository, 'get_by_id')
        assert hasattr(self.repository, 'get_by_id_or_raise')
        assert hasattr(self.repository, 'get_all')
        assert hasattr(self.repository, 'filter')
        assert hasattr(self.repository, 'create')
        assert hasattr(self.repository, 'update')
        assert hasattr(self.repository, 'delete')
        assert hasattr(self.repository, 'exists')
        assert hasattr(self.repository, 'count')

        # Test that hop-specific methods are available
        assert hasattr(self.repository, 'get_aroma_hops')
        assert hasattr(self.repository, 'get_bittering_hops')
        assert hasattr(self.repository, 'get_dual_purpose_hops')
        assert hasattr(self.repository, 'search_by_name')
        assert hasattr(self.repository, 'get_by_country')
        assert hasattr(self.repository, 'get_by_alpha_acid_range')
        assert hasattr(self.repository, 'get_high_alpha_hops')
        assert hasattr(self.repository, 'get_low_alpha_hops')

    @pytest.mark.django_db
    def test_model_class_is_hop(self):
        """Test that repository model class is Hop."""
        assert self.repository.model_class == Hop
