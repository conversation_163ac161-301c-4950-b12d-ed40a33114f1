"""
Tests for Hop Inclusion Repositories.

This module contains comprehensive tests for all hop inclusion repository classes,
testing all hop inclusion-specific operations and queries.
"""

import pytest

from core.repositories.hop_inclusion import (
    BoilHopInclusionRepository, FirstWortHopInclusionRepository,
    FlameoutHopInclusionRepository, WhirlpoolHopInclusionRepository,
    DryHopInclusionRepository
)
from core.models import (
    BoilHopInclusion, FirstWortHopInclusion, FlameoutHopInclusion,
    WhirlpoolHopInclusion, DryHopInclusion
)
from tests.factories import (
    BoilHopInclusionFactory, FirstWortHopInclusionFactory,
    FlameoutHopInclusionFactory, WhirlpoolHopInclusionFactory,
    DryHopInclusionFactory, RecipeFactory, HopFactory
)
from .base import BaseRepositoryTest


class TestBoilHopInclusionRepository(BaseRepositoryTest):
    """Test BoilHopInclusionRepository functionality."""

    repository_class = BoilHopInclusionRepository
    model_class = BoilHopInclusion
    factory_class = BoilHopInclusionFactory

    def get_create_data(self) -> dict:
        """Get data for creating boil hop inclusion instances."""
        recipe = RecipeFactory.create_in_db()
        hop = HopFactory.create_in_db()
        return {
            'recipe': recipe,
            'hop': hop,
            'quantity': 1.0,
            'quantity_unit': 'OUNCES',
            'time_minutes': 60,
            'notes': 'Test boil hop inclusion notes',
        }

    def get_update_data(self) -> dict:
        """Get data for updating boil hop inclusion instances."""
        return {
            'quantity': 2.0,
            'time_minutes': 30,
            'notes': 'Updated boil hop inclusion notes',
        }

    @pytest.mark.django_db
    def test_get_by_recipe(self):
        """Test getting boil hop inclusions by recipe."""
        # Arrange
        recipe1 = RecipeFactory.create_in_db(name="Recipe 1")
        recipe2 = RecipeFactory.create_in_db(name="Recipe 2")

        inclusion1 = BoilHopInclusionFactory.create_in_db(recipe=recipe1)
        inclusion2 = BoilHopInclusionFactory.create_in_db(recipe=recipe1)
        inclusion3 = BoilHopInclusionFactory.create_in_db(recipe=recipe2)

        # Act
        result = self.repository.get_by_recipe(recipe1.id)

        # Assert
        assert len(result) == 2
        result_ids = [i.id for i in result]
        assert inclusion1.id in result_ids
        assert inclusion2.id in result_ids
        assert inclusion3.id not in result_ids

    @pytest.mark.django_db
    def test_get_by_recipe_with_hops(self):
        """Test getting boil hop inclusions with hop data prefetched."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Recipe with Hops")
        hop = HopFactory.create_in_db(name="Test Hop")
        inclusion = BoilHopInclusionFactory.create_in_db(recipe=recipe, hop=hop)

        # Act
        result = self.repository.get_by_recipe_with_hops(recipe.id)

        # Assert
        assert len(result) == 1
        assert result[0].id == inclusion.id
        assert result[0].hop.name == "Test Hop"
        assert result[0].hop.id == hop.id

    @pytest.mark.django_db
    def test_exists_for_recipe_and_hop_true(self):
        """Test checking if boil hop inclusion exists when it does."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        hop = HopFactory.create_in_db(name="Test Hop")
        BoilHopInclusionFactory.create_in_db(recipe=recipe, hop=hop)

        # Act
        result = self.repository.exists_for_recipe_and_hop(recipe.id, hop.id)

        # Assert
        assert result is True

    @pytest.mark.django_db
    def test_exists_for_recipe_and_hop_false(self):
        """Test checking if boil hop inclusion exists when it doesn't."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        hop = HopFactory.create_in_db(name="Test Hop")

        # Act
        result = self.repository.exists_for_recipe_and_hop(recipe.id, hop.id)

        # Assert
        assert result is False

    @pytest.mark.django_db
    def test_repository_inherits_base_functionality(self):
        """Test that BoilHopInclusionRepository inherits all base repository functionality."""
        # Test that all base methods are available
        assert hasattr(self.repository, 'get_by_id')
        assert hasattr(self.repository, 'get_by_id_or_raise')
        assert hasattr(self.repository, 'get_all')
        assert hasattr(self.repository, 'filter')
        assert hasattr(self.repository, 'create')
        assert hasattr(self.repository, 'update')
        assert hasattr(self.repository, 'delete')
        assert hasattr(self.repository, 'exists')
        assert hasattr(self.repository, 'count')

        # Test that boil hop inclusion-specific methods are available
        assert hasattr(self.repository, 'get_by_recipe')
        assert hasattr(self.repository, 'get_by_recipe_with_hops')
        assert hasattr(self.repository, 'exists_for_recipe_and_hop')

    @pytest.mark.django_db
    def test_model_class_is_boil_hop_inclusion(self):
        """Test that repository model class is BoilHopInclusion."""
        assert self.repository.model_class == BoilHopInclusion


class TestFirstWortHopInclusionRepository(BaseRepositoryTest):
    """Test FirstWortHopInclusionRepository functionality."""

    repository_class = FirstWortHopInclusionRepository
    model_class = FirstWortHopInclusion
    factory_class = FirstWortHopInclusionFactory

    def get_create_data(self) -> dict:
        """Get data for creating first wort hop inclusion instances."""
        recipe = RecipeFactory.create_in_db()
        hop = HopFactory.create_in_db()
        return {
            'recipe': recipe,
            'hop': hop,
            'quantity': 1.0,
            'quantity_unit': 'OUNCES',
            'notes': 'Test first wort hop inclusion notes',
        }

    def get_update_data(self) -> dict:
        """Get data for updating first wort hop inclusion instances."""
        return {
            'quantity': 1.5,
            'notes': 'Updated first wort hop inclusion notes',
        }

    @pytest.mark.django_db
    def test_get_by_recipe(self):
        """Test getting first wort hop inclusions by recipe."""
        # Arrange
        recipe1 = RecipeFactory.create_in_db(name="Recipe 1")
        recipe2 = RecipeFactory.create_in_db(name="Recipe 2")

        inclusion1 = FirstWortHopInclusionFactory.create_in_db(recipe=recipe1)
        inclusion2 = FirstWortHopInclusionFactory.create_in_db(recipe=recipe2)

        # Act
        result = self.repository.get_by_recipe(recipe1.id)

        # Assert
        assert len(result) == 1
        assert result[0].id == inclusion1.id

    @pytest.mark.django_db
    def test_get_by_recipe_with_hops(self):
        """Test getting first wort hop inclusions with hop data prefetched."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Recipe with Hops")
        hop = HopFactory.create_in_db(name="Test Hop")
        inclusion = FirstWortHopInclusionFactory.create_in_db(recipe=recipe, hop=hop)

        # Act
        result = self.repository.get_by_recipe_with_hops(recipe.id)

        # Assert
        assert len(result) == 1
        assert result[0].id == inclusion.id
        assert result[0].hop.name == "Test Hop"
        assert result[0].hop.id == hop.id

    @pytest.mark.django_db
    def test_exists_for_recipe_and_hop_true(self):
        """Test checking if first wort hop inclusion exists when it does."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        hop = HopFactory.create_in_db(name="Test Hop")
        FirstWortHopInclusionFactory.create_in_db(recipe=recipe, hop=hop)

        # Act
        result = self.repository.exists_for_recipe_and_hop(recipe.id, hop.id)

        # Assert
        assert result is True

    @pytest.mark.django_db
    def test_exists_for_recipe_and_hop_false(self):
        """Test checking if first wort hop inclusion exists when it doesn't."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        hop = HopFactory.create_in_db(name="Test Hop")

        # Act
        result = self.repository.exists_for_recipe_and_hop(recipe.id, hop.id)

        # Assert
        assert result is False

    @pytest.mark.django_db
    def test_repository_inherits_base_functionality(self):
        """Test that FirstWortHopInclusionRepository inherits all base repository functionality."""
        # Test that all base methods are available
        assert hasattr(self.repository, 'get_by_id')
        assert hasattr(self.repository, 'get_by_id_or_raise')
        assert hasattr(self.repository, 'get_all')
        assert hasattr(self.repository, 'filter')
        assert hasattr(self.repository, 'create')
        assert hasattr(self.repository, 'update')
        assert hasattr(self.repository, 'delete')
        assert hasattr(self.repository, 'exists')
        assert hasattr(self.repository, 'count')

        # Test that first wort hop inclusion-specific methods are available
        assert hasattr(self.repository, 'get_by_recipe')
        assert hasattr(self.repository, 'get_by_recipe_with_hops')
        assert hasattr(self.repository, 'exists_for_recipe_and_hop')

    @pytest.mark.django_db
    def test_model_class_is_first_wort_hop_inclusion(self):
        """Test that repository model class is FirstWortHopInclusion."""
        assert self.repository.model_class == FirstWortHopInclusion


class TestFlameoutHopInclusionRepository(BaseRepositoryTest):
    """Test FlameoutHopInclusionRepository functionality."""

    repository_class = FlameoutHopInclusionRepository
    model_class = FlameoutHopInclusion
    factory_class = FlameoutHopInclusionFactory

    def get_create_data(self) -> dict:
        """Get data for creating flameout hop inclusion instances."""
        recipe = RecipeFactory.create_in_db()
        hop = HopFactory.create_in_db()
        return {
            'recipe': recipe,
            'hop': hop,
            'quantity': 1.0,
            'quantity_unit': 'OUNCES',
            'notes': 'Test flameout hop inclusion notes',
        }

    def get_update_data(self) -> dict:
        """Get data for updating flameout hop inclusion instances."""
        return {
            'quantity': 1.5,
            'notes': 'Updated flameout hop inclusion notes',
        }

    @pytest.mark.django_db
    def test_get_by_recipe(self):
        """Test getting flameout hop inclusions by recipe."""
        # Arrange
        recipe1 = RecipeFactory.create_in_db(name="Recipe 1")
        recipe2 = RecipeFactory.create_in_db(name="Recipe 2")

        inclusion1 = FlameoutHopInclusionFactory.create_in_db(recipe=recipe1)
        inclusion2 = FlameoutHopInclusionFactory.create_in_db(recipe=recipe2)

        # Act
        result = self.repository.get_by_recipe(recipe1.id)

        # Assert
        assert len(result) == 1
        assert result[0].id == inclusion1.id

    @pytest.mark.django_db
    def test_get_by_recipe_with_hops(self):
        """Test getting flameout hop inclusions with hop data prefetched."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Recipe with Hops")
        hop = HopFactory.create_in_db(name="Test Hop")
        inclusion = FlameoutHopInclusionFactory.create_in_db(recipe=recipe, hop=hop)

        # Act
        result = self.repository.get_by_recipe_with_hops(recipe.id)

        # Assert
        assert len(result) == 1
        assert result[0].id == inclusion.id
        assert result[0].hop.name == "Test Hop"
        assert result[0].hop.id == hop.id

    @pytest.mark.django_db
    def test_exists_for_recipe_and_hop_true(self):
        """Test checking if flameout hop inclusion exists when it does."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        hop = HopFactory.create_in_db(name="Test Hop")
        FlameoutHopInclusionFactory.create_in_db(recipe=recipe, hop=hop)

        # Act
        result = self.repository.exists_for_recipe_and_hop(recipe.id, hop.id)

        # Assert
        assert result is True

    @pytest.mark.django_db
    def test_exists_for_recipe_and_hop_false(self):
        """Test checking if flameout hop inclusion exists when it doesn't."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        hop = HopFactory.create_in_db(name="Test Hop")

        # Act
        result = self.repository.exists_for_recipe_and_hop(recipe.id, hop.id)

        # Assert
        assert result is False

    @pytest.mark.django_db
    def test_model_class_is_flameout_hop_inclusion(self):
        """Test that repository model class is FlameoutHopInclusion."""
        assert self.repository.model_class == FlameoutHopInclusion


class TestWhirlpoolHopInclusionRepository(BaseRepositoryTest):
    """Test WhirlpoolHopInclusionRepository functionality."""

    repository_class = WhirlpoolHopInclusionRepository
    model_class = WhirlpoolHopInclusion
    factory_class = WhirlpoolHopInclusionFactory

    def get_create_data(self) -> dict:
        """Get data for creating whirlpool hop inclusion instances."""
        recipe = RecipeFactory.create_in_db()
        hop = HopFactory.create_in_db()
        return {
            'recipe': recipe,
            'hop': hop,
            'quantity': 1.0,
            'quantity_unit': 'OUNCES',
            'temperature_f': 180,
            'time_minutes': 20,
            'notes': 'Test whirlpool hop inclusion notes',
        }

    def get_update_data(self) -> dict:
        """Get data for updating whirlpool hop inclusion instances."""
        return {
            'quantity': 1.5,
            'temperature_f': 170,
            'time_minutes': 30,
            'notes': 'Updated whirlpool hop inclusion notes',
        }

    @pytest.mark.django_db
    def test_get_by_recipe(self):
        """Test getting whirlpool hop inclusions by recipe."""
        # Arrange
        recipe1 = RecipeFactory.create_in_db(name="Recipe 1")
        recipe2 = RecipeFactory.create_in_db(name="Recipe 2")

        inclusion1 = WhirlpoolHopInclusionFactory.create_in_db(recipe=recipe1)
        inclusion2 = WhirlpoolHopInclusionFactory.create_in_db(recipe=recipe2)

        # Act
        result = self.repository.get_by_recipe(recipe1.id)

        # Assert
        assert len(result) == 1
        assert result[0].id == inclusion1.id

    @pytest.mark.django_db
    def test_get_by_recipe_with_hops(self):
        """Test getting whirlpool hop inclusions with hop data prefetched."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Recipe with Hops")
        hop = HopFactory.create_in_db(name="Test Hop")
        inclusion = WhirlpoolHopInclusionFactory.create_in_db(recipe=recipe, hop=hop)

        # Act
        result = self.repository.get_by_recipe_with_hops(recipe.id)

        # Assert
        assert len(result) == 1
        assert result[0].id == inclusion.id
        assert result[0].hop.name == "Test Hop"
        assert result[0].hop.id == hop.id

    @pytest.mark.django_db
    def test_exists_for_recipe_and_hop_true(self):
        """Test checking if whirlpool hop inclusion exists when it does."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        hop = HopFactory.create_in_db(name="Test Hop")
        WhirlpoolHopInclusionFactory.create_in_db(recipe=recipe, hop=hop)

        # Act
        result = self.repository.exists_for_recipe_and_hop(recipe.id, hop.id)

        # Assert
        assert result is True

    @pytest.mark.django_db
    def test_exists_for_recipe_and_hop_false(self):
        """Test checking if whirlpool hop inclusion exists when it doesn't."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        hop = HopFactory.create_in_db(name="Test Hop")

        # Act
        result = self.repository.exists_for_recipe_and_hop(recipe.id, hop.id)

        # Assert
        assert result is False

    @pytest.mark.django_db
    def test_model_class_is_whirlpool_hop_inclusion(self):
        """Test that repository model class is WhirlpoolHopInclusion."""
        assert self.repository.model_class == WhirlpoolHopInclusion


class TestDryHopInclusionRepository(BaseRepositoryTest):
    """Test DryHopInclusionRepository functionality."""

    repository_class = DryHopInclusionRepository
    model_class = DryHopInclusion
    factory_class = DryHopInclusionFactory

    def get_create_data(self) -> dict:
        """Get data for creating dry hop inclusion instances."""
        recipe = RecipeFactory.create_in_db()
        hop = HopFactory.create_in_db()
        return {
            'recipe': recipe,
            'hop': hop,
            'quantity': 1.0,
            'quantity_unit': 'OUNCES',
            'time_days': 3,
            'notes': 'Test dry hop inclusion notes',
        }

    def get_update_data(self) -> dict:
        """Get data for updating dry hop inclusion instances."""
        return {
            'quantity': 2.0,
            'time_days': 5,
            'notes': 'Updated dry hop inclusion notes',
        }

    @pytest.mark.django_db
    def test_get_by_recipe(self):
        """Test getting dry hop inclusions by recipe."""
        # Arrange
        recipe1 = RecipeFactory.create_in_db(name="Recipe 1")
        recipe2 = RecipeFactory.create_in_db(name="Recipe 2")

        inclusion1 = DryHopInclusionFactory.create_in_db(recipe=recipe1)
        inclusion2 = DryHopInclusionFactory.create_in_db(recipe=recipe2)

        # Act
        result = self.repository.get_by_recipe(recipe1.id)

        # Assert
        assert len(result) == 1
        assert result[0].id == inclusion1.id

    @pytest.mark.django_db
    def test_get_by_recipe_with_hops(self):
        """Test getting dry hop inclusions with hop data prefetched."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Recipe with Hops")
        hop = HopFactory.create_in_db(name="Test Hop")
        inclusion = DryHopInclusionFactory.create_in_db(recipe=recipe, hop=hop)

        # Act
        result = self.repository.get_by_recipe_with_hops(recipe.id)

        # Assert
        assert len(result) == 1
        assert result[0].id == inclusion.id
        assert result[0].hop.name == "Test Hop"
        assert result[0].hop.id == hop.id

    @pytest.mark.django_db
    def test_exists_for_recipe_and_hop_true(self):
        """Test checking if dry hop inclusion exists when it does."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        hop = HopFactory.create_in_db(name="Test Hop")
        DryHopInclusionFactory.create_in_db(recipe=recipe, hop=hop)

        # Act
        result = self.repository.exists_for_recipe_and_hop(recipe.id, hop.id)

        # Assert
        assert result is True

    @pytest.mark.django_db
    def test_exists_for_recipe_and_hop_false(self):
        """Test checking if dry hop inclusion exists when it doesn't."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        hop = HopFactory.create_in_db(name="Test Hop")

        # Act
        result = self.repository.exists_for_recipe_and_hop(recipe.id, hop.id)

        # Assert
        assert result is False

    @pytest.mark.django_db
    def test_model_class_is_dry_hop_inclusion(self):
        """Test that repository model class is DryHopInclusion."""
        assert self.repository.model_class == DryHopInclusion
