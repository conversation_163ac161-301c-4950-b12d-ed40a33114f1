"""
Tests for RecipeRepository.

This module contains comprehensive tests for the RecipeRepository class,
testing all recipe-specific operations and queries.
"""

import pytest

from core.repositories.recipe import RecipeRepository
from core.models import Recipe
from tests.factories import (
    RecipeFactory, UserFactory, FermentableInclusionFactory,
    BoilHopInclusionFactory, YeastInclusionFactory, MashStepFactory,
    FermentationPhaseFactory
)
from .base import BaseRepositoryTest


class TestRecipeRepository(BaseRepositoryTest):
    """Test RecipeRepository functionality."""
    
    repository_class = RecipeRepository
    model_class = Recipe
    factory_class = RecipeFactory
    
    def get_create_data(self) -> dict:
        """Get data for creating recipe instances."""
        return {
            'name': 'Test Recipe',
            'description': 'A test recipe for brewing',
            'batch_size_gallons': 5.0,
            'mash_efficiency_percent': 75.0,
            'target_original_gravity': 1.050,
        }
    
    def get_update_data(self) -> dict:
        """Get data for updating recipe instances."""
        return {
            'name': 'Updated Recipe',
            'description': 'An updated test recipe',
            'batch_size_gallons': 10.0,
        }
    
    @pytest.mark.django_db
    def test_get_by_user_with_recipes(self):
        """Test getting recipes by user when user has recipes."""
        # Arrange
        user = UserFactory.create_in_db(email="<EMAIL>")
        recipe1 = RecipeFactory.create_in_db(name="User Recipe 1", user=user)
        recipe2 = RecipeFactory.create_in_db(name="User Recipe 2", user=user)
        
        # Create recipe for different user
        other_user = UserFactory.create_in_db(email="<EMAIL>")
        RecipeFactory.create_in_db(name="Other Recipe", user=other_user)
        
        # Act
        result = self.repository.get_by_user(user.id)
        
        # Assert
        assert len(result) == 2
        result_ids = [r.id for r in result]
        assert recipe1.id in result_ids
        assert recipe2.id in result_ids
        
        # Verify names
        result_names = [r.name for r in result]
        assert "User Recipe 1" in result_names
        assert "User Recipe 2" in result_names
        assert "Other Recipe" not in result_names
    
    @pytest.mark.django_db
    def test_get_by_user_no_recipes(self):
        """Test getting recipes by user when user has no recipes."""
        # Arrange
        user = UserFactory.create_in_db(email="<EMAIL>")
        
        # Create recipe for different user
        other_user = UserFactory.create_in_db(email="<EMAIL>")
        RecipeFactory.create_in_db(name="Other Recipe", user=other_user)
        
        # Act
        result = self.repository.get_by_user(user.id)
        
        # Assert
        assert result == []
    
    @pytest.mark.django_db
    def test_get_by_name_found(self):
        """Test getting recipe by name when it exists."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Unique Recipe Name")
        RecipeFactory.create_in_db(name="Different Recipe")
        
        # Act
        result = self.repository.get_by_name("Unique Recipe Name")
        
        # Assert
        assert result is not None
        assert result.id == recipe.id
        assert result.name == "Unique Recipe Name"
    
    @pytest.mark.django_db
    def test_get_by_name_not_found(self):
        """Test getting recipe by name when it doesn't exist."""
        # Arrange
        RecipeFactory.create_in_db(name="Existing Recipe")
        
        # Act
        result = self.repository.get_by_name("Non-existent Recipe")
        
        # Assert
        assert result is None
    
    @pytest.mark.django_db
    def test_search_by_name_partial_match(self):
        """Test searching recipes by partial name match."""
        # Arrange
        recipe1 = RecipeFactory.create_in_db(name="American IPA")
        recipe2 = RecipeFactory.create_in_db(name="English IPA")
        recipe3 = RecipeFactory.create_in_db(name="Imperial Stout")
        
        # Act
        result = self.repository.search_by_name("IPA")
        
        # Assert
        assert len(result) == 2
        result_ids = [r.id for r in result]
        assert recipe1.id in result_ids
        assert recipe2.id in result_ids
        assert recipe3.id not in result_ids
    
    @pytest.mark.django_db
    def test_search_by_name_case_insensitive(self):
        """Test searching recipes by name is case insensitive."""
        # Arrange
        recipe1 = RecipeFactory.create_in_db(name="American IPA")
        recipe2 = RecipeFactory.create_in_db(name="english ipa")
        RecipeFactory.create_in_db(name="Imperial Stout")
        
        # Act
        result = self.repository.search_by_name("ipa")
        
        # Assert
        assert len(result) == 2
        result_ids = [r.id for r in result]
        assert recipe1.id in result_ids
        assert recipe2.id in result_ids
    
    @pytest.mark.django_db
    def test_search_by_name_no_matches(self):
        """Test searching recipes by name with no matches."""
        # Arrange
        RecipeFactory.create_in_db(name="American IPA")
        RecipeFactory.create_in_db(name="Imperial Stout")
        
        # Act
        result = self.repository.search_by_name("Lager")
        
        # Assert
        assert result == []
    
    @pytest.mark.django_db
    def test_get_with_fermentables_found(self):
        """Test getting recipe with fermentables prefetched."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Recipe with Fermentables")
        fermentable_inclusion = FermentableInclusionFactory.create_in_db(recipe=recipe)
        
        # Act
        result = self.repository.get_with_fermentables(recipe.id)
        
        # Assert
        assert result is not None
        assert result.id == recipe.id
        assert result.name == "Recipe with Fermentables"
        
        # Verify fermentables are prefetched (this would normally cause a DB query without prefetch)
        fermentable_inclusions = list(result.fermentableinclusion_set.all())
        assert len(fermentable_inclusions) == 1
        assert fermentable_inclusions[0].id == fermentable_inclusion.id
    
    @pytest.mark.django_db
    def test_get_with_fermentables_not_found(self):
        """Test getting recipe with fermentables when recipe doesn't exist."""
        # Act
        result = self.repository.get_with_fermentables("nonexistent_id")
        
        # Assert
        assert result is None
    
    @pytest.mark.django_db
    def test_get_with_all_inclusions_found(self):
        """Test getting recipe with all inclusions prefetched."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Complete Recipe")
        
        # Create various inclusions
        fermentable_inclusion = FermentableInclusionFactory.create_in_db(recipe=recipe)
        hop_inclusion = BoilHopInclusionFactory.create_in_db(recipe=recipe)
        yeast_inclusion = YeastInclusionFactory.create_in_db(recipe=recipe)
        mash_step = MashStepFactory.create_in_db(recipe=recipe)
        fermentation_phase = FermentationPhaseFactory.create_in_db(recipe=recipe)
        
        # Act
        result = self.repository.get_with_all_inclusions(recipe.id)
        
        # Assert
        assert result is not None
        assert result.id == recipe.id
        assert result.name == "Complete Recipe"
        
        # Verify all inclusions are prefetched
        fermentable_inclusions = list(result.fermentableinclusion_set.all())
        assert len(fermentable_inclusions) == 1
        assert fermentable_inclusions[0].id == fermentable_inclusion.id
        
        hop_inclusions = list(result.boilhopinclusion_set.all())
        assert len(hop_inclusions) == 1
        assert hop_inclusions[0].id == hop_inclusion.id
        
        yeast_inclusions = list(result.yeastinclusion_set.all())
        assert len(yeast_inclusions) == 1
        assert yeast_inclusions[0].id == yeast_inclusion.id
        
        mash_steps = list(result.mashstep_set.all())
        assert len(mash_steps) == 1
        assert mash_steps[0].id == mash_step.id
        
        fermentation_phases = list(result.fermentationphase_set.all())
        assert len(fermentation_phases) == 1
        assert fermentation_phases[0].id == fermentation_phase.id
    
    @pytest.mark.django_db
    def test_get_with_all_inclusions_not_found(self):
        """Test getting recipe with all inclusions when recipe doesn't exist."""
        # Act
        result = self.repository.get_with_all_inclusions("nonexistent_id")
        
        # Assert
        assert result is None
    
    @pytest.mark.django_db
    def test_get_with_all_inclusions_empty_recipe(self):
        """Test getting recipe with all inclusions when recipe has no inclusions."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Empty Recipe")
        
        # Act
        result = self.repository.get_with_all_inclusions(recipe.id)
        
        # Assert
        assert result is not None
        assert result.id == recipe.id
        assert result.name == "Empty Recipe"
        
        # Verify all inclusion sets are empty
        assert list(result.fermentableinclusion_set.all()) == []
        assert list(result.boilhopinclusion_set.all()) == []
        assert list(result.yeastinclusion_set.all()) == []
        assert list(result.mashstep_set.all()) == []
        assert list(result.fermentationphase_set.all()) == []
    
    @pytest.mark.django_db
    def test_repository_inherits_base_functionality(self):
        """Test that RecipeRepository inherits all base repository functionality."""
        # Test that all base methods are available
        assert hasattr(self.repository, 'get_by_id')
        assert hasattr(self.repository, 'get_by_id_or_raise')
        assert hasattr(self.repository, 'get_all')
        assert hasattr(self.repository, 'filter')
        assert hasattr(self.repository, 'create')
        assert hasattr(self.repository, 'update')
        assert hasattr(self.repository, 'delete')
        assert hasattr(self.repository, 'exists')
        assert hasattr(self.repository, 'count')
        
        # Test that recipe-specific methods are available
        assert hasattr(self.repository, 'get_by_user')
        assert hasattr(self.repository, 'get_by_name')
        assert hasattr(self.repository, 'search_by_name')
        assert hasattr(self.repository, 'get_with_fermentables')
        assert hasattr(self.repository, 'get_with_all_inclusions')
    
    @pytest.mark.django_db
    def test_model_class_is_recipe(self):
        """Test that repository model class is Recipe."""
        assert self.repository.model_class == Recipe
