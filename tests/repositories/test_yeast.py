"""
Tests for YeastRepository and YeastInclusionRepository.

This module contains comprehensive tests for the YeastRepository and YeastInclusionRepository classes,
testing all yeast-specific operations and queries.
"""

import pytest

from core.repositories.yeast import YeastRepository, YeastInclusionRepository
from core.models import Yeast, YeastInclusion, YeastType, YeastForm
from tests.factories import YeastFactory, YeastInclusionFactory, RecipeFactory
from .base import BaseRepositoryTest


class TestYeastRepository(BaseRepositoryTest):
    """Test YeastRepository functionality."""

    repository_class = YeastRepository
    model_class = Yeast
    factory_class = YeastFactory

    def get_create_data(self) -> dict:
        """Get data for creating yeast instances."""
        return {
            'name': 'Test Yeast',
            'laboratory': 'Test Lab',
            'product_id': 'TEST-001',
            'yeast_type': YeastType.ALE,
            'yeast_form': YeastForm.DRY,
            'min_temperature_fahrenheit': 60.0,
            'max_temperature_fahrenheit': 75.0,
            'attenuation_percent': 75.0,
            'flocculation': 'MEDIUM',
            'description': 'Test yeast description',
        }

    def get_update_data(self) -> dict:
        """Get data for updating yeast instances."""
        return {
            'name': 'Updated Yeast',
            'attenuation_percent': 80.0,
            'description': 'Updated yeast description',
        }

    @pytest.mark.django_db
    def test_get_by_type_ale(self):
        """Test getting yeasts by ale type."""
        # Arrange
        ale_yeast1 = YeastFactory.create_in_db(name="US-05", yeast_type=YeastType.ALE)
        ale_yeast2 = YeastFactory.create_in_db(name="S-04", yeast_type=YeastType.ALE)
        lager_yeast = YeastFactory.create_in_db(name="S-23", yeast_type=YeastType.LAGER)

        # Act
        result = self.repository.get_by_type(YeastType.ALE)

        # Assert
        assert len(result) == 2
        result_ids = [y.id for y in result]
        assert ale_yeast1.id in result_ids
        assert ale_yeast2.id in result_ids
        assert lager_yeast.id not in result_ids

    @pytest.mark.django_db
    def test_get_by_type_lager(self):
        """Test getting yeasts by lager type."""
        # Arrange
        lager_yeast = YeastFactory.create_in_db(name="S-23", yeast_type=YeastType.LAGER)
        ale_yeast = YeastFactory.create_in_db(name="US-05", yeast_type=YeastType.ALE)

        # Act
        result = self.repository.get_by_type(YeastType.LAGER)

        # Assert
        assert len(result) == 1
        assert result[0].id == lager_yeast.id
        assert result[0].name == "S-23"

    @pytest.mark.django_db
    def test_get_ale_yeasts(self):
        """Test getting all ale yeasts."""
        # Arrange
        ale_yeast1 = YeastFactory.create_in_db(name="US-05", yeast_type=YeastType.ALE)
        ale_yeast2 = YeastFactory.create_in_db(name="S-04", yeast_type=YeastType.ALE)
        lager_yeast = YeastFactory.create_in_db(name="S-23", yeast_type=YeastType.LAGER)

        # Act
        result = self.repository.get_ale_yeasts()

        # Assert
        assert len(result) == 2
        result_ids = [y.id for y in result]
        assert ale_yeast1.id in result_ids
        assert ale_yeast2.id in result_ids
        assert lager_yeast.id not in result_ids

    @pytest.mark.django_db
    def test_get_lager_yeasts(self):
        """Test getting all lager yeasts."""
        # Arrange
        lager_yeast1 = YeastFactory.create_in_db(name="S-23", yeast_type=YeastType.LAGER)
        lager_yeast2 = YeastFactory.create_in_db(name="W-34/70", yeast_type=YeastType.LAGER)
        ale_yeast = YeastFactory.create_in_db(name="US-05", yeast_type=YeastType.ALE)

        # Act
        result = self.repository.get_lager_yeasts()

        # Assert
        assert len(result) == 2
        result_ids = [y.id for y in result]
        assert lager_yeast1.id in result_ids
        assert lager_yeast2.id in result_ids
        assert ale_yeast.id not in result_ids

    @pytest.mark.django_db
    def test_get_by_form_dry(self):
        """Test getting yeasts by dry form."""
        # Arrange
        dry_yeast1 = YeastFactory.create_in_db(name="US-05", yeast_form=YeastForm.DRY)
        dry_yeast2 = YeastFactory.create_in_db(name="S-04", yeast_form=YeastForm.DRY)
        liquid_yeast = YeastFactory.create_in_db(name="WLP001", yeast_form=YeastForm.LIQUID)

        # Act
        result = self.repository.get_by_form(YeastForm.DRY)

        # Assert
        assert len(result) == 2
        result_ids = [y.id for y in result]
        assert dry_yeast1.id in result_ids
        assert dry_yeast2.id in result_ids
        assert liquid_yeast.id not in result_ids

    @pytest.mark.django_db
    def test_get_by_form_liquid(self):
        """Test getting yeasts by liquid form."""
        # Arrange
        liquid_yeast = YeastFactory.create_in_db(name="WLP001", yeast_form=YeastForm.LIQUID)
        dry_yeast = YeastFactory.create_in_db(name="US-05", yeast_form=YeastForm.DRY)

        # Act
        result = self.repository.get_by_form(YeastForm.LIQUID)

        # Assert
        assert len(result) == 1
        assert result[0].id == liquid_yeast.id
        assert result[0].name == "WLP001"

    @pytest.mark.django_db
    def test_get_by_laboratory(self):
        """Test getting yeasts from a specific laboratory."""
        # Arrange
        wyeast_yeast1 = YeastFactory.create_in_db(name="1056", laboratory="Wyeast")
        wyeast_yeast2 = YeastFactory.create_in_db(name="1968", laboratory="Wyeast")
        whitelabs_yeast = YeastFactory.create_in_db(name="WLP001", laboratory="White Labs")

        # Act
        result = self.repository.get_by_laboratory("Wyeast")

        # Assert
        assert len(result) == 2
        result_ids = [y.id for y in result]
        assert wyeast_yeast1.id in result_ids
        assert wyeast_yeast2.id in result_ids
        assert whitelabs_yeast.id not in result_ids

    @pytest.mark.django_db
    def test_get_by_laboratory_case_insensitive(self):
        """Test getting yeasts by laboratory is case insensitive."""
        # Arrange
        wyeast_yeast = YeastFactory.create_in_db(name="1056", laboratory="Wyeast")
        whitelabs_yeast = YeastFactory.create_in_db(name="WLP001", laboratory="White Labs")

        # Act
        result = self.repository.get_by_laboratory("wyeast")

        # Assert
        assert len(result) == 1
        assert result[0].id == wyeast_yeast.id

    @pytest.mark.django_db
    def test_search_by_name_partial_match(self):
        """Test searching yeasts by partial name match."""
        # Arrange
        yeast1 = YeastFactory.create_in_db(name="American Ale")
        yeast2 = YeastFactory.create_in_db(name="English Ale")
        yeast3 = YeastFactory.create_in_db(name="German Lager")

        # Act
        result = self.repository.search_by_name("Ale")

        # Assert
        assert len(result) == 2
        result_ids = [y.id for y in result]
        assert yeast1.id in result_ids
        assert yeast2.id in result_ids
        assert yeast3.id not in result_ids

    @pytest.mark.django_db
    def test_search_by_name_case_insensitive(self):
        """Test searching yeasts by name is case insensitive."""
        # Arrange
        yeast1 = YeastFactory.create_in_db(name="American Ale")
        yeast2 = YeastFactory.create_in_db(name="ENGLISH ALE")
        yeast3 = YeastFactory.create_in_db(name="German Lager")

        # Act
        result = self.repository.search_by_name("ale")

        # Assert
        assert len(result) == 2
        result_ids = [y.id for y in result]
        assert yeast1.id in result_ids
        assert yeast2.id in result_ids
        assert yeast3.id not in result_ids

    @pytest.mark.django_db
    def test_get_by_temperature_range(self):
        """Test getting yeasts suitable for a temperature range."""
        # Arrange
        cold_yeast = YeastFactory.create_in_db(
            name="Cold Yeast",
            min_temperature_fahrenheit=45.0,
            max_temperature_fahrenheit=55.0
        )
        warm_yeast = YeastFactory.create_in_db(
            name="Warm Yeast",
            min_temperature_fahrenheit=65.0,
            max_temperature_fahrenheit=75.0
        )
        versatile_yeast = YeastFactory.create_in_db(
            name="Versatile Yeast",
            min_temperature_fahrenheit=55.0,
            max_temperature_fahrenheit=70.0
        )

        # Act - looking for yeasts that can work at 60-65°F
        result = self.repository.get_by_temperature_range(60.0, 65.0)

        # Assert
        assert len(result) == 2
        result_ids = [y.id for y in result]
        assert warm_yeast.id in result_ids
        assert versatile_yeast.id in result_ids
        assert cold_yeast.id not in result_ids

    @pytest.mark.django_db
    def test_get_by_attenuation_range(self):
        """Test getting yeasts by attenuation range."""
        # Arrange
        low_attenuation_yeast = YeastFactory.create_in_db(
            name="Low Attenuation",
            attenuation_percent=65.0
        )
        medium_attenuation_yeast = YeastFactory.create_in_db(
            name="Medium Attenuation",
            attenuation_percent=75.0
        )
        high_attenuation_yeast = YeastFactory.create_in_db(
            name="High Attenuation",
            attenuation_percent=85.0
        )

        # Act
        result = self.repository.get_by_attenuation_range(70.0, 80.0)

        # Assert
        assert len(result) == 1
        assert result[0].id == medium_attenuation_yeast.id
        assert result[0].name == "Medium Attenuation"

    @pytest.mark.django_db
    def test_get_high_attenuation_yeasts_default(self):
        """Test getting high attenuation yeasts with default threshold."""
        # Arrange
        low_attenuation_yeast = YeastFactory.create_in_db(
            name="Low Attenuation",
            attenuation_percent=70.0
        )
        high_attenuation_yeast1 = YeastFactory.create_in_db(
            name="High Attenuation 1",
            attenuation_percent=80.0
        )
        high_attenuation_yeast2 = YeastFactory.create_in_db(
            name="High Attenuation 2",
            attenuation_percent=85.0
        )

        # Act
        result = self.repository.get_high_attenuation_yeasts()

        # Assert
        assert len(result) == 2
        result_ids = [y.id for y in result]
        assert high_attenuation_yeast1.id in result_ids
        assert high_attenuation_yeast2.id in result_ids
        assert low_attenuation_yeast.id not in result_ids

    @pytest.mark.django_db
    def test_get_high_attenuation_yeasts_custom_threshold(self):
        """Test getting high attenuation yeasts with custom threshold."""
        # Arrange
        medium_attenuation_yeast = YeastFactory.create_in_db(
            name="Medium Attenuation",
            attenuation_percent=80.0
        )
        high_attenuation_yeast = YeastFactory.create_in_db(
            name="High Attenuation",
            attenuation_percent=85.0
        )

        # Act
        result = self.repository.get_high_attenuation_yeasts(min_attenuation=82.0)

        # Assert
        assert len(result) == 1
        assert result[0].id == high_attenuation_yeast.id
        assert result[0].name == "High Attenuation"

    @pytest.mark.django_db
    def test_repository_inherits_base_functionality(self):
        """Test that YeastRepository inherits all base repository functionality."""
        # Test that all base methods are available
        assert hasattr(self.repository, 'get_by_id')
        assert hasattr(self.repository, 'get_by_id_or_raise')
        assert hasattr(self.repository, 'get_all')
        assert hasattr(self.repository, 'filter')
        assert hasattr(self.repository, 'create')
        assert hasattr(self.repository, 'update')
        assert hasattr(self.repository, 'delete')
        assert hasattr(self.repository, 'exists')
        assert hasattr(self.repository, 'count')

        # Test that yeast-specific methods are available
        assert hasattr(self.repository, 'get_by_type')
        assert hasattr(self.repository, 'get_ale_yeasts')
        assert hasattr(self.repository, 'get_lager_yeasts')
        assert hasattr(self.repository, 'get_by_form')
        assert hasattr(self.repository, 'get_by_laboratory')
        assert hasattr(self.repository, 'search_by_name')
        assert hasattr(self.repository, 'get_by_temperature_range')
        assert hasattr(self.repository, 'get_by_attenuation_range')
        assert hasattr(self.repository, 'get_high_attenuation_yeasts')

    @pytest.mark.django_db
    def test_model_class_is_yeast(self):
        """Test that repository model class is Yeast."""
        assert self.repository.model_class == Yeast


class TestYeastInclusionRepository(BaseRepositoryTest):
    """Test YeastInclusionRepository functionality."""

    repository_class = YeastInclusionRepository
    model_class = YeastInclusion
    factory_class = YeastInclusionFactory

    def get_create_data(self) -> dict:
        """Get data for creating yeast inclusion instances."""
        recipe = RecipeFactory.create_in_db()
        yeast = YeastFactory.create_in_db()
        return {
            'recipe': recipe,
            'yeast': yeast,
            'quantity': 1.0,
            'quantity_unit': 'PACKET',
            'notes': 'Test yeast inclusion notes',
        }

    def get_update_data(self) -> dict:
        """Get data for updating yeast inclusion instances."""
        return {
            'quantity': 2.0,
            'notes': 'Updated yeast inclusion notes',
        }

    @pytest.mark.django_db
    def test_get_by_recipe(self):
        """Test getting yeast inclusions by recipe."""
        # Arrange
        recipe1 = RecipeFactory.create_in_db(name="Recipe 1")
        recipe2 = RecipeFactory.create_in_db(name="Recipe 2")

        inclusion1 = YeastInclusionFactory.create_in_db(recipe=recipe1)
        inclusion2 = YeastInclusionFactory.create_in_db(recipe=recipe1)
        inclusion3 = YeastInclusionFactory.create_in_db(recipe=recipe2)

        # Act
        result = self.repository.get_by_recipe(recipe1.id)

        # Assert
        assert len(result) == 2
        result_ids = [i.id for i in result]
        assert inclusion1.id in result_ids
        assert inclusion2.id in result_ids
        assert inclusion3.id not in result_ids

    @pytest.mark.django_db
    def test_get_by_recipe_no_inclusions(self):
        """Test getting yeast inclusions by recipe when none exist."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Empty Recipe")

        # Act
        result = self.repository.get_by_recipe(recipe.id)

        # Assert
        assert result == []

    @pytest.mark.django_db
    def test_get_by_recipe_with_yeast(self):
        """Test getting yeast inclusions with yeast data prefetched."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Recipe with Yeast")
        yeast = YeastFactory.create_in_db(name="Test Yeast")
        inclusion = YeastInclusionFactory.create_in_db(recipe=recipe, yeast=yeast)

        # Act
        result = self.repository.get_by_recipe_with_yeast(recipe.id)

        # Assert
        assert len(result) == 1
        assert result[0].id == inclusion.id
        assert result[0].yeast.name == "Test Yeast"
        # Verify yeast is prefetched (this would normally cause a DB query without prefetch)
        assert result[0].yeast.id == yeast.id

    @pytest.mark.django_db
    def test_get_by_recipe_with_yeast_no_inclusions(self):
        """Test getting yeast inclusions with yeast data when none exist."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Empty Recipe")

        # Act
        result = self.repository.get_by_recipe_with_yeast(recipe.id)

        # Assert
        assert result == []

    @pytest.mark.django_db
    def test_get_by_recipe_and_yeast_found(self):
        """Test getting a specific yeast inclusion by recipe and yeast."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        yeast1 = YeastFactory.create_in_db(name="Yeast 1")
        yeast2 = YeastFactory.create_in_db(name="Yeast 2")

        inclusion1 = YeastInclusionFactory.create_in_db(recipe=recipe, yeast=yeast1)
        inclusion2 = YeastInclusionFactory.create_in_db(recipe=recipe, yeast=yeast2)

        # Act
        result = self.repository.get_by_recipe_and_yeast(recipe.id, yeast1.id)

        # Assert
        assert result is not None
        assert result.id == inclusion1.id
        assert result.recipe.id == recipe.id
        assert result.yeast.id == yeast1.id

    @pytest.mark.django_db
    def test_get_by_recipe_and_yeast_not_found(self):
        """Test getting a specific yeast inclusion when it doesn't exist."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        yeast = YeastFactory.create_in_db(name="Test Yeast")

        # Act
        result = self.repository.get_by_recipe_and_yeast(recipe.id, yeast.id)

        # Assert
        assert result is None

    @pytest.mark.django_db
    def test_exists_for_recipe_and_yeast_true(self):
        """Test checking if yeast inclusion exists when it does."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        yeast = YeastFactory.create_in_db(name="Test Yeast")
        YeastInclusionFactory.create_in_db(recipe=recipe, yeast=yeast)

        # Act
        result = self.repository.exists_for_recipe_and_yeast(recipe.id, yeast.id)

        # Assert
        assert result is True

    @pytest.mark.django_db
    def test_exists_for_recipe_and_yeast_false(self):
        """Test checking if yeast inclusion exists when it doesn't."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        yeast = YeastFactory.create_in_db(name="Test Yeast")

        # Act
        result = self.repository.exists_for_recipe_and_yeast(recipe.id, yeast.id)

        # Assert
        assert result is False

    @pytest.mark.django_db
    def test_repository_inherits_base_functionality(self):
        """Test that YeastInclusionRepository inherits all base repository functionality."""
        # Test that all base methods are available
        assert hasattr(self.repository, 'get_by_id')
        assert hasattr(self.repository, 'get_by_id_or_raise')
        assert hasattr(self.repository, 'get_all')
        assert hasattr(self.repository, 'filter')
        assert hasattr(self.repository, 'create')
        assert hasattr(self.repository, 'update')
        assert hasattr(self.repository, 'delete')
        assert hasattr(self.repository, 'exists')
        assert hasattr(self.repository, 'count')

        # Test that yeast inclusion-specific methods are available
        assert hasattr(self.repository, 'get_by_recipe')
        assert hasattr(self.repository, 'get_by_recipe_with_yeast')
        assert hasattr(self.repository, 'get_by_recipe_and_yeast')
        assert hasattr(self.repository, 'exists_for_recipe_and_yeast')

    @pytest.mark.django_db
    def test_model_class_is_yeast_inclusion(self):
        """Test that repository model class is YeastInclusion."""
        assert self.repository.model_class == YeastInclusion
