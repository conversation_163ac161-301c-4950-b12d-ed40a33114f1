"""
Tests for BeerStyle serializer functionality.

This module tests the BeerStyleSerializer and its integration
with other serializers.
"""

import pytest
from rest_framework.test import APITestCase

from core.models import BeerStyle, Recipe
from core.serializers.beer_style import BeerStyleSerializer
from core.serializers.recipe import RecipeSerializer


class TestBeerStyleSerializer(APITestCase):
    """Test the BeerStyleSerializer class."""

    def setUp(self):
        """Set up test case."""
        self.beer_style = BeerStyle.objects.create(
            name="American IPA",
            description="A hoppy American beer style with citrus and pine notes",
            srm_min=6.0,
            srm_max=14.0,
            ibu_min=40.0,
            ibu_max=70.0,
            og_min=1.056,
            og_max=1.070,
            fg_min=1.008,
            fg_max=1.014,
            abv_min=5.5,
            abv_max=7.5,
        )

    def test_beer_style_serialization(self):
        """Test basic BeerStyle serialization."""
        serializer = BeerStyleSerializer(self.beer_style)
        data = serializer.data

        assert data['name'] == "American IPA"
        assert data['description'] == "A hoppy American beer style with citrus and pine notes"
        assert data['srm_min'] == 6.0
        assert data['srm_max'] == 14.0
        assert data['ibu_min'] == 40.0
        assert data['ibu_max'] == 70.0
        assert data['og_min'] == 1.056
        assert data['og_max'] == 1.070
        assert data['fg_min'] == 1.008
        assert data['fg_max'] == 1.014
        assert data['abv_min'] == 5.5
        assert data['abv_max'] == 7.5

    def test_beer_style_display_properties(self):
        """Test that display properties are included in serialization."""
        serializer = BeerStyleSerializer(self.beer_style)
        data = serializer.data

        assert data['srm_range_display'] == "6.0-14.0 SRM"
        assert data['ibu_range_display'] == "40.0-70.0 IBU"
        assert data['og_range_display'] == "1.056-1.070 OG"
        assert data['fg_range_display'] == "1.008-1.014 FG"
        assert data['abv_range_display'] == "5.5-7.5% ABV"

    def test_beer_style_with_partial_ranges(self):
        """Test serialization of beer style with partial ranges."""
        partial_style = BeerStyle.objects.create(
            name="Custom Style",
            description="Custom style with partial ranges",
            srm_min=5.0,  # Only min value
            ibu_max=30.0,  # Only max value
            # No OG, FG, or ABV ranges
        )

        serializer = BeerStyleSerializer(partial_style)
        data = serializer.data

        assert data['srm_min'] == 5.0
        assert data['srm_max'] is None
        assert data['ibu_min'] is None
        assert data['ibu_max'] == 30.0
        assert data['og_min'] is None
        assert data['og_max'] is None

        # Test display properties
        assert data['srm_range_display'] == "5.0+ SRM"
        assert data['ibu_range_display'] == "≤30.0 IBU"
        assert data['og_range_display'] == "No OG range specified"
        assert data['fg_range_display'] == "No FG range specified"
        assert data['abv_range_display'] == "No ABV range specified"

    def test_beer_style_timestamps_included(self):
        """Test that timestamps are included in serialization."""
        serializer = BeerStyleSerializer(self.beer_style)
        data = serializer.data

        assert 'created_at' in data
        assert 'updated_at' in data
        assert data['created_at'] is not None
        assert data['updated_at'] is not None

    def test_beer_style_id_included(self):
        """Test that ID is included in serialization."""
        serializer = BeerStyleSerializer(self.beer_style)
        data = serializer.data

        assert 'id' in data
        assert data['id'] == self.beer_style.id
        assert data['id'].startswith('bst_')

    def test_beer_style_serializer_fields(self):
        """Test that all expected fields are present in serialization."""
        serializer = BeerStyleSerializer(self.beer_style)
        data = serializer.data

        expected_fields = {
            'id', 'name', 'description',
            'srm_min', 'srm_max', 'ibu_min', 'ibu_max',
            'og_min', 'og_max', 'fg_min', 'fg_max',
            'abv_min', 'abv_max',
            'srm_range_display', 'ibu_range_display',
            'og_range_display', 'fg_range_display', 'abv_range_display',
            'created_at', 'updated_at'
        }

        assert set(data.keys()) == expected_fields

    def test_multiple_beer_styles_serialization(self):
        """Test serialization of multiple beer styles."""
        style2 = BeerStyle.objects.create(
            name="Imperial Stout",
            description="A strong, dark beer",
            srm_min=30.0,
            srm_max=40.0,
            ibu_min=50.0,
            ibu_max=90.0,
            og_min=1.075,
            og_max=1.115,
            fg_min=1.018,
            fg_max=1.030,
            abv_min=8.0,
            abv_max=12.0,
        )

        styles = [self.beer_style, style2]
        serializer = BeerStyleSerializer(styles, many=True)
        data = serializer.data

        assert len(data) == 2
        assert data[0]['name'] == "American IPA"
        assert data[1]['name'] == "Imperial Stout"

    @pytest.mark.django_db
    def test_beer_style_in_recipe_serializer(self):
        """Test that BeerStyle is properly serialized within RecipeSerializer."""
        recipe = Recipe.objects.create(
            name="Test IPA Recipe",
            description="A test IPA recipe",
            beer_style=self.beer_style,
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.060,
        )

        serializer = RecipeSerializer(recipe)
        data = serializer.data

        # Check that beer_style is included
        assert 'beer_style' in data
        assert data['beer_style'] is not None
        
        # Check beer style data
        beer_style_data = data['beer_style']
        assert beer_style_data['name'] == "American IPA"
        assert beer_style_data['description'] == "A hoppy American beer style with citrus and pine notes"
        assert beer_style_data['srm_range_display'] == "6.0-14.0 SRM"
        assert beer_style_data['ibu_range_display'] == "40.0-70.0 IBU"

    @pytest.mark.django_db
    def test_recipe_without_beer_style_serialization(self):
        """Test recipe serialization when no beer style is assigned."""
        recipe = Recipe.objects.create(
            name="Recipe without Style",
            description="A recipe with no beer style",
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050,
        )

        serializer = RecipeSerializer(recipe)
        data = serializer.data

        # beer_style should be null
        assert 'beer_style' in data
        assert data['beer_style'] is None

    def test_beer_style_deserialization(self):
        """Test BeerStyle deserialization (creation from data)."""
        data = {
            'name': 'Test Lager',
            'description': 'A crisp, clean lager',
            'srm_min': 2.0,
            'srm_max': 4.0,
            'ibu_min': 8.0,
            'ibu_max': 25.0,
            'og_min': 1.044,
            'og_max': 1.056,
            'fg_min': 1.006,
            'fg_max': 1.012,
            'abv_min': 4.2,
            'abv_max': 5.8,
        }

        serializer = BeerStyleSerializer(data=data)
        assert serializer.is_valid()

        beer_style = serializer.save()
        assert beer_style.name == 'Test Lager'
        assert beer_style.description == 'A crisp, clean lager'
        assert beer_style.srm_min == 2.0
        assert beer_style.srm_max == 4.0

    def test_beer_style_partial_deserialization(self):
        """Test BeerStyle deserialization with minimal required fields."""
        data = {
            'name': 'Minimal Style',
            # Only name is required, all other fields are optional
        }

        serializer = BeerStyleSerializer(data=data)
        assert serializer.is_valid()

        beer_style = serializer.save()
        assert beer_style.name == 'Minimal Style'
        assert beer_style.description == ''
        assert beer_style.srm_min is None
        assert beer_style.srm_max is None

    def test_beer_style_invalid_data(self):
        """Test BeerStyle deserialization with invalid data."""
        data = {
            # Missing required 'name' field
            'description': 'Style without name',
        }

        serializer = BeerStyleSerializer(data=data)
        assert not serializer.is_valid()
        assert 'name' in serializer.errors
