"""
Tests for chat serializer.

This module tests the ChatMessageSerializer used for validating
chat messages sent to the agent.
"""

import pytest
from rest_framework.exceptions import ValidationError

from core.serializers.chat import ChatMessageSerializer


class TestChatMessageSerializer:
    """Test ChatMessageSerializer functionality."""

    def test_serializer_fields(self):
        """Test that serializer has correct fields."""
        serializer = ChatMessageSerializer()
        expected_fields = {'message'}
        assert set(serializer.fields.keys()) == expected_fields

    def test_message_field_properties(self):
        """Test message field properties."""
        serializer = ChatMessageSerializer()
        message_field = serializer.fields['message']

        assert message_field.max_length == 2000
        assert message_field.help_text == "The message to send to the agent"

    def test_validates_valid_message(self):
        """Test validation of valid message data."""
        valid_data = {
            'message': 'Add 1 oz of Cascade hops at 60 minutes'
        }

        serializer = ChatMessageSerializer(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        validated_data = serializer.validated_data
        assert validated_data['message'] == 'Add 1 oz of Cascade hops at 60 minutes'

    def test_validates_message_with_whitespace(self):
        """Test validation trims whitespace from message."""
        data_with_whitespace = {
            'message': '  Add some hops to the recipe  '
        }

        serializer = ChatMessageSerializer(data=data_with_whitespace)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        validated_data = serializer.validated_data
        assert validated_data['message'] == 'Add some hops to the recipe'

    def test_validates_empty_message_fails(self):
        """Test validation fails for empty message."""
        invalid_data = {
            'message': ''
        }

        serializer = ChatMessageSerializer(data=invalid_data)
        assert not serializer.is_valid()
        assert 'message' in serializer.errors
        # Django's CharField validation catches empty strings before custom validation
        assert 'This field may not be blank.' in str(serializer.errors['message'])

    def test_validates_whitespace_only_message_fails(self):
        """Test validation fails for whitespace-only message."""
        invalid_data = {
            'message': '   \t\n   '
        }

        serializer = ChatMessageSerializer(data=invalid_data)
        assert not serializer.is_valid()
        assert 'message' in serializer.errors
        # Django's CharField treats whitespace-only strings as blank
        assert 'This field may not be blank.' in str(serializer.errors['message'])

    def test_validates_missing_message_field(self):
        """Test validation fails when message field is missing."""
        invalid_data = {}

        serializer = ChatMessageSerializer(data=invalid_data)
        assert not serializer.is_valid()
        assert 'message' in serializer.errors

    def test_validates_message_max_length(self):
        """Test validation of message maximum length."""
        # Test message at max length (should be valid)
        max_length_message = 'a' * 2000
        valid_data = {
            'message': max_length_message
        }

        serializer = ChatMessageSerializer(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test message over max length (should be invalid)
        over_length_message = 'a' * 2001
        invalid_data = {
            'message': over_length_message
        }

        serializer = ChatMessageSerializer(data=invalid_data)
        assert not serializer.is_valid()
        assert 'message' in serializer.errors

    def test_validates_various_message_types(self):
        """Test validation of various types of messages."""
        valid_messages = [
            'Add hops',
            'What is the IBU of this recipe?',
            'Change the batch size to 10 gallons',
            'Remove the Munich malt',
            'Add a dry hop addition of Citra for 3 days',
            'What style is this recipe?',
            'Increase the mash temperature to 154°F',
            'Add a secondary fermentation phase',
            'What is the estimated ABV?',
            'Change the water profile to Burton on Trent',
        ]

        for message in valid_messages:
            data = {'message': message}
            serializer = ChatMessageSerializer(data=data)
            assert serializer.is_valid(), f"Failed for message: '{message}' - {serializer.errors}"

            validated_data = serializer.validated_data
            assert validated_data['message'] == message

    def test_validates_message_with_special_characters(self):
        """Test validation of messages with special characters."""
        special_char_messages = [
            'Add 1.5 oz of hops @ 60 minutes',
            'Set temperature to 152°F',
            'Use 10% Munich malt',
            'Add hops (Cascade & Centennial)',
            'Ferment at 68°F for 7 days',
            'Target OG: 1.050, FG: 1.012',
            'Mash efficiency = 75%',
            'Add 2-row pale malt',
        ]

        for message in special_char_messages:
            data = {'message': message}
            serializer = ChatMessageSerializer(data=data)
            assert serializer.is_valid(), f"Failed for message with special chars: '{message}' - {serializer.errors}"

    def test_validates_message_with_unicode(self):
        """Test validation of messages with unicode characters."""
        unicode_messages = [
            'Set temperature to 65°C',
            'Add München malt',
            'Ferment at 20°C',
            'Use Saflager W-34/70™ yeast',
        ]

        for message in unicode_messages:
            data = {'message': message}
            serializer = ChatMessageSerializer(data=data)
            assert serializer.is_valid(), f"Failed for unicode message: '{message}' - {serializer.errors}"

    def test_validates_multiline_message(self):
        """Test validation of multiline messages."""
        multiline_message = """Add the following ingredients:
        - 10 lbs Pale 2-Row
        - 1 lb Munich Malt
        - 1 oz Cascade hops at 60 minutes
        - 1 oz Cascade hops at 5 minutes"""

        data = {'message': multiline_message}
        serializer = ChatMessageSerializer(data=data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        validated_data = serializer.validated_data
        assert validated_data['message'] == multiline_message

    def test_custom_validate_message_method(self):
        """Test the custom validate_message method directly."""
        serializer = ChatMessageSerializer()

        # Test valid message
        result = serializer.validate_message("Valid message")
        assert result == "Valid message"

        # Test message with whitespace
        result = serializer.validate_message("  Message with whitespace  ")
        assert result == "Message with whitespace"

        # Test empty message
        with pytest.raises(ValidationError) as exc_info:
            serializer.validate_message("")
        assert "Message cannot be empty." in str(exc_info.value)

        # Test whitespace-only message
        with pytest.raises(ValidationError) as exc_info:
            serializer.validate_message("   \t\n   ")
        assert "Message cannot be empty." in str(exc_info.value)

    def test_serializer_is_not_model_serializer(self):
        """Test that ChatMessageSerializer is a regular Serializer, not ModelSerializer."""
        serializer = ChatMessageSerializer()

        # Should not have Meta class with model
        assert not hasattr(serializer, 'Meta') or not hasattr(serializer.Meta, 'model')

        # Should be a regular Serializer
        from rest_framework import serializers
        assert isinstance(serializer, serializers.Serializer)
        assert not isinstance(serializer, serializers.ModelSerializer)

    def test_serializer_data_structure(self):
        """Test the structure of serialized data."""
        valid_data = {
            'message': 'Test message for structure'
        }

        serializer = ChatMessageSerializer(data=valid_data)
        assert serializer.is_valid()

        # Test that validated_data has correct structure
        validated_data = serializer.validated_data
        assert isinstance(validated_data, dict)
        assert len(validated_data) == 1
        assert 'message' in validated_data
        assert isinstance(validated_data['message'], str)

    def test_serializer_error_structure(self):
        """Test the structure of validation errors."""
        invalid_data = {
            'message': ''
        }

        serializer = ChatMessageSerializer(data=invalid_data)
        assert not serializer.is_valid()

        errors = serializer.errors
        assert isinstance(errors, dict)
        assert 'message' in errors
        assert isinstance(errors['message'], list)
        assert len(errors['message']) > 0
        assert isinstance(errors['message'][0], str)
