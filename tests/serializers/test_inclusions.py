"""
Tests for inclusion serializers.

This module tests the inclusion serializers including FermentableInclusionSerializer,
YeastInclusionSerializer, and all hop inclusion serializers.
"""

import pytest
from unittest.mock import Mock, patch

from core.serializers.inclusions import (
    FermentableInclusionSerializer, YeastInclusionSerializer,
    BoilHopInclusionSerializer, FirstWortHopInclusionSerializer,
    FlameoutHopInclusionSerializer, WhirlpoolHopInclusionSerializer,
    DryHopInclusionSerializer
)
from core.models import (
    FermentableInclusion, YeastInclusion, BoilHopInclusion,
    FirstWortHopInclusion, FlameoutHopInclusion, WhirlpoolHopInclusion,
    DryHopInclusion, Recipe, Fermentable, Hop, Yeast
)
from tests.base import BaseSerializerTest
from tests.factories import (
    FermentableInclusionFactory, YeastInclusionFactory,
    BoilHopInclusionFactory, FirstWortHopInclusionFactory,
    FlameoutHopInclusionFactory, WhirlpoolHopInclusionFactory,
    DryHopInclusionFactory, RecipeFactory, FermentableFactory,
    HopFactory, YeastFactory
)


class TestFermentableInclusionSerializer(BaseSerializerTest):
    """Test FermentableInclusionSerializer functionality."""

    serializer_class = FermentableInclusionSerializer
    factory = FermentableInclusionFactory

    def get_valid_data(self, instance):
        """Return valid data for FermentableInclusionSerializer."""
        return {
            'quantity': 10.0,
            'quantity_unit': 'lb',
            'efficiency_percent': 75.0,
        }

    def test_serializer_fields(self):
        """Test that serializer has correct fields."""
        serializer = self.serializer_class()
        expected_fields = {
            'id', 'fermentable', 'quantity', 'quantity_unit',
            'efficiency_percent', 'gravity_points_contribution', 'srm_contribution'
        }
        assert set(serializer.fields.keys()) == expected_fields

    def test_readonly_fields(self):
        """Test that calculated fields are read-only."""
        serializer = self.serializer_class()

        readonly_fields = ['fermentable', 'gravity_points_contribution', 'srm_contribution']
        for field_name in readonly_fields:
            field = serializer.fields[field_name]
            assert field.read_only is True, f"Field {field_name} should be read-only"

    @pytest.mark.django_db
    def test_serializes_fermentable_inclusion_instance(self):
        """Test serialization of a FermentableInclusion instance."""
        recipe = Recipe.objects.create(name="Test Recipe", batch_size_gallons=5.0)
        fermentable = Fermentable.objects.create(
            name="Pale 2-Row",
            extract_potential_ppg=37.0,
            color_lovibond=2.0
        )

        inclusion = FermentableInclusion.objects.create(
            recipe=recipe,
            fermentable=fermentable,
            quantity=10.0,
            quantity_unit="lb",
            efficiency_percent=75.0
        )

        serializer = self.serializer_class(inclusion)
        data = serializer.data

        assert data['quantity'] == 10.0
        assert data['quantity_unit'] == "lb"
        assert data['efficiency_percent'] == 75.0
        assert 'fermentable' in data
        assert data['fermentable']['name'] == "Pale 2-Row"
        assert 'gravity_points_contribution' in data
        assert 'srm_contribution' in data
        assert 'id' in data

    def test_validates_quantity_positive(self):
        """Test validation of positive quantity values."""
        valid_data = {
            'quantity': 5.0,
            'quantity_unit': 'lb',
            'efficiency_percent': 75.0,
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test zero quantity (should be invalid at model level)
        invalid_data = valid_data.copy()
        invalid_data['quantity'] = 0.0

        serializer = self.serializer_class(data=invalid_data)
        # Serializer validation may pass, but model validation should catch it
        assert serializer.is_valid()

    def test_validates_efficiency_percent_range(self):
        """Test validation of efficiency percentage range."""
        valid_data = {
            'quantity': 5.0,
            'quantity_unit': 'lb',
            'efficiency_percent': 75.0,
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test boundary values
        for efficiency in [0.0, 100.0]:
            data = valid_data.copy()
            data['efficiency_percent'] = efficiency
            serializer = self.serializer_class(data=data)
            assert serializer.is_valid(), f"Failed for efficiency={efficiency}: {serializer.errors}"


class TestBoilHopInclusionSerializer(BaseSerializerTest):
    """Test BoilHopInclusionSerializer functionality."""

    serializer_class = BoilHopInclusionSerializer
    factory = BoilHopInclusionFactory

    def get_valid_data(self, instance):
        """Return valid data for BoilHopInclusionSerializer."""
        return {
            'quantity': 1.0,
            'quantity_unit': 'oz',
            'time_minutes': 60,
            'notes': 'Test hop addition',
        }

    def test_serializer_fields(self):
        """Test that serializer has correct fields."""
        serializer = self.serializer_class()
        expected_fields = {
            'id', 'hop', 'quantity', 'quantity_unit', 'time_minutes', 'notes',
            'ibu_contribution', 'is_bittering_addition', 'is_aroma_addition'
        }
        assert set(serializer.fields.keys()) == expected_fields

    def test_readonly_fields(self):
        """Test that calculated fields are read-only."""
        serializer = self.serializer_class()

        readonly_fields = ['hop', 'ibu_contribution', 'is_bittering_addition', 'is_aroma_addition']
        for field_name in readonly_fields:
            field = serializer.fields[field_name]
            assert field.read_only is True, f"Field {field_name} should be read-only"

    @pytest.mark.django_db
    def test_serializes_boil_hop_inclusion_instance(self):
        """Test serialization of a BoilHopInclusion instance."""
        recipe = Recipe.objects.create(name="Test Recipe", batch_size_gallons=5.0)
        hop = Hop.objects.create(
            name="Cascade",
            alpha_acid=5.5,
            aroma=True,
            bittering=False
        )

        inclusion = BoilHopInclusion.objects.create(
            recipe=recipe,
            hop=hop,
            quantity=1.0,
            quantity_unit="oz",
            time_minutes=60,
            notes="Bittering addition"
        )

        serializer = self.serializer_class(inclusion)
        data = serializer.data

        assert data['quantity'] == 1.0
        assert data['quantity_unit'] == "oz"
        assert data['time_minutes'] == 60
        assert data['notes'] == "Bittering addition"
        assert 'hop' in data
        assert data['hop']['name'] == "Cascade"
        assert 'ibu_contribution' in data
        assert 'is_bittering_addition' in data
        assert 'is_aroma_addition' in data
        assert 'id' in data

    def test_validates_time_minutes_positive(self):
        """Test validation of positive time values."""
        valid_data = {
            'quantity': 1.0,
            'quantity_unit': 'oz',
            'time_minutes': 30,
            'notes': '',
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test various time values
        for time_minutes in [5, 15, 30, 60, 90]:
            data = valid_data.copy()
            data['time_minutes'] = time_minutes
            serializer = self.serializer_class(data=data)
            assert serializer.is_valid(), f"Failed for time_minutes={time_minutes}: {serializer.errors}"


class TestFirstWortHopInclusionSerializer(BaseSerializerTest):
    """Test FirstWortHopInclusionSerializer functionality."""

    serializer_class = FirstWortHopInclusionSerializer
    factory = FirstWortHopInclusionFactory

    def get_valid_data(self, instance):
        """Return valid data for FirstWortHopInclusionSerializer."""
        return {
            'quantity': 0.75,
            'quantity_unit': 'oz',
            'notes': 'First wort hop addition',
        }

    def test_serializer_fields(self):
        """Test that serializer has correct fields."""
        serializer = self.serializer_class()
        expected_fields = {
            'id', 'hop', 'quantity', 'quantity_unit', 'notes',
            'ibu_contribution', 'is_bittering_addition'
        }
        assert set(serializer.fields.keys()) == expected_fields

    def test_readonly_fields(self):
        """Test that calculated fields are read-only."""
        serializer = self.serializer_class()

        readonly_fields = ['hop', 'ibu_contribution', 'is_bittering_addition']
        for field_name in readonly_fields:
            field = serializer.fields[field_name]
            assert field.read_only is True, f"Field {field_name} should be read-only"

    @pytest.mark.django_db
    def test_serializes_first_wort_hop_inclusion_instance(self):
        """Test serialization of a FirstWortHopInclusion instance."""
        recipe = Recipe.objects.create(name="Test Recipe", batch_size_gallons=5.0)
        hop = Hop.objects.create(
            name="Magnum",
            alpha_acid=12.0,
            aroma=False,
            bittering=True
        )

        inclusion = FirstWortHopInclusion.objects.create(
            recipe=recipe,
            hop=hop,
            quantity=0.5,
            quantity_unit="oz",
            notes="First wort bittering"
        )

        serializer = self.serializer_class(inclusion)
        data = serializer.data

        assert data['quantity'] == 0.5
        assert data['quantity_unit'] == "oz"
        assert data['notes'] == "First wort bittering"
        assert 'hop' in data
        assert data['hop']['name'] == "Magnum"
        assert 'ibu_contribution' in data
        assert 'is_bittering_addition' in data
        assert 'id' in data


class TestWhirlpoolHopInclusionSerializer(BaseSerializerTest):
    """Test WhirlpoolHopInclusionSerializer functionality."""

    serializer_class = WhirlpoolHopInclusionSerializer
    factory = WhirlpoolHopInclusionFactory

    def get_valid_data(self, instance):
        """Return valid data for WhirlpoolHopInclusionSerializer."""
        return {
            'quantity': 2.0,
            'quantity_unit': 'oz',
            'time_minutes': 20,
            'temperature_f': 180,
            'notes': 'Whirlpool hop addition',
        }

    def test_serializer_fields(self):
        """Test that serializer has correct fields."""
        serializer = self.serializer_class()
        expected_fields = {
            'id', 'hop', 'quantity', 'quantity_unit', 'time_minutes',
            'temperature_f', 'notes', 'ibu_contribution', 'is_aroma_addition'
        }
        assert set(serializer.fields.keys()) == expected_fields

    @pytest.mark.django_db
    def test_serializes_whirlpool_hop_inclusion_instance(self):
        """Test serialization of a WhirlpoolHopInclusion instance."""
        recipe = Recipe.objects.create(name="Test Recipe", batch_size_gallons=5.0)
        hop = Hop.objects.create(
            name="Citra",
            alpha_acid=12.0,
            aroma=True,
            bittering=False
        )

        inclusion = WhirlpoolHopInclusion.objects.create(
            recipe=recipe,
            hop=hop,
            quantity=2.0,
            quantity_unit="oz",
            time_minutes=20,
            temperature_f=180,
            notes="Whirlpool aroma"
        )

        serializer = self.serializer_class(inclusion)
        data = serializer.data

        assert data['quantity'] == 2.0
        assert data['quantity_unit'] == "oz"
        assert data['time_minutes'] == 20
        assert data['temperature_f'] == 180
        assert data['notes'] == "Whirlpool aroma"
        assert 'hop' in data
        assert data['hop']['name'] == "Citra"
        assert 'ibu_contribution' in data
        assert 'is_aroma_addition' in data
        assert 'id' in data

    def test_validates_temperature_range(self):
        """Test validation of temperature values."""
        valid_data = {
            'quantity': 1.0,
            'quantity_unit': 'oz',
            'time_minutes': 20,
            'temperature_f': 180,
            'notes': '',
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test various temperature values
        for temp in [150, 170, 180, 200]:
            data = valid_data.copy()
            data['temperature_f'] = temp
            serializer = self.serializer_class(data=data)
            assert serializer.is_valid(), f"Failed for temperature_f={temp}: {serializer.errors}"


class TestDryHopInclusionSerializer(BaseSerializerTest):
    """Test DryHopInclusionSerializer functionality."""

    serializer_class = DryHopInclusionSerializer
    factory = DryHopInclusionFactory

    def get_valid_data(self, instance):
        """Return valid data for DryHopInclusionSerializer."""
        return {
            'quantity': 1.0,
            'quantity_unit': 'oz',
            'time_days': 4,
            'notes': 'Dry hop addition',
        }

    def test_serializer_fields(self):
        """Test that serializer has correct fields."""
        serializer = self.serializer_class()
        expected_fields = {
            'id', 'hop', 'quantity', 'quantity_unit', 'time_days',
            'notes', 'is_aroma_addition'
        }
        assert set(serializer.fields.keys()) == expected_fields

    @pytest.mark.django_db
    def test_serializes_dry_hop_inclusion_instance(self):
        """Test serialization of a DryHopInclusion instance."""
        recipe = Recipe.objects.create(name="Test Recipe", batch_size_gallons=5.0)
        hop = Hop.objects.create(
            name="Mosaic",
            alpha_acid=11.5,
            aroma=True,
            bittering=False
        )

        inclusion = DryHopInclusion.objects.create(
            recipe=recipe,
            hop=hop,
            quantity=1.0,
            quantity_unit="oz",
            time_days=4,
            notes="Dry hop for aroma"
        )

        serializer = self.serializer_class(inclusion)
        data = serializer.data

        assert data['quantity'] == 1.0
        assert data['quantity_unit'] == "oz"
        assert data['time_days'] == 4
        assert data['notes'] == "Dry hop for aroma"
        assert 'hop' in data
        assert data['hop']['name'] == "Mosaic"
        assert 'is_aroma_addition' in data
        assert 'id' in data

    def test_validates_time_days_positive(self):
        """Test validation of positive time_days values."""
        valid_data = {
            'quantity': 1.0,
            'quantity_unit': 'oz',
            'time_days': 3,
            'notes': '',
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test various day values
        for days in [1, 3, 5, 7, 14]:
            data = valid_data.copy()
            data['time_days'] = days
            serializer = self.serializer_class(data=data)
            assert serializer.is_valid(), f"Failed for time_days={days}: {serializer.errors}"


class TestYeastInclusionSerializer(BaseSerializerTest):
    """Test YeastInclusionSerializer functionality."""

    serializer_class = YeastInclusionSerializer
    factory = YeastInclusionFactory

    def get_valid_data(self, instance):
        """Return valid data for YeastInclusionSerializer."""
        return {
            'quantity': 1.0,
            'quantity_unit': 'PACKET',
            'starter_made': True,
            'starter_size_ml': 500.0,
        }

    def test_serializer_fields(self):
        """Test that serializer has correct fields."""
        serializer = self.serializer_class()
        expected_fields = {
            'id', 'yeast', 'quantity', 'quantity_unit', 'starter_made', 'starter_size_ml'
        }
        assert set(serializer.fields.keys()) == expected_fields

    @pytest.mark.django_db
    def test_serializes_yeast_inclusion_instance(self):
        """Test serialization of a YeastInclusion instance."""
        recipe = Recipe.objects.create(name="Test Recipe", batch_size_gallons=5.0)
        yeast = Yeast.objects.create(
            name="Safale US-05",
            min_temperature_fahrenheit=59.0,
            max_temperature_fahrenheit=75.0,
            attenuation_percent=75.0
        )

        inclusion = YeastInclusion.objects.create(
            recipe=recipe,
            yeast=yeast,
            quantity=1.0,
            quantity_unit="PACKET",
            starter_made=True,
            starter_size_ml=500.0
        )

        serializer = self.serializer_class(inclusion)
        data = serializer.data

        assert data['quantity'] == 1.0
        assert data['quantity_unit'] == "PACKET"
        assert data['starter_made'] is True
        assert data['starter_size_ml'] == 500.0
        assert 'yeast' in data
        assert data['yeast']['name'] == "Safale US-05"
        assert 'id' in data
