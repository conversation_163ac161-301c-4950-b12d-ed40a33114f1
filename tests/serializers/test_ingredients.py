"""
Tests for ingredient serializers.

This module tests the ingredient serializers including FermentableSerializer,
HopSerializer, YeastSerializer, and WaterProfileSerializer.
"""

import pytest
from rest_framework.exceptions import ValidationError

from core.serializers.ingredients import (
    FermentableSerializer, HopSerializer, YeastSerializer, WaterProfileSerializer
)
from core.models import Fermentable, Hop, Yeast, WaterProfile, FermentableType, YeastType, YeastForm
from tests.base import BaseSerializerTest
from tests.factories import FermentableFactory, HopFactory, YeastFactory, WaterProfileFactory


class TestFermentableSerializer(BaseSerializerTest):
    """Test FermentableSerializer functionality."""

    serializer_class = FermentableSerializer
    factory = FermentableFactory

    def get_valid_data(self, instance):
        """Return valid data for FermentableSerializer."""
        return {
            'name': 'Test Fermentable',
            'extract_potential_ppg': 37.0,
            'color_lovibond': 2.0,
            'requires_mashing': True,
        }

    def test_serializer_fields(self):
        """Test that serializer has correct fields."""
        serializer = self.serializer_class()
        expected_fields = {
            'id', 'name', 'extract_potential_ppg', 'color_lovibond', 'requires_mashing'
        }
        assert set(serializer.fields.keys()) == expected_fields

    @pytest.mark.django_db
    def test_serializes_fermentable_instance(self):
        """Test serialization of a Fermentable instance."""
        fermentable = Fermentable.objects.create(
            name="Pale 2-Row",
            fermentable_type=FermentableType.BASE_MALT,
            extract_potential_ppg=37.0,
            color_lovibond=2.0,
            requires_mashing=True
        )

        serializer = self.serializer_class(fermentable)
        data = serializer.data

        assert data['name'] == "Pale 2-Row"
        assert data['extract_potential_ppg'] == 37.0
        assert data['color_lovibond'] == 2.0
        assert data['requires_mashing'] is True
        assert 'id' in data

    def test_validates_valid_data(self):
        """Test validation of valid fermentable data."""
        valid_data = {
            'name': 'Munich Malt',
            'extract_potential_ppg': 35.0,
            'color_lovibond': 8.0,
            'requires_mashing': True,
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        validated_data = serializer.validated_data
        assert validated_data['name'] == 'Munich Malt'
        assert validated_data['extract_potential_ppg'] == 35.0

    def test_validates_required_fields(self):
        """Test validation fails when required fields are missing."""
        invalid_data = {
            'extract_potential_ppg': 37.0,
            # Missing required 'name' field
        }

        serializer = self.serializer_class(data=invalid_data)
        assert not serializer.is_valid()
        assert 'name' in serializer.errors

    def test_validates_numeric_fields(self):
        """Test validation of numeric field constraints."""
        invalid_data = {
            'name': 'Test Malt',
            'extract_potential_ppg': -5.0,  # Invalid negative value
            'color_lovibond': -1.0,  # Invalid negative value
        }

        serializer = self.serializer_class(data=invalid_data)
        # Note: Django model validation happens at model level, not serializer level
        # So we test that the serializer accepts the data but model validation would catch it
        assert serializer.is_valid()  # Serializer validation passes

        # Model validation would catch negative values, but we can't test save()
        # without a complete model instance, so we just verify the serializer accepts it


class TestHopSerializer(BaseSerializerTest):
    """Test HopSerializer functionality."""

    serializer_class = HopSerializer
    factory = HopFactory

    def get_valid_data(self, instance):
        """Return valid data for HopSerializer."""
        return {
            'name': 'Test Hop',
            'country_of_origin': 'USA',
            'notes': 'Test hop notes',
            'alpha_acid': 5.5,
            'beta_acid': 4.0,
            'aroma': True,
            'bittering': False,
        }

    def test_serializer_fields(self):
        """Test that serializer has correct fields."""
        serializer = self.serializer_class()
        expected_fields = {
            'id', 'name', 'country_of_origin', 'notes', 'alpha_acid',
            'beta_acid', 'aroma', 'bittering'
        }
        assert set(serializer.fields.keys()) == expected_fields

    @pytest.mark.django_db
    def test_serializes_hop_instance(self):
        """Test serialization of a Hop instance."""
        hop = Hop.objects.create(
            name="Cascade",
            country_of_origin="USA",
            notes="Classic American aroma hop",
            alpha_acid=5.5,
            beta_acid=4.5,
            aroma=True,
            bittering=False
        )

        serializer = self.serializer_class(hop)
        data = serializer.data

        assert data['name'] == "Cascade"
        assert data['country_of_origin'] == "USA"
        assert data['notes'] == "Classic American aroma hop"
        assert data['alpha_acid'] == 5.5
        assert data['beta_acid'] == 4.5
        assert data['aroma'] is True
        assert data['bittering'] is False
        assert 'id' in data

    def test_validates_alpha_acid_range(self):
        """Test validation of alpha acid percentage."""
        valid_data = {
            'name': 'Test Hop',
            'alpha_acid': 12.5,
            'beta_acid': 4.0,
            'aroma': True,
            'bittering': True,
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test edge cases
        edge_cases = [0.0, 25.0]  # Typical range boundaries
        for alpha_acid in edge_cases:
            data = valid_data.copy()
            data['alpha_acid'] = alpha_acid
            serializer = self.serializer_class(data=data)
            assert serializer.is_valid(), f"Failed for alpha_acid={alpha_acid}: {serializer.errors}"


class TestYeastSerializer(BaseSerializerTest):
    """Test YeastSerializer functionality."""

    serializer_class = YeastSerializer
    factory = YeastFactory

    def get_valid_data(self, instance):
        """Return valid data for YeastSerializer."""
        return {
            'name': 'Test Yeast',
            'laboratory': 'Test Lab',
            'product_id': 'T-123',
            'yeast_type': YeastType.ALE,
            'yeast_form': YeastForm.DRY,
            'min_temperature_fahrenheit': 59.0,
            'max_temperature_fahrenheit': 75.0,
            'attenuation_percent': 75.0,
        }

    def test_serializer_fields(self):
        """Test that serializer has correct fields."""
        serializer = self.serializer_class()
        expected_fields = {
            'id', 'name', 'laboratory', 'product_id', 'yeast_type', 'yeast_form',
            'min_temperature_fahrenheit', 'max_temperature_fahrenheit',
            'attenuation_percent', 'temperature_range_display'
        }
        assert set(serializer.fields.keys()) == expected_fields

    def test_temperature_range_display_readonly(self):
        """Test that temperature_range_display is read-only."""
        serializer = self.serializer_class()
        field = serializer.fields['temperature_range_display']
        assert field.read_only is True

    @pytest.mark.django_db
    def test_serializes_yeast_instance(self):
        """Test serialization of a Yeast instance."""
        yeast = Yeast.objects.create(
            name="Safale US-05",
            laboratory="Fermentis",
            product_id="US-05",
            yeast_type=YeastType.ALE,
            yeast_form=YeastForm.DRY,
            min_temperature_fahrenheit=59.0,
            max_temperature_fahrenheit=75.0,
            attenuation_percent=75.0
        )

        serializer = self.serializer_class(yeast)
        data = serializer.data

        assert data['name'] == "Safale US-05"
        assert data['laboratory'] == "Fermentis"
        assert data['product_id'] == "US-05"
        assert data['yeast_type'] == YeastType.ALE
        assert data['yeast_form'] == YeastForm.DRY
        assert data['min_temperature_fahrenheit'] == 59.0
        assert data['max_temperature_fahrenheit'] == 75.0
        assert data['attenuation_percent'] == 75.0
        assert 'temperature_range_display' in data
        assert 'id' in data

    def test_validates_temperature_range(self):
        """Test validation of temperature range."""
        valid_data = {
            'name': 'Test Yeast',
            'yeast_type': YeastType.ALE,
            'yeast_form': YeastForm.DRY,
            'min_temperature_fahrenheit': 60.0,
            'max_temperature_fahrenheit': 75.0,
            'attenuation_percent': 75.0,
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test that min > max would be caught at model level
        invalid_data = valid_data.copy()
        invalid_data['min_temperature_fahrenheit'] = 80.0
        invalid_data['max_temperature_fahrenheit'] = 60.0

        serializer = self.serializer_class(data=invalid_data)
        assert serializer.is_valid()  # Serializer validation passes
        # Model validation would catch the logical error


class TestWaterProfileSerializer(BaseSerializerTest):
    """Test WaterProfileSerializer functionality."""

    serializer_class = WaterProfileSerializer
    factory = WaterProfileFactory

    def get_valid_data(self, instance):
        """Return valid data for WaterProfileSerializer."""
        return {
            'name': 'Test Water Profile',
            'description': 'Test water profile description',
            'calcium_ppm': 50.0,
            'magnesium_ppm': 10.0,
            'sodium_ppm': 15.0,
            'sulfate_ppm': 150.0,
            'chloride_ppm': 50.0,
            'bicarbonate_ppm': 100.0,
        }

    def test_serializer_fields(self):
        """Test that serializer has correct fields."""
        serializer = self.serializer_class()
        expected_fields = {
            'id', 'name', 'description', 'calcium_ppm', 'magnesium_ppm',
            'sodium_ppm', 'sulfate_ppm', 'chloride_ppm', 'bicarbonate_ppm'
        }
        assert set(serializer.fields.keys()) == expected_fields

    @pytest.mark.django_db
    def test_serializes_water_profile_instance(self):
        """Test serialization of a WaterProfile instance."""
        water_profile = WaterProfile.objects.create(
            name="Burton on Trent",
            description="Classic English brewing water",
            calcium_ppm=295.0,
            magnesium_ppm=45.0,
            sodium_ppm=55.0,
            sulfate_ppm=725.0,
            chloride_ppm=25.0,
            bicarbonate_ppm=300.0
        )

        serializer = self.serializer_class(water_profile)
        data = serializer.data

        assert data['name'] == "Burton on Trent"
        assert data['description'] == "Classic English brewing water"
        assert data['calcium_ppm'] == 295.0
        assert data['magnesium_ppm'] == 45.0
        assert data['sodium_ppm'] == 55.0
        assert data['sulfate_ppm'] == 725.0
        assert data['chloride_ppm'] == 25.0
        assert data['bicarbonate_ppm'] == 300.0
        assert 'id' in data

    def test_validates_mineral_concentrations(self):
        """Test validation of mineral concentration values."""
        valid_data = {
            'name': 'Test Profile',
            'calcium_ppm': 50.0,
            'magnesium_ppm': 10.0,
            'sodium_ppm': 15.0,
            'sulfate_ppm': 150.0,
            'chloride_ppm': 50.0,
            'bicarbonate_ppm': 100.0,
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test zero values are valid
        zero_data = valid_data.copy()
        for field in ['calcium_ppm', 'magnesium_ppm', 'sodium_ppm', 'sulfate_ppm', 'chloride_ppm', 'bicarbonate_ppm']:
            zero_data[field] = 0.0

        serializer = self.serializer_class(data=zero_data)
        assert serializer.is_valid(), f"Serializer errors for zero values: {serializer.errors}"
