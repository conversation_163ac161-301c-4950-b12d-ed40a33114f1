"""
Tests for process serializers.

This module tests the process serializers including MashStepSerializer
and FermentationPhaseSerializer.
"""

import pytest

from core.serializers.processes import MashStepSerializer, FermentationPhaseSerializer
from core.models import MashStep, FermentationPhase, Recipe
from tests.base import BaseSerializerTest
from tests.factories import MashStepFactory, FermentationPhaseFactory


class TestMashStepSerializer(BaseSerializerTest):
    """Test MashStepSerializer functionality."""

    serializer_class = MashStepSerializer
    factory = MashStepFactory

    def get_valid_data(self, instance):
        """Return valid data for MashStepSerializer."""
        return {
            'name': instance.name,
            'step_type': 'INFUSION',  # Use actual choice value
            'temperature_fahrenheit': 152.0,  # Use real float value
            'duration_minutes': 60,  # Use real int value
            'step_order': 1,  # Use real int value
        }

    def test_serializer_fields(self):
        """Test that serializer has correct fields."""
        serializer = self.serializer_class()
        expected_fields = {
            'id', 'name', 'step_type', 'temperature_fahrenheit',
            'duration_minutes', 'step_order', 'duration_display'
        }
        assert set(serializer.fields.keys()) == expected_fields

    def test_duration_display_readonly(self):
        """Test that duration_display is read-only."""
        serializer = self.serializer_class()
        field = serializer.fields['duration_display']
        assert field.read_only is True

    @pytest.mark.django_db
    def test_serializes_mash_step_instance(self):
        """Test serialization of a MashStep instance."""
        recipe = Recipe.objects.create(name="Test Recipe", batch_size_gallons=5.0)

        mash_step = MashStep.objects.create(
            recipe=recipe,
            name="Single Infusion",
            step_type="INFUSION",
            temperature_fahrenheit=152.0,
            duration_minutes=60,
            step_order=1
        )

        serializer = self.serializer_class(mash_step)
        data = serializer.data

        assert data['name'] == "Single Infusion"
        assert data['step_type'] == "INFUSION"
        assert data['temperature_fahrenheit'] == 152.0
        assert data['duration_minutes'] == 60
        assert data['step_order'] == 1
        assert 'duration_display' in data
        assert 'id' in data

    def test_validates_temperature_range(self):
        """Test validation of temperature values."""
        valid_data = {
            'name': 'Protein Rest',
            'step_type': 'INFUSION',
            'temperature_fahrenheit': 122.0,
            'duration_minutes': 20,
            'step_order': 1,
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test typical mash temperature range
        for temp in [104, 122, 140, 152, 168]:  # Typical mash temperatures
            data = valid_data.copy()
            data['temperature_fahrenheit'] = temp
            serializer = self.serializer_class(data=data)
            assert serializer.is_valid(), f"Failed for temperature={temp}: {serializer.errors}"

    def test_validates_duration_positive(self):
        """Test validation of positive duration values."""
        valid_data = {
            'name': 'Saccharification Rest',
            'step_type': 'INFUSION',
            'temperature_fahrenheit': 152.0,
            'duration_minutes': 60,
            'step_order': 1,
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test various duration values
        for duration in [10, 20, 30, 60, 90]:
            data = valid_data.copy()
            data['duration_minutes'] = duration
            serializer = self.serializer_class(data=data)
            assert serializer.is_valid(), f"Failed for duration={duration}: {serializer.errors}"

    def test_validates_step_order_positive(self):
        """Test validation of positive step_order values."""
        valid_data = {
            'name': 'First Step',
            'step_type': 'INFUSION',
            'temperature_fahrenheit': 152.0,
            'duration_minutes': 60,
            'step_order': 1,
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test various step orders
        for order in [1, 2, 3, 4, 5]:
            data = valid_data.copy()
            data['step_order'] = order
            serializer = self.serializer_class(data=data)
            assert serializer.is_valid(), f"Failed for step_order={order}: {serializer.errors}"

    def test_validates_required_fields(self):
        """Test validation fails when required fields are missing."""
        incomplete_data = {
            'name': 'Incomplete Step',
            # Missing required fields
        }

        serializer = self.serializer_class(data=incomplete_data)
        assert not serializer.is_valid()

        # Check that required fields are in errors (step_type has default, so not required)
        required_fields = ['temperature_fahrenheit', 'duration_minutes', 'step_order']
        for field in required_fields:
            assert field in serializer.errors, f"Missing required field {field} not in errors"


class TestFermentationPhaseSerializer(BaseSerializerTest):
    """Test FermentationPhaseSerializer functionality."""

    serializer_class = FermentationPhaseSerializer
    factory = FermentationPhaseFactory

    def get_valid_data(self, instance):
        """Return valid data for FermentationPhaseSerializer."""
        return {
            'name': instance.name,
            'phase_type': 'PRIMARY',  # Use actual choice value
            'temperature_fahrenheit': 68.0,  # Use real float value
            'duration_days': 7,  # Use real int value
            'phase_order': 1,  # Use real int value
        }

    def test_serializer_fields(self):
        """Test that serializer has correct fields."""
        serializer = self.serializer_class()
        expected_fields = {
            'id', 'name', 'phase_type', 'temperature_fahrenheit',
            'duration_days', 'phase_order', 'duration_display', 'temperature_display'
        }
        assert set(serializer.fields.keys()) == expected_fields

    def test_readonly_fields(self):
        """Test that display fields are read-only."""
        serializer = self.serializer_class()

        readonly_fields = ['duration_display', 'temperature_display']
        for field_name in readonly_fields:
            field = serializer.fields[field_name]
            assert field.read_only is True, f"Field {field_name} should be read-only"

    @pytest.mark.django_db
    def test_serializes_fermentation_phase_instance(self):
        """Test serialization of a FermentationPhase instance."""
        recipe = Recipe.objects.create(name="Test Recipe", batch_size_gallons=5.0)

        fermentation_phase = FermentationPhase.objects.create(
            recipe=recipe,
            name="Primary Fermentation",
            phase_type="PRIMARY",
            temperature_fahrenheit=68.0,
            duration_days=7,
            phase_order=1
        )

        serializer = self.serializer_class(fermentation_phase)
        data = serializer.data

        assert data['name'] == "Primary Fermentation"
        assert data['phase_type'] == "PRIMARY"
        assert data['temperature_fahrenheit'] == 68.0
        assert data['duration_days'] == 7
        assert data['phase_order'] == 1
        assert 'duration_display' in data
        assert 'temperature_display' in data
        assert 'id' in data

    def test_validates_fermentation_temperature_range(self):
        """Test validation of fermentation temperature values."""
        valid_data = {
            'name': 'Primary Fermentation',
            'phase_type': 'PRIMARY',
            'temperature_fahrenheit': 68.0,
            'duration_days': 7,
            'phase_order': 1,
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test typical fermentation temperature range
        for temp in [60, 65, 68, 72, 75]:  # Typical ale fermentation temperatures
            data = valid_data.copy()
            data['temperature_fahrenheit'] = temp
            serializer = self.serializer_class(data=data)
            assert serializer.is_valid(), f"Failed for temperature={temp}: {serializer.errors}"

        # Test lager temperatures
        for temp in [45, 50, 55]:  # Typical lager fermentation temperatures
            data = valid_data.copy()
            data['temperature_fahrenheit'] = temp
            serializer = self.serializer_class(data=data)
            assert serializer.is_valid(), f"Failed for lager temperature={temp}: {serializer.errors}"

    def test_validates_duration_days_positive(self):
        """Test validation of positive duration_days values."""
        valid_data = {
            'name': 'Secondary Fermentation',
            'phase_type': 'SECONDARY',
            'temperature_fahrenheit': 68.0,
            'duration_days': 14,
            'phase_order': 2,
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test various duration values
        for duration in [3, 7, 14, 21, 30]:
            data = valid_data.copy()
            data['duration_days'] = duration
            serializer = self.serializer_class(data=data)
            assert serializer.is_valid(), f"Failed for duration_days={duration}: {serializer.errors}"

    def test_validates_phase_order_positive(self):
        """Test validation of positive phase_order values."""
        valid_data = {
            'name': 'Primary Fermentation',
            'phase_type': 'PRIMARY',
            'temperature_fahrenheit': 68.0,
            'duration_days': 7,
            'phase_order': 1,
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test various phase orders
        for order in [1, 2, 3]:
            data = valid_data.copy()
            data['phase_order'] = order
            serializer = self.serializer_class(data=data)
            assert serializer.is_valid(), f"Failed for phase_order={order}: {serializer.errors}"

    def test_validates_phase_types(self):
        """Test validation of different phase types."""
        base_data = {
            'name': 'Test Phase',
            'temperature_fahrenheit': 68.0,
            'duration_days': 7,
            'phase_order': 1,
        }

        # Test different phase types (assuming these are the valid choices)
        phase_types = ['PRIMARY', 'SECONDARY', 'CONDITIONING', 'LAGERING']

        for phase_type in phase_types:
            data = base_data.copy()
            data['phase_type'] = phase_type
            data['name'] = f"{phase_type.title()} Phase"

            serializer = self.serializer_class(data=data)
            assert serializer.is_valid(), f"Failed for phase_type={phase_type}: {serializer.errors}"

    def test_validates_required_fields(self):
        """Test validation fails when required fields are missing."""
        incomplete_data = {
            'name': 'Incomplete Phase',
            # Missing required fields
        }

        serializer = self.serializer_class(data=incomplete_data)
        assert not serializer.is_valid()

        # Check that required fields are in errors (phase_type has default, so not required)
        required_fields = ['temperature_fahrenheit', 'duration_days', 'phase_order']
        for field in required_fields:
            assert field in serializer.errors, f"Missing required field {field} not in errors"

    def test_serializes_multiple_phases(self):
        """Test serialization of multiple fermentation phases."""
        valid_phases = [
            {
                'name': 'Primary Fermentation',
                'phase_type': 'PRIMARY',
                'temperature_fahrenheit': 68.0,
                'duration_days': 7,
                'phase_order': 1,
            },
            {
                'name': 'Secondary Fermentation',
                'phase_type': 'SECONDARY',
                'temperature_fahrenheit': 65.0,
                'duration_days': 14,
                'phase_order': 2,
            },
            {
                'name': 'Conditioning',
                'phase_type': 'CONDITIONING',
                'temperature_fahrenheit': 35.0,
                'duration_days': 21,
                'phase_order': 3,
            }
        ]

        for phase_data in valid_phases:
            serializer = self.serializer_class(data=phase_data)
            assert serializer.is_valid(), f"Failed for phase {phase_data['name']}: {serializer.errors}"

            validated_data = serializer.validated_data
            assert validated_data['name'] == phase_data['name']
            assert validated_data['phase_type'] == phase_data['phase_type']
            assert validated_data['temperature_fahrenheit'] == phase_data['temperature_fahrenheit']
            assert validated_data['duration_days'] == phase_data['duration_days']
            assert validated_data['phase_order'] == phase_data['phase_order']
