"""
Tests for recipe serializer.

This module tests the RecipeSerializer which combines all other serializers
and includes calculated properties.
"""

import pytest
from unittest.mock import Mock, patch

from core.serializers.recipe import RecipeSerializer
from core.models import (
    Recipe, Fermentable, Hop, Yeast, WaterProfile,
    FermentableInclusion, BoilHopInclusion, YeastInclusion,
    MashStep, FermentationPhase
)
from tests.base import BaseSerializerTest
from tests.factories import RecipeFactory


class TestRecipeSerializer(BaseSerializerTest):
    """Test RecipeSerializer functionality."""

    serializer_class = RecipeSerializer
    factory = RecipeFactory

    def get_valid_data(self, instance):
        """Return valid data for RecipeSerializer."""
        return {
            'name': 'Test Recipe',
            'description': 'A test recipe for brewing',
            'batch_size_gallons': 5.0,
            'mash_efficiency_percent': 75.0,
            'target_original_gravity': 1.050,
        }

    def test_serializer_fields(self):
        """Test that serializer has correct fields."""
        serializer = self.serializer_class()
        expected_fields = {
            'id', 'name', 'description', 'batch_size_gallons',
            'mash_efficiency_percent', 'target_original_gravity',
            'calculated_original_gravity', 'original_gravity', 'calculated_srm',
            'total_ibus', 'estimated_final_gravity', 'estimated_abv',
            'fermentable_inclusions', 'boil_hop_inclusions',
            'first_wort_hop_inclusions', 'flameout_hop_inclusions',
            'whirlpool_hop_inclusions', 'dry_hop_inclusions',
            'water_profile', 'yeast_inclusions', 'mash_steps',
            'fermentation_phases', 'beer_style', 'created_at', 'updated_at'
        }
        assert set(serializer.fields.keys()) == expected_fields

    def test_readonly_fields(self):
        """Test that calculated and timestamp fields are read-only."""
        serializer = self.serializer_class()

        readonly_fields = [
            'id', 'calculated_original_gravity', 'original_gravity', 'calculated_srm',
            'total_ibus', 'estimated_final_gravity', 'estimated_abv',
            'fermentable_inclusions', 'boil_hop_inclusions',
            'first_wort_hop_inclusions', 'flameout_hop_inclusions',
            'whirlpool_hop_inclusions', 'dry_hop_inclusions',
            'water_profile', 'yeast_inclusions', 'mash_steps',
            'fermentation_phases', 'created_at', 'updated_at'
        ]

        for field_name in readonly_fields:
            field = serializer.fields[field_name]
            assert field.read_only is True, f"Field {field_name} should be read-only"

    @pytest.mark.django_db
    def test_serializes_basic_recipe_instance(self):
        """Test serialization of a basic Recipe instance."""
        recipe = Recipe.objects.create(
            name="Simple IPA",
            description="A simple IPA recipe",
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.060
        )

        serializer = self.serializer_class(recipe)
        data = serializer.data

        assert data['name'] == "Simple IPA"
        assert data['description'] == "A simple IPA recipe"
        assert data['batch_size_gallons'] == 5.0
        assert data['mash_efficiency_percent'] == 75.0
        assert data['target_original_gravity'] == 1.060
        assert 'id' in data
        assert 'created_at' in data
        assert 'updated_at' in data

    @pytest.mark.django_db
    def test_serializes_calculated_properties(self):
        """Test serialization of calculated properties."""
        recipe = Recipe.objects.create(
            name="Test Recipe",
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

        serializer = self.serializer_class(recipe)
        data = serializer.data

        # Test that calculated properties are included
        assert 'calculated_original_gravity' in data
        assert 'original_gravity' in data
        assert 'calculated_srm' in data
        assert 'total_ibus' in data
        assert 'estimated_final_gravity' in data
        assert 'estimated_abv' in data

        # Test that they return numeric values
        assert isinstance(data['calculated_original_gravity'], float)
        assert isinstance(data['original_gravity'], float)
        assert isinstance(data['calculated_srm'], (int, float))
        assert isinstance(data['total_ibus'], (int, float))
        assert isinstance(data['estimated_final_gravity'], float)
        assert isinstance(data['estimated_abv'], (int, float))

    @pytest.mark.django_db
    def test_serializes_empty_related_data(self):
        """Test serialization of empty related data collections."""
        recipe = Recipe.objects.create(
            name="Empty Recipe",
            batch_size_gallons=5.0
        )

        serializer = self.serializer_class(recipe)
        data = serializer.data

        # Test that all related data fields are present and empty
        related_fields = [
            'fermentable_inclusions', 'boil_hop_inclusions',
            'first_wort_hop_inclusions', 'flameout_hop_inclusions',
            'whirlpool_hop_inclusions', 'dry_hop_inclusions',
            'yeast_inclusions', 'mash_steps', 'fermentation_phases'
        ]

        for field in related_fields:
            assert field in data
            assert isinstance(data[field], list)
            assert len(data[field]) == 0

        # Water profile should be None
        assert data['water_profile'] is None

    @pytest.mark.django_db
    def test_serializes_recipe_with_fermentables(self):
        """Test serialization of recipe with fermentable inclusions."""
        recipe = Recipe.objects.create(
            name="Recipe with Fermentables",
            batch_size_gallons=5.0
        )

        fermentable = Fermentable.objects.create(
            name="Pale 2-Row",
            extract_potential_ppg=37.0,
            color_lovibond=2.0
        )

        FermentableInclusion.objects.create(
            recipe=recipe,
            fermentable=fermentable,
            quantity=10.0,
            quantity_unit="lb",
            efficiency_percent=75.0
        )

        serializer = self.serializer_class(recipe)
        data = serializer.data

        assert len(data['fermentable_inclusions']) == 1
        fermentable_data = data['fermentable_inclusions'][0]
        assert fermentable_data['fermentable']['name'] == "Pale 2-Row"
        assert fermentable_data['quantity'] == 10.0
        assert fermentable_data['quantity_unit'] == "lb"
        assert fermentable_data['efficiency_percent'] == 75.0
        assert 'gravity_points_contribution' in fermentable_data
        assert 'srm_contribution' in fermentable_data

    @pytest.mark.django_db
    def test_serializes_recipe_with_hops(self):
        """Test serialization of recipe with hop inclusions."""
        recipe = Recipe.objects.create(
            name="Recipe with Hops",
            batch_size_gallons=5.0
        )

        hop = Hop.objects.create(
            name="Cascade",
            alpha_acid=5.5,
            aroma=True,
            bittering=False
        )

        BoilHopInclusion.objects.create(
            recipe=recipe,
            hop=hop,
            quantity=1.0,
            quantity_unit="oz",
            time_minutes=60,
            notes="Bittering addition"
        )

        serializer = self.serializer_class(recipe)
        data = serializer.data

        assert len(data['boil_hop_inclusions']) == 1
        hop_data = data['boil_hop_inclusions'][0]
        assert hop_data['hop']['name'] == "Cascade"
        assert hop_data['quantity'] == 1.0
        assert hop_data['quantity_unit'] == "oz"
        assert hop_data['time_minutes'] == 60
        assert hop_data['notes'] == "Bittering addition"
        assert 'ibu_contribution' in hop_data
        assert 'is_bittering_addition' in hop_data
        assert 'is_aroma_addition' in hop_data

    @pytest.mark.django_db
    def test_serializes_recipe_with_water_profile(self):
        """Test serialization of recipe with water profile."""
        water_profile = WaterProfile.objects.create(
            name="Burton on Trent",
            description="Classic English brewing water",
            calcium_ppm=295.0,
            sulfate_ppm=725.0
        )

        recipe = Recipe.objects.create(
            name="Recipe with Water Profile",
            batch_size_gallons=5.0,
            water_profile=water_profile
        )

        serializer = self.serializer_class(recipe)
        data = serializer.data

        assert data['water_profile'] is not None
        water_data = data['water_profile']
        assert water_data['name'] == "Burton on Trent"
        assert water_data['description'] == "Classic English brewing water"
        assert water_data['calcium_ppm'] == 295.0
        assert water_data['sulfate_ppm'] == 725.0

    @pytest.mark.django_db
    def test_serializes_recipe_with_yeast(self):
        """Test serialization of recipe with yeast inclusions."""
        recipe = Recipe.objects.create(
            name="Recipe with Yeast",
            batch_size_gallons=5.0
        )

        yeast = Yeast.objects.create(
            name="Safale US-05",
            min_temperature_fahrenheit=59.0,
            max_temperature_fahrenheit=75.0,
            attenuation_percent=75.0
        )

        YeastInclusion.objects.create(
            recipe=recipe,
            yeast=yeast,
            quantity=1.0,
            quantity_unit="PACKET",
            starter_made=True,
            starter_size_ml=500.0
        )

        serializer = self.serializer_class(recipe)
        data = serializer.data

        assert len(data['yeast_inclusions']) == 1
        yeast_data = data['yeast_inclusions'][0]
        assert yeast_data['yeast']['name'] == "Safale US-05"
        assert yeast_data['quantity'] == 1.0
        assert yeast_data['quantity_unit'] == "PACKET"
        assert yeast_data['starter_made'] is True
        assert yeast_data['starter_size_ml'] == 500.0

    @pytest.mark.django_db
    def test_serializes_recipe_with_mash_steps(self):
        """Test serialization of recipe with mash steps."""
        recipe = Recipe.objects.create(
            name="Recipe with Mash Steps",
            batch_size_gallons=5.0
        )

        MashStep.objects.create(
            recipe=recipe,
            name="Single Infusion",
            step_type="INFUSION",
            temperature_fahrenheit=152.0,
            duration_minutes=60,
            step_order=1
        )

        serializer = self.serializer_class(recipe)
        data = serializer.data

        assert len(data['mash_steps']) == 1
        mash_data = data['mash_steps'][0]
        assert mash_data['name'] == "Single Infusion"
        assert mash_data['step_type'] == "INFUSION"
        assert mash_data['temperature_fahrenheit'] == 152.0
        assert mash_data['duration_minutes'] == 60
        assert mash_data['step_order'] == 1
        assert 'duration_display' in mash_data

    @pytest.mark.django_db
    def test_serializes_recipe_with_fermentation_phases(self):
        """Test serialization of recipe with fermentation phases."""
        recipe = Recipe.objects.create(
            name="Recipe with Fermentation Phases",
            batch_size_gallons=5.0
        )

        FermentationPhase.objects.create(
            recipe=recipe,
            name="Primary Fermentation",
            phase_type="PRIMARY",
            temperature_fahrenheit=68.0,
            duration_days=7,
            phase_order=1
        )

        serializer = self.serializer_class(recipe)
        data = serializer.data

        assert len(data['fermentation_phases']) == 1
        fermentation_data = data['fermentation_phases'][0]
        assert fermentation_data['name'] == "Primary Fermentation"
        assert fermentation_data['phase_type'] == "PRIMARY"
        assert fermentation_data['temperature_fahrenheit'] == 68.0
        assert fermentation_data['duration_days'] == 7
        assert fermentation_data['phase_order'] == 1
        assert 'duration_display' in fermentation_data
        assert 'temperature_display' in fermentation_data

    def test_validates_basic_recipe_data(self):
        """Test validation of basic recipe data."""
        valid_data = {
            'name': 'Test Recipe',
            'description': 'A test recipe',
            'batch_size_gallons': 5.0,
            'mash_efficiency_percent': 75.0,
            'target_original_gravity': 1.050,
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        validated_data = serializer.validated_data
        assert validated_data['name'] == 'Test Recipe'
        assert validated_data['batch_size_gallons'] == 5.0
        assert validated_data['mash_efficiency_percent'] == 75.0
        assert validated_data['target_original_gravity'] == 1.050

    def test_validates_required_fields(self):
        """Test validation fails when required fields are missing."""
        incomplete_data = {
            'description': 'Missing required fields',
            # Missing name (the only truly required field)
        }

        serializer = self.serializer_class(data=incomplete_data)
        assert not serializer.is_valid()

        # Check that required fields are in errors
        assert 'name' in serializer.errors
        # batch_size_gallons has a default, so it's not required

    def test_validates_batch_size_positive(self):
        """Test validation of positive batch size."""
        valid_data = {
            'name': 'Test Recipe',
            'batch_size_gallons': 5.0,
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test various batch sizes
        for batch_size in [1.0, 2.5, 5.0, 10.0, 15.5]:
            data = valid_data.copy()
            data['batch_size_gallons'] = batch_size
            serializer = self.serializer_class(data=data)
            assert serializer.is_valid(), f"Failed for batch_size={batch_size}: {serializer.errors}"

    def test_validates_efficiency_percent_range(self):
        """Test validation of efficiency percentage range."""
        valid_data = {
            'name': 'Test Recipe',
            'batch_size_gallons': 5.0,
            'mash_efficiency_percent': 75.0,
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test typical efficiency range
        for efficiency in [60.0, 70.0, 75.0, 80.0, 85.0]:
            data = valid_data.copy()
            data['mash_efficiency_percent'] = efficiency
            serializer = self.serializer_class(data=data)
            assert serializer.is_valid(), f"Failed for efficiency={efficiency}: {serializer.errors}"

    def test_validates_gravity_range(self):
        """Test validation of gravity values."""
        valid_data = {
            'name': 'Test Recipe',
            'batch_size_gallons': 5.0,
            'target_original_gravity': 1.050,
        }

        serializer = self.serializer_class(data=valid_data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        # Test typical gravity range
        for gravity in [1.030, 1.040, 1.050, 1.060, 1.080, 1.100]:
            data = valid_data.copy()
            data['target_original_gravity'] = gravity
            serializer = self.serializer_class(data=data)
            assert serializer.is_valid(), f"Failed for gravity={gravity}: {serializer.errors}"
