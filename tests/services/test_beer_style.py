"""
Tests for BeerStyleService.

This module contains comprehensive tests for the BeerStyleService class,
testing all business logic with proper repository mocking.
"""

import pytest
from unittest.mock import Mock, patch

from core.models import BeerStyle, Recipe
from core.services.beer_style import BeerStyleService
from core.repositories.beer_style import BeerStyleRepository
from core.repositories.recipe import RecipeRepository
from tests.base import BaseServiceTest


class TestBeerStyleService(BaseServiceTest):
    """Test the BeerStyleService class."""

    service_class = BeerStyleService

    def test_service_initialization_with_dependencies(self):
        """Test service initialization with injected dependencies."""
        assert self.service.repository is not None
        assert self.service.recipe_repository is not None

    def test_service_has_required_repositories(self):
        """Test that service has required repository dependencies."""
        assert hasattr(self.service, 'repository')
        assert hasattr(self.service, 'recipe_repository')

    def test_get_all_styles(self):
        """Test getting all beer styles."""
        mock_styles = [
            <PERSON><PERSON>(name="American IPA"),
            <PERSON><PERSON>(name="Imperial Stout"),
        ]

        # Configure the mock before calling the service
        self.service.repository.get_all.return_value = mock_styles

        result = self.service.get_all_styles()

        self.service.repository.get_all.assert_called_once()
        assert result == mock_styles

    def test_get_style_by_id(self):
        """Test getting a beer style by ID."""
        mock_style = Mock(name="American IPA")
        mock_repo = self.get_mock(BeerStyleRepository)
        mock_repo.get_by_id.return_value = mock_style

        result = self.service.get_style_by_id("bst_123")

        mock_repo.get_by_id.assert_called_once_with("bst_123")
        assert result == mock_style

    def test_get_style_by_id_not_found(self):
        """Test getting a beer style by ID when not found."""
        self.service.repository.get_by_id.return_value = None

        result = self.service.get_style_by_id("bst_nonexistent")

        self.service.repository.get_by_id.assert_called_once_with("bst_nonexistent")
        assert result is None

    def test_search_styles(self):
        """Test searching beer styles by name."""
        mock_styles = [Mock(name="American IPA"), Mock(name="English IPA")]
        self.service.repository.search_by_name.return_value = mock_styles

        result = self.service.search_styles("IPA")

        self.service.repository.search_by_name.assert_called_once_with("IPA")
        assert result == mock_styles

    def test_get_default_style(self):
        """Test getting the default beer style."""
        mock_style = Mock(name="Custom Style")
        self.service.repository.get_default_style.return_value = mock_style

        result = self.service.get_default_style()

        self.service.repository.get_default_style.assert_called_once()
        assert result == mock_style

    # Note: create_style method doesn't exist on BeerStyleService
    # This test is skipped until the method is implemented
    def test_create_style(self):
        """Test creating a new beer style."""
        pytest.skip("create_style method not implemented on BeerStyleService")

    def test_create_style_minimal(self):
        """Test creating a beer style with minimal parameters."""
        pytest.skip("create_style method not implemented on BeerStyleService")

    def test_update_style(self):
        """Test updating an existing beer style."""
        pytest.skip("update_style method not implemented on BeerStyleService")

    def test_delete_style(self):
        """Test deleting a beer style."""
        pytest.skip("delete_style method not implemented on BeerStyleService")

    def test_find_matching_styles_for_recipe(self):
        """Test finding matching styles for a recipe."""
        # Mock recipe with calculated properties
        mock_recipe = Mock()
        mock_recipe.calculated_srm = 10.0
        mock_recipe.total_ibus = 55.0
        mock_recipe.original_gravity = 1.062
        mock_recipe.estimated_final_gravity = 1.012
        mock_recipe.estimated_abv = 6.5

        mock_styles = [Mock(name="American IPA")]

        self.service.recipe_repository.get_by_id.return_value = mock_recipe
        self.service.repository.get_styles_for_recipe_stats.return_value = mock_styles

        result = self.service.find_matching_styles_for_recipe("rec_123")

        self.service.recipe_repository.get_by_id.assert_called_once_with("rec_123")
        self.service.repository.get_styles_for_recipe_stats.assert_called_once_with(
            srm=10.0,
            ibu=55.0,
            og=1.062,
            fg=1.012,
            abv=6.5,
        )
        assert result == mock_styles

    def test_find_matching_styles_for_recipe_not_found(self):
        """Test finding matching styles when recipe is not found."""
        self.service.recipe_repository.get_by_id.return_value = None

        result = self.service.find_matching_styles_for_recipe("rec_nonexistent")

        self.service.recipe_repository.get_by_id.assert_called_once_with("rec_nonexistent")
        self.service.repository.get_styles_for_recipe_stats.assert_not_called()
        assert result == []

    def test_assign_style_to_recipe_success(self):
        """Test successfully assigning a beer style to a recipe."""
        mock_recipe = Mock()
        mock_recipe.name = "Test Recipe"
        mock_style = Mock()
        mock_style.name = "American IPA"

        self.service.recipe_repository.get_by_id.return_value = mock_recipe
        self.service.repository.get_by_id.return_value = mock_style

        result = self.service.assign_style_to_recipe("rec_123", "bst_456")

        self.service.recipe_repository.get_by_id.assert_called_once_with("rec_123")
        self.service.repository.get_by_id.assert_called_once_with("bst_456")

        # Verify assignment
        assert mock_recipe.beer_style == mock_style
        mock_recipe.save.assert_called_once()

        assert result == "Successfully assigned beer style 'American IPA' to recipe 'Test Recipe'"

    def test_assign_style_to_recipe_recipe_not_found(self):
        """Test assigning style when recipe is not found."""
        self.service.recipe_repository.get_by_id.return_value = None
        mock_style = Mock()
        self.service.repository.get_by_id.return_value = mock_style

        result = self.service.assign_style_to_recipe("rec_nonexistent", "bst_456")

        self.service.recipe_repository.get_by_id.assert_called_once_with("rec_nonexistent")
        self.service.repository.get_by_id.assert_called_once_with("bst_456")

        assert result == "Recipe with ID rec_nonexistent not found"

    def test_assign_style_to_recipe_style_not_found(self):
        """Test assigning style when beer style is not found."""
        mock_recipe = Mock()
        self.service.recipe_repository.get_by_id.return_value = mock_recipe
        self.service.repository.get_by_id.return_value = None

        result = self.service.assign_style_to_recipe("rec_123", "bst_nonexistent")

        self.service.recipe_repository.get_by_id.assert_called_once_with("rec_123")
        self.service.repository.get_by_id.assert_called_once_with("bst_nonexistent")

        assert result == "Beer style with ID bst_nonexistent not found"

    def test_assign_style_to_recipe_both_not_found(self):
        """Test assigning style when both recipe and style are not found."""
        self.service.recipe_repository.get_by_id.return_value = None
        self.service.repository.get_by_id.return_value = None

        result = self.service.assign_style_to_recipe("rec_nonexistent", "bst_nonexistent")

        # Should return recipe not found message first
        assert result == "Recipe with ID rec_nonexistent not found"
