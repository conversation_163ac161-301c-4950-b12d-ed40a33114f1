"""
Tests for FermentableService.

This module contains comprehensive tests for the FermentableService class,
testing all business logic with proper repository mocking.
"""

import pytest
from unittest.mock import Mock, patch

from core.services import FermentableService
from core.models import Fermentable, FermentableType
from core.repositories import FermentableRepository
from tests.base import BaseServiceTest


class TestFermentableService(BaseServiceTest):
    """Test FermentableService functionality."""

    service_class = FermentableService

    def test_service_initialization(self):
        """Test that service initializes with repository."""
        assert self.service.repository is not None

    def test_get_fermentable_by_id_found(self):
        """Test getting fermentable by ID when found."""
        # Arrange
        fermentable_id = "fer_test123"
        expected_fermentable = Mock(id=fermentable_id)
        self.service.repository.get_by_id.return_value = expected_fermentable

        # Act
        result = self.service.get_fermentable_by_id(fermentable_id)

        # Assert
        assert result == expected_fermentable
        self.service.repository.get_by_id.assert_called_once_with(fermentable_id)

    def test_get_fermentable_by_id_not_found(self):
        """Test getting fermentable by ID when not found."""
        # Arrange
        fermentable_id = "fer_nonexistent"
        self.service.repository.get_by_id.return_value = None

        # Act
        result = self.service.get_fermentable_by_id(fermentable_id)

        # Assert
        assert result is None
        self.service.repository.get_by_id.assert_called_once_with(fermentable_id)

    def test_get_all_fermentables(self):
        """Test getting all fermentables."""
        # Arrange
        expected_fermentables = [
            Mock(id="fer_1", name="Fermentable 1"),
            Mock(id="fer_2", name="Fermentable 2"),
            Mock(id="fer_3", name="Fermentable 3")
        ]
        self.service.repository.get_all.return_value = expected_fermentables

        # Act
        result = self.service.get_all_fermentables()

        # Assert
        assert result == expected_fermentables
        assert len(result) == 3
        self.service.repository.get_all.assert_called_once()

    def test_get_all_fermentables_empty(self):
        """Test getting all fermentables when none exist."""
        # Arrange
        self.service.repository.get_all.return_value = []

        # Act
        result = self.service.get_all_fermentables()

        # Assert
        assert result == []
        self.service.repository.get_all.assert_called_once()

    def test_agent_accessible_decorators(self):
        """Test that methods have agent_accessible decorators."""
        # Check that the actual methods have the agent_accessible decorator
        assert hasattr(self.service.get_fermentable_by_id, '_tool_metadata')
        assert hasattr(self.service.get_all_fermentables, '_tool_metadata')

        # Check that the metadata contains the expected information
        metadata1 = self.service.get_fermentable_by_id._tool_metadata
        metadata2 = self.service.get_all_fermentables._tool_metadata

        assert metadata1.description is not None
        assert metadata2.description is not None
        assert metadata1.serializer is not None
        assert metadata2.serializer is not None

    def test_service_has_correct_methods(self):
        """Test that service has the expected methods."""
        # Test that the service has the methods we expect
        assert hasattr(self.service, 'get_fermentable_by_id')
        assert hasattr(self.service, 'get_all_fermentables')
        assert hasattr(self.service, 'repository')

        # Test that methods are callable
        assert callable(self.service.get_fermentable_by_id)
        assert callable(self.service.get_all_fermentables)
