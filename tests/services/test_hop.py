"""
Tests for HopService.

This module contains comprehensive tests for the HopService class,
testing all business logic with proper repository mocking.
"""

import pytest
from unittest.mock import Mock, patch

from core.services import HopService
from core.models import Hop, BoilHopInclusion, DryHopInclusion, FirstWortHopInclusion, FlameoutHopInclusion, WhirlpoolHopInclusion
from core.repositories import (
    HopRepository, RecipeRepository, BoilHopInclusionRepository,
    DryHopInclusionRepository, FirstWortHopInclusionRepository,
    FlameoutHopInclusionRepository, WhirlpoolHopInclusionRepository
)
from .base import BaseServiceTest, MockRepositoryFactory


class TestHopService(BaseServiceTest):
    """Test HopService functionality."""

    service_class = HopService

    def setup_method(self):
        """Set up test method with mocked repositories."""
        self.mock_hop_repository = MockRepositoryFactory.create_hop_repository_mock()
        self.mock_recipe_repository = MockRepositoryFactory.create_recipe_repository_mock()
        self.mock_boil_hop_repository = MockRepositoryFactory.create_hop_inclusion_repository_mock()
        self.mock_first_wort_repository = MockRepositoryFactory.create_hop_inclusion_repository_mock()
        self.mock_flameout_repository = MockRepositoryFactory.create_hop_inclusion_repository_mock()
        self.mock_whirlpool_repository = MockRepositoryFactory.create_hop_inclusion_repository_mock()
        self.mock_dry_hop_repository = MockRepositoryFactory.create_hop_inclusion_repository_mock()

        # Create the service with mocked repositories directly
        self.service = HopService(
            recipe_repository=self.mock_recipe_repository,
            hop_repository=self.mock_hop_repository,
            boil_hop_repository=self.mock_boil_hop_repository,
            first_wort_repository=self.mock_first_wort_repository,
            flameout_repository=self.mock_flameout_repository,
            whirlpool_repository=self.mock_whirlpool_repository,
            dry_hop_repository=self.mock_dry_hop_repository,
        )

    def test_service_initialization(self):
        """Test that service initializes with all repositories."""
        assert self.service.hop_repository is not None
        assert self.service.recipe_repository is not None
        assert self.service.boil_hop_repository is not None
        assert self.service.first_wort_repository is not None
        assert self.service.flameout_repository is not None
        assert self.service.whirlpool_repository is not None
        assert self.service.dry_hop_repository is not None

        assert self.service.hop_repository == self.mock_hop_repository
        assert self.service.recipe_repository == self.mock_recipe_repository
        assert self.service.boil_hop_repository == self.mock_boil_hop_repository
        assert self.service.first_wort_repository == self.mock_first_wort_repository
        assert self.service.flameout_repository == self.mock_flameout_repository
        assert self.service.whirlpool_repository == self.mock_whirlpool_repository
        assert self.service.dry_hop_repository == self.mock_dry_hop_repository

    def test_get_hop_by_id_found(self):
        """Test getting hop by ID when found."""
        # Arrange
        hop_id = "hop_test123"
        expected_hop = self.create_mock_hop(id=hop_id)
        self.mock_hop_repository.get_by_id.return_value = expected_hop

        # Act
        result = self.service.get_hop_by_id(hop_id)

        # Assert
        assert result == expected_hop
        self.mock_hop_repository.get_by_id.assert_called_once_with(hop_id)

    def test_get_hop_by_id_not_found(self):
        """Test getting hop by ID when not found."""
        # Arrange
        hop_id = "hop_nonexistent"
        self.mock_hop_repository.get_by_id.return_value = None

        # Act
        result = self.service.get_hop_by_id(hop_id)

        # Assert
        assert result is None
        self.mock_hop_repository.get_by_id.assert_called_once_with(hop_id)

    def test_get_all_hops(self):
        """Test getting all hops."""
        # Arrange
        expected_hops = [
            self.create_mock_hop(id="hop_1", name="Cascade"),
            self.create_mock_hop(id="hop_2", name="Centennial"),
            self.create_mock_hop(id="hop_3", name="Chinook")
        ]
        self.mock_hop_repository.get_all.return_value = expected_hops

        # Act
        result = self.service.get_all_hops()

        # Assert
        assert result == expected_hops
        assert len(result) == 3
        self.mock_hop_repository.get_all.assert_called_once()

    def test_search_hops_by_name(self):
        """Test searching hops by name."""
        # Arrange
        search_query = "cascade"
        expected_hops = [
            self.create_mock_hop(id="hop_1", name="Cascade"),
            self.create_mock_hop(id="hop_2", name="Cascade (US)")
        ]
        self.mock_hop_repository.search_by_name.return_value = expected_hops

        # Act
        result = self.service.search_hops(search_query)

        # Assert
        assert result == expected_hops
        assert len(result) == 2
        self.mock_hop_repository.search_by_name.assert_called_once_with(search_query)

    def test_get_aroma_hops(self):
        """Test getting aroma hops."""
        # Arrange
        expected_hops = [
            self.create_mock_hop(id="hop_1", name="Cascade", aroma=True, bittering=False),
            self.create_mock_hop(id="hop_2", name="Saaz", aroma=True, bittering=False)
        ]
        self.mock_hop_repository.get_aroma_hops.return_value = expected_hops

        # Act
        result = self.service.get_aroma_hops()

        # Assert
        assert result == expected_hops
        assert len(result) == 2
        self.mock_hop_repository.get_aroma_hops.assert_called_once()

    def test_get_bittering_hops(self):
        """Test getting bittering hops."""
        # Arrange
        expected_hops = [
            self.create_mock_hop(id="hop_1", name="Magnum", aroma=False, bittering=True),
            self.create_mock_hop(id="hop_2", name="Warrior", aroma=False, bittering=True)
        ]
        self.mock_hop_repository.get_bittering_hops.return_value = expected_hops

        # Act
        result = self.service.get_bittering_hops()

        # Assert
        assert result == expected_hops
        assert len(result) == 2
        self.mock_hop_repository.get_bittering_hops.assert_called_once()

    def test_get_hops_by_country(self):
        """Test getting hops by country."""
        # Arrange
        country = "Germany"
        expected_hops = [
            self.create_mock_hop(id="hop_1", name="Hallertau", country_of_origin=country),
            self.create_mock_hop(id="hop_2", name="Tettnang", country_of_origin=country)
        ]
        self.mock_hop_repository.get_by_country.return_value = expected_hops

        # Act
        result = self.service.get_hops_by_country(country)

        # Assert
        assert result == expected_hops
        assert len(result) == 2
        self.mock_hop_repository.get_by_country.assert_called_once_with(country)

    def test_get_high_alpha_hops_default(self):
        """Test getting high alpha hops with default minimum."""
        # Arrange
        expected_hops = [
            self.create_mock_hop(id="hop_1", name="Warrior", alpha_acid_percent=15.0),
            self.create_mock_hop(id="hop_2", name="Magnum", alpha_acid_percent=12.0)
        ]
        self.mock_hop_repository.get_high_alpha_hops.return_value = expected_hops

        # Act
        result = self.service.get_high_alpha_hops()

        # Assert
        assert result == expected_hops
        assert len(result) == 2
        self.mock_hop_repository.get_high_alpha_hops.assert_called_once_with(10.0)

    def test_get_high_alpha_hops_custom_minimum(self):
        """Test getting high alpha hops with custom minimum."""
        # Arrange
        min_alpha = 12.0
        expected_hops = [
            self.create_mock_hop(id="hop_1", name="Warrior", alpha_acid_percent=15.0)
        ]
        self.mock_hop_repository.get_high_alpha_hops.return_value = expected_hops

        # Act
        result = self.service.get_high_alpha_hops(min_alpha)

        # Assert
        assert result == expected_hops
        self.mock_hop_repository.get_high_alpha_hops.assert_called_once_with(min_alpha)

    def test_get_low_alpha_hops_default(self):
        """Test getting low alpha hops with default maximum."""
        # Arrange
        expected_hops = [
            self.create_mock_hop(id="hop_1", name="Saaz", alpha_acid_percent=3.5),
            self.create_mock_hop(id="hop_2", name="Tettnang", alpha_acid_percent=4.5)
        ]
        self.mock_hop_repository.get_low_alpha_hops.return_value = expected_hops

        # Act
        result = self.service.get_low_alpha_hops()

        # Assert
        assert result == expected_hops
        assert len(result) == 2
        self.mock_hop_repository.get_low_alpha_hops.assert_called_once_with(6.0)

    def test_get_low_alpha_hops_custom_maximum(self):
        """Test getting low alpha hops with custom maximum."""
        # Arrange
        max_alpha = 4.0
        expected_hops = [
            self.create_mock_hop(id="hop_1", name="Saaz", alpha_acid_percent=3.5)
        ]
        self.mock_hop_repository.get_low_alpha_hops.return_value = expected_hops

        # Act
        result = self.service.get_low_alpha_hops(max_alpha)

        # Assert
        assert result == expected_hops
        self.mock_hop_repository.get_low_alpha_hops.assert_called_once_with(max_alpha)

    def test_agent_accessible_decorators(self):
        """Test that methods have agent_accessible decorators."""
        # Check that key methods have the agent_accessible decorator
        assert hasattr(self.service.get_hop_by_id, '_tool_metadata')
        assert hasattr(self.service.get_all_hops, '_tool_metadata')
        assert hasattr(self.service.search_hops, '_tool_metadata')
        assert hasattr(self.service.get_aroma_hops, '_tool_metadata')
        assert hasattr(self.service.get_bittering_hops, '_tool_metadata')
        assert hasattr(self.service.get_hops_by_country, '_tool_metadata')
        assert hasattr(self.service.get_high_alpha_hops, '_tool_metadata')
        assert hasattr(self.service.get_low_alpha_hops, '_tool_metadata')

    def test_service_has_correct_methods(self):
        """Test that service has the expected methods."""
        # Test that the service has the methods we expect
        expected_methods = [
            'get_hop_by_id', 'get_all_hops', 'search_hops',
            'get_aroma_hops', 'get_bittering_hops', 'get_hops_by_country',
            'get_high_alpha_hops', 'get_low_alpha_hops'
        ]

        for method_name in expected_methods:
            assert hasattr(self.service, method_name)
            assert callable(getattr(self.service, method_name))

    def test_service_repository_attributes(self):
        """Test that service has all expected repository attributes."""
        expected_repositories = [
            'hop_repository', 'recipe_repository', 'boil_hop_repository',
            'first_wort_repository', 'flameout_repository',
            'whirlpool_repository', 'dry_hop_repository'
        ]

        for repo_name in expected_repositories:
            assert hasattr(self.service, repo_name)
            assert getattr(self.service, repo_name) is not None

    # Tests for hop inclusion operations

    def test_add_boil_hop_to_recipe_success(self):
        """Test successfully adding a boil hop to a recipe."""
        # Arrange
        recipe_id = "rec_test123"
        hop_id = "hop_test123"
        quantity = 1.0
        quantity_unit = "OUNCES"
        time_minutes = 60
        notes = "Bittering addition"

        mock_recipe = self.create_mock_recipe(id=recipe_id)
        mock_hop = self.create_mock_hop(id=hop_id)
        mock_inclusion = self.create_mock_hop_inclusion(
            'boil',
            recipe=mock_recipe,
            hop=mock_hop,
            quantity=quantity,
            quantity_unit=quantity_unit,
            time_minutes=time_minutes,
            notes=notes
        )

        self.mock_recipe_repository.get_by_id_or_raise.return_value = mock_recipe
        self.mock_hop_repository.get_by_id_or_raise.return_value = mock_hop
        self.mock_boil_hop_repository.create.return_value = mock_inclusion

        # Act
        result = self.service.add_boil_hop_to_recipe(
            recipe_id, hop_id, quantity, quantity_unit, time_minutes, notes
        )

        # Assert
        assert result is not None  # Service should return the created inclusion
        self.mock_recipe_repository.get_by_id_or_raise.assert_called_once_with(recipe_id)
        self.mock_hop_repository.get_by_id_or_raise.assert_called_once_with(hop_id)
        # Verify create was called with correct parameters
        create_call = self.mock_boil_hop_repository.create.call_args
        assert create_call is not None
        _, kwargs = create_call
        assert kwargs['quantity'] == quantity
        assert kwargs['quantity_unit'] == quantity_unit
        assert kwargs['time_minutes'] == time_minutes
        assert kwargs['notes'] == notes

    def test_add_dry_hop_to_recipe_success(self):
        """Test successfully adding a dry hop to a recipe."""
        # Arrange
        recipe_id = "rec_test123"
        hop_id = "hop_test123"
        quantity = 2.0
        quantity_unit = "OUNCES"
        time_days = 3
        notes = "Aroma addition"

        mock_recipe = self.create_mock_recipe(id=recipe_id)
        mock_hop = self.create_mock_hop(id=hop_id)
        mock_inclusion = self.create_mock_hop_inclusion(
            'dry',
            recipe=mock_recipe,
            hop=mock_hop,
            quantity=quantity,
            quantity_unit=quantity_unit,
            time_days=time_days,
            notes=notes
        )

        self.mock_recipe_repository.get_by_id_or_raise.return_value = mock_recipe
        self.mock_hop_repository.get_by_id_or_raise.return_value = mock_hop
        self.mock_dry_hop_repository.create.return_value = mock_inclusion

        # Act
        result = self.service.add_dry_hop_to_recipe(
            recipe_id, hop_id, quantity, quantity_unit, time_days, notes
        )

        # Assert
        assert result is not None  # Service should return the created inclusion
        self.mock_recipe_repository.get_by_id_or_raise.assert_called_once_with(recipe_id)
        self.mock_hop_repository.get_by_id_or_raise.assert_called_once_with(hop_id)
        # Verify create was called with correct parameters
        create_call = self.mock_dry_hop_repository.create.call_args
        assert create_call is not None
        _, kwargs = create_call
        assert kwargs['quantity'] == quantity
        assert kwargs['quantity_unit'] == quantity_unit
        assert kwargs['time_days'] == time_days
        assert kwargs['notes'] == notes

    def test_add_whirlpool_hop_to_recipe_success(self):
        """Test successfully adding a whirlpool hop to a recipe."""
        # Arrange
        recipe_id = "rec_test123"
        hop_id = "hop_test123"
        quantity = 1.5
        quantity_unit = "OUNCES"
        time_minutes = 20
        temperature_f = 180
        notes = "Whirlpool addition"

        mock_recipe = self.create_mock_recipe(id=recipe_id)
        mock_hop = self.create_mock_hop(id=hop_id)
        mock_inclusion = self.create_mock_hop_inclusion(
            'whirlpool',
            recipe=mock_recipe,
            hop=mock_hop,
            quantity=quantity,
            quantity_unit=quantity_unit,
            time_minutes=time_minutes,
            temperature_fahrenheit=temperature_f,
            notes=notes
        )

        self.mock_recipe_repository.get_by_id_or_raise.return_value = mock_recipe
        self.mock_hop_repository.get_by_id_or_raise.return_value = mock_hop
        self.mock_whirlpool_repository.create.return_value = mock_inclusion

        # Act
        result = self.service.add_whirlpool_hop_to_recipe(
            recipe_id, hop_id, quantity, quantity_unit, time_minutes, temperature_f, notes
        )

        # Assert
        assert result is not None  # Service should return the created inclusion
        self.mock_recipe_repository.get_by_id_or_raise.assert_called_once_with(recipe_id)
        self.mock_hop_repository.get_by_id_or_raise.assert_called_once_with(hop_id)
        # Verify create was called with correct parameters
        create_call = self.mock_whirlpool_repository.create.call_args
        assert create_call is not None
        _, kwargs = create_call
        assert kwargs['quantity'] == quantity
        assert kwargs['quantity_unit'] == quantity_unit
        assert kwargs['time_minutes'] == time_minutes
        assert kwargs['temperature_f'] == temperature_f
        assert kwargs['notes'] == notes

    def test_update_boil_hop_inclusion_success(self):
        """Test successfully updating a boil hop inclusion."""
        # Arrange
        inclusion_id = "inc_test123"
        new_quantity = 1.5
        new_time_minutes = 45
        new_notes = "Updated bittering addition"

        mock_inclusion = self.create_mock_hop_inclusion('boil', id=inclusion_id)
        updated_inclusion = self.create_mock_hop_inclusion(
            'boil',
            id=inclusion_id,
            quantity=new_quantity,
            time_minutes=new_time_minutes,
            notes=new_notes
        )

        self.mock_boil_hop_repository.get_by_id_or_raise.return_value = mock_inclusion
        self.mock_boil_hop_repository.update.return_value = updated_inclusion

        # Act
        result = self.service.update_boil_hop_inclusion(
            inclusion_id,
            quantity=new_quantity,
            time_minutes=new_time_minutes,
            notes=new_notes
        )

        # Assert
        assert result is not None  # Service should return the updated inclusion
        self.mock_boil_hop_repository.get_by_id_or_raise.assert_called_once_with(inclusion_id)
        # Verify update was called with correct parameters
        update_call = self.mock_boil_hop_repository.update.call_args
        assert update_call is not None
        _, kwargs = update_call
        assert kwargs['quantity'] == new_quantity
        assert kwargs['time_minutes'] == new_time_minutes
        assert kwargs['notes'] == new_notes

    def test_update_dry_hop_inclusion_partial(self):
        """Test updating only some fields of a dry hop inclusion."""
        # Arrange
        inclusion_id = "inc_test123"
        new_time_days = 5

        mock_inclusion = self.create_mock_hop_inclusion('dry', id=inclusion_id)
        updated_inclusion = self.create_mock_hop_inclusion(
            'dry',
            id=inclusion_id,
            time_days=new_time_days
        )

        self.mock_dry_hop_repository.get_by_id_or_raise.return_value = mock_inclusion
        self.mock_dry_hop_repository.update.return_value = updated_inclusion

        # Act
        result = self.service.update_dry_hop_inclusion(
            inclusion_id,
            time_days=new_time_days
        )

        # Assert
        assert result is not None  # Service should return the updated inclusion
        self.mock_dry_hop_repository.get_by_id_or_raise.assert_called_once_with(inclusion_id)
        # Verify update was called with correct parameters
        update_call = self.mock_dry_hop_repository.update.call_args
        assert update_call is not None
        _, kwargs = update_call
        assert kwargs['time_days'] == new_time_days

    def test_delete_boil_hop_inclusion_success(self):
        """Test successfully deleting a boil hop inclusion."""
        # Arrange
        inclusion_id = "inc_test123"
        mock_inclusion = self.create_mock_hop_inclusion('boil', id=inclusion_id)

        self.mock_boil_hop_repository.get_by_id_or_raise.return_value = mock_inclusion

        # Act
        result = self.service.delete_boil_hop_inclusion(inclusion_id)

        # Assert
        assert result is True
        self.mock_boil_hop_repository.get_by_id_or_raise.assert_called_once_with(inclusion_id)
        # Verify delete was called (the exact mock object may differ due to service implementation)
        self.mock_boil_hop_repository.delete.assert_called_once()

    def test_delete_dry_hop_inclusion_success(self):
        """Test successfully deleting a dry hop inclusion."""
        # Arrange
        inclusion_id = "inc_test123"
        mock_inclusion = self.create_mock_hop_inclusion('dry', id=inclusion_id)

        self.mock_dry_hop_repository.get_by_id_or_raise.return_value = mock_inclusion

        # Act
        result = self.service.delete_dry_hop_inclusion(inclusion_id)

        # Assert
        assert result is True
        self.mock_dry_hop_repository.get_by_id_or_raise.assert_called_once_with(inclusion_id)
        # Verify delete was called (the exact mock object may differ due to service implementation)
        self.mock_dry_hop_repository.delete.assert_called_once()

    def test_get_boil_hop_inclusion_by_id_found(self):
        """Test getting a boil hop inclusion by ID when found."""
        # Arrange
        inclusion_id = "inc_test123"
        mock_inclusion = self.create_mock_hop_inclusion('boil', id=inclusion_id)

        self.mock_boil_hop_repository.get_by_id.return_value = mock_inclusion

        # Act
        result = self.service.get_boil_hop_inclusion_by_id(inclusion_id)

        # Assert
        assert result == mock_inclusion
        self.mock_boil_hop_repository.get_by_id.assert_called_once_with(inclusion_id)

    def test_get_dry_hop_inclusion_by_id_not_found(self):
        """Test getting a dry hop inclusion by ID when not found."""
        # Arrange
        inclusion_id = "inc_nonexistent"

        self.mock_dry_hop_repository.get_by_id.return_value = None

        # Act
        result = self.service.get_dry_hop_inclusion_by_id(inclusion_id)

        # Assert
        assert result is None
        self.mock_dry_hop_repository.get_by_id.assert_called_once_with(inclusion_id)

    def test_hop_inclusion_methods_have_agent_accessible_decorators(self):
        """Test that hop inclusion methods have agent_accessible decorators."""
        inclusion_methods = [
            'add_boil_hop_to_recipe', 'add_first_wort_hop_to_recipe',
            'add_flameout_hop_to_recipe', 'add_whirlpool_hop_to_recipe',
            'add_dry_hop_to_recipe', 'update_boil_hop_inclusion',
            'update_first_wort_hop_inclusion', 'update_flameout_hop_inclusion',
            'update_whirlpool_hop_inclusion', 'update_dry_hop_inclusion',
            'delete_boil_hop_inclusion', 'delete_first_wort_hop_inclusion',
            'delete_flameout_hop_inclusion', 'delete_whirlpool_hop_inclusion',
            'delete_dry_hop_inclusion', 'get_boil_hop_inclusion_by_id',
            'get_first_wort_hop_inclusion_by_id', 'get_flameout_hop_inclusion_by_id',
            'get_whirlpool_hop_inclusion_by_id', 'get_dry_hop_inclusion_by_id'
        ]

        for method_name in inclusion_methods:
            method = getattr(self.service, method_name)
            assert hasattr(method, '_tool_metadata'), f"{method_name} should have agent_accessible decorator"
