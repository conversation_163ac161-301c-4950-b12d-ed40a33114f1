"""
Tests for RecipeService.

This module contains comprehensive tests for the RecipeService class,
testing all business logic with proper repository mocking.
"""

import pytest
from unittest.mock import Mock, patch

from core.services import RecipeService
from core.models import (
    Recipe, Fermentable, FermentableInclusion, BoilHopInclusion,
    FirstWortHopInclusion, FlameoutHopInclusion, WhirlpoolHopInclusion,
    DryHopInclusion, YeastInclusion
)
from core.repositories import (
    RecipeRepository, FermentableRepository, FermentableInclusionRepository,
    BoilHopInclusionRepository, FirstWortHopInclusionRepository,
    FlameoutHopInclusionRepository, WhirlpoolHopInclusionRepository,
    DryHopInclusionRepository, YeastInclusionRepository
)
from tests.base import BaseServiceTest


class TestRecipeService(BaseServiceTest):
    """Test RecipeService functionality."""

    service_class = RecipeService



    def test_service_initialization(self):
        """Test that service initializes with all repositories."""
        assert self.service.recipe_repository is not None
        assert self.service.fermentable_repository is not None
        assert self.service.fermentable_inclusion_repository is not None
        assert self.service.boil_hop_repository is not None
        assert self.service.first_wort_repository is not None
        assert self.service.flameout_repository is not None
        assert self.service.whirlpool_repository is not None
        assert self.service.dry_hop_repository is not None
        assert self.service.yeast_inclusion_repository is not None

    def test_get_recipe_by_id_found(self):
        """Test getting recipe by ID when found."""
        # Arrange
        recipe_id = "rec_test123"
        expected_recipe = Mock()
        self.service.recipe_repository.get_by_id.return_value = expected_recipe

        # Act
        result = self.service.get_recipe_by_id(recipe_id)

        # Assert
        assert result == expected_recipe
        self.service.recipe_repository.get_by_id.assert_called_once_with(recipe_id)

    def test_get_recipe_by_id_not_found(self):
        """Test getting recipe by ID when not found."""
        # Arrange
        recipe_id = "rec_nonexistent"
        self.service.recipe_repository.get_by_id.return_value = None

        # Act
        result = self.service.get_recipe_by_id(recipe_id)

        # Assert
        assert result is None
        self.service.recipe_repository.get_by_id.assert_called_once_with(recipe_id)

    def test_get_recipe_with_inclusions(self):
        """Test getting recipe with all inclusions prefetched."""
        # Arrange
        recipe_id = "rec_test123"
        expected_recipe = Mock()
        self.service.recipe_repository.get_with_all_inclusions.return_value = expected_recipe

        # Act
        result = self.service.get_recipe_with_inclusions(recipe_id)

        # Assert
        assert result == expected_recipe
        self.service.recipe_repository.get_with_all_inclusions.assert_called_once_with(recipe_id)

    def test_get_included_fermentables_by_id(self):
        """Test getting fermentable inclusions for a recipe."""
        # Arrange
        recipe_id = "rec_test123"
        expected_inclusions = [
            Mock(),
            Mock()
        ]
        self.service.fermentable_inclusion_repository.get_by_recipe_with_fermentables.return_value = expected_inclusions

        # Act
        result = self.service.get_included_fermentables_by_id(recipe_id)

        # Assert
        assert result == expected_inclusions
        assert len(result) == 2
        self.service.fermentable_inclusion_repository.get_by_recipe_with_fermentables.assert_called_once_with(recipe_id)

    def test_add_fermentable_to_recipe_success(self):
        """Test successfully adding a fermentable to a recipe."""
        # Arrange
        recipe_id = "rec_test123"
        fermentable_id = "fer_test123"
        quantity = 10.0
        quantity_unit = "POUNDS"
        efficiency_percent = 75.0

        mock_recipe = Mock()
        mock_fermentable = Mock()
        mock_inclusion = Mock()

        self.service.recipe_repository.get_by_id_or_raise.return_value = mock_recipe
        self.service.fermentable_repository.get_by_id_or_raise.return_value = mock_fermentable
        self.service.fermentable_inclusion_repository.create.return_value = mock_inclusion

        # Act
        result = self.service.add_fermentable_to_recipe(
            recipe_id, fermentable_id, quantity, quantity_unit, efficiency_percent
        )

        # Assert
        assert result is not None
        self.service.recipe_repository.get_by_id_or_raise.assert_called_once_with(recipe_id)
        self.service.fermentable_repository.get_by_id_or_raise.assert_called_once_with(fermentable_id)

        # Check that create was called once with the correct arguments
        self.service.fermentable_inclusion_repository.create.assert_called_once()
        call_args = self.service.fermentable_inclusion_repository.create.call_args
        # The recipe and fermentable should be the ones returned by the repositories
        assert call_args.kwargs['recipe'] is not None
        assert call_args.kwargs['fermentable'] is not None
        assert call_args.kwargs['quantity'] == quantity
        assert call_args.kwargs['quantity_unit'] == quantity_unit
        assert call_args.kwargs['efficiency_percent'] == efficiency_percent

    def test_add_fermentable_to_recipe_default_efficiency(self):
        """Test adding a fermentable to a recipe with default efficiency."""
        # Arrange
        recipe_id = "rec_test123"
        fermentable_id = "fer_test123"
        quantity = 10.0
        quantity_unit = "POUNDS"

        mock_recipe = Mock()
        mock_fermentable = Mock()
        mock_inclusion = Mock()

        self.service.recipe_repository.get_by_id_or_raise.return_value = mock_recipe
        self.service.fermentable_repository.get_by_id_or_raise.return_value = mock_fermentable
        self.service.fermentable_inclusion_repository.create.return_value = mock_inclusion

        # Act
        result = self.service.add_fermentable_to_recipe(
            recipe_id, fermentable_id, quantity, quantity_unit
        )

        # Assert
        assert result is not None
        self.service.recipe_repository.get_by_id_or_raise.assert_called_once_with(recipe_id)
        self.service.fermentable_repository.get_by_id_or_raise.assert_called_once_with(fermentable_id)

        # Check that create was called once with the correct arguments
        self.service.fermentable_inclusion_repository.create.assert_called_once()
        call_args = self.service.fermentable_inclusion_repository.create.call_args
        # The recipe and fermentable should be the ones returned by the repositories
        assert call_args.kwargs['recipe'] is not None
        assert call_args.kwargs['fermentable'] is not None
        assert call_args.kwargs['quantity'] == quantity
        assert call_args.kwargs['quantity_unit'] == quantity_unit
        # Should be called with None efficiency (no default set by service)
        assert call_args.kwargs['efficiency_percent'] is None

    def test_get_recipe_hop_inclusions(self):
        """Test getting all hop inclusions for a recipe."""
        # Arrange
        recipe_id = "rec_test123"

        boil_hops = [Mock()]
        first_wort_hops = [Mock()]
        flameout_hops = [Mock()]
        whirlpool_hops = [Mock()]
        dry_hops = [Mock()]

        self.service.boil_hop_repository.get_by_recipe_with_hops.return_value = boil_hops
        self.service.first_wort_repository.get_by_recipe_with_hops.return_value = first_wort_hops
        self.service.flameout_repository.get_by_recipe_with_hops.return_value = flameout_hops
        self.service.whirlpool_repository.get_by_recipe_with_hops.return_value = whirlpool_hops
        self.service.dry_hop_repository.get_by_recipe_with_hops.return_value = dry_hops

        # Act
        result = self.service.get_recipe_hop_inclusions(recipe_id)

        # Assert
        assert isinstance(result, dict)
        assert "boil_hops" in result
        assert "first_wort_hops" in result
        assert "flameout_hops" in result
        assert "whirlpool_hops" in result
        assert "dry_hops" in result

        assert result["boil_hops"] == boil_hops
        assert result["first_wort_hops"] == first_wort_hops
        assert result["flameout_hops"] == flameout_hops
        assert result["whirlpool_hops"] == whirlpool_hops
        assert result["dry_hops"] == dry_hops

        # Verify all repositories were called
        self.service.boil_hop_repository.get_by_recipe_with_hops.assert_called_once_with(recipe_id)
        self.service.first_wort_repository.get_by_recipe_with_hops.assert_called_once_with(recipe_id)
        self.service.flameout_repository.get_by_recipe_with_hops.assert_called_once_with(recipe_id)
        self.service.whirlpool_repository.get_by_recipe_with_hops.assert_called_once_with(recipe_id)
        self.service.dry_hop_repository.get_by_recipe_with_hops.assert_called_once_with(recipe_id)

    def test_get_recipe_yeast_inclusions(self):
        """Test getting yeast inclusions for a recipe."""
        # Arrange
        recipe_id = "rec_test123"
        expected_inclusions = [
            Mock(),
            Mock()
        ]
        self.service.yeast_inclusion_repository.get_by_recipe_with_yeast.return_value = expected_inclusions

        # Act
        result = self.service.get_recipe_yeast_inclusions(recipe_id)

        # Assert
        assert result == expected_inclusions
        assert len(result) == 2
        self.service.yeast_inclusion_repository.get_by_recipe_with_yeast.assert_called_once_with(recipe_id)

    def test_agent_accessible_decorators(self):
        """Test that methods have agent_accessible decorators."""
        # Check that key methods have the agent_accessible decorator
        assert hasattr(self.service.get_included_fermentables_by_id, '_tool_metadata')
        assert hasattr(self.service.add_fermentable_to_recipe, '_tool_metadata')
        assert hasattr(self.service.get_recipe_hop_inclusions, '_tool_metadata')
        assert hasattr(self.service.get_recipe_yeast_inclusions, '_tool_metadata')

    def test_service_has_correct_methods(self):
        """Test that service has the expected methods."""
        # Test that the service has the methods we expect
        expected_methods = [
            'get_recipe_by_id', 'get_recipe_with_inclusions',
            'get_included_fermentables_by_id', 'add_fermentable_to_recipe',
            'get_recipe_hop_inclusions', 'get_recipe_yeast_inclusions'
        ]

        for method_name in expected_methods:
            assert hasattr(self.service, method_name)
            assert callable(getattr(self.service, method_name))

    def test_service_repository_attributes(self):
        """Test that service has all expected repository attributes."""
        expected_repositories = [
            'recipe_repository', 'fermentable_repository', 'fermentable_inclusion_repository',
            'boil_hop_repository', 'first_wort_repository', 'flameout_repository',
            'whirlpool_repository', 'dry_hop_repository', 'yeast_inclusion_repository'
        ]

        for repo_name in expected_repositories:
            assert hasattr(self.service, repo_name)
            assert getattr(self.service, repo_name) is not None
