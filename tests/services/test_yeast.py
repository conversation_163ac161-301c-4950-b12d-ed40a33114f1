"""
Tests for YeastService.

This module contains comprehensive tests for the YeastService class,
testing all business logic with proper repository mocking.
"""

import pytest
from unittest.mock import Mock, patch

from core.services import YeastService
from core.models import Yeast, YeastInclusion, YeastType, YeastForm
from core.repositories.yeast import YeastRepository, YeastInclusionRepository
from tests.base import BaseServiceTest


class TestYeastService(BaseServiceTest):
    """Test YeastService functionality."""

    service_class = YeastService

    def test_service_initialization(self):
        """Test that service initializes with repositories."""
        assert self.service.repository is not None
        assert self.service.inclusion_repository is not None

    def test_get_yeast_by_id_found(self):
        """Test getting yeast by ID when found."""
        # Arrange
        yeast_id = "yst_test123"
        expected_yeast = Mock()
        self.service.repository.get_by_id.return_value = expected_yeast

        # Act
        result = self.service.get_yeast_by_id(yeast_id)

        # Assert
        assert result == expected_yeast
        self.service.repository.get_by_id.assert_called_once_with(yeast_id)

    def test_get_yeast_by_id_not_found(self):
        """Test getting yeast by ID when not found."""
        # Arrange
        yeast_id = "yst_nonexistent"
        self.service.repository.get_by_id.return_value = None

        # Act
        result = self.service.get_yeast_by_id(yeast_id)

        # Assert
        assert result is None
        self.service.repository.get_by_id.assert_called_once_with(yeast_id)

    def test_get_all_yeasts(self):
        """Test getting all yeasts."""
        # Arrange
        expected_yeasts = [
            Mock(),
            Mock(),
            Mock()
        ]
        self.service.repository.get_all.return_value = expected_yeasts

        # Act
        result = self.service.get_all_yeasts()

        # Assert
        assert result == expected_yeasts
        assert len(result) == 3
        self.service.repository.get_all.assert_called_once()

    def test_search_yeasts_by_name(self):
        """Test searching yeasts by name."""
        # Arrange
        search_query = "safale"
        expected_yeasts = [
            Mock(),
            Mock()
        ]
        self.service.repository.search_by_name.return_value = expected_yeasts

        # Act
        result = self.service.search_yeasts(search_query)

        # Assert
        assert result == expected_yeasts
        assert len(result) == 2
        self.service.repository.search_by_name.assert_called_once_with(search_query)

    def test_get_ale_yeasts(self):
        """Test getting ale yeasts."""
        # Arrange
        expected_yeasts = [
            Mock(),
            Mock()
        ]
        self.service.repository.get_ale_yeasts.return_value = expected_yeasts

        # Act
        result = self.service.get_ale_yeasts()

        # Assert
        assert result == expected_yeasts
        assert len(result) == 2
        self.service.repository.get_ale_yeasts.assert_called_once()

    def test_get_lager_yeasts(self):
        """Test getting lager yeasts."""
        # Arrange
        expected_yeasts = [
            Mock(),
            Mock()
        ]
        self.service.repository.get_lager_yeasts.return_value = expected_yeasts

        # Act
        result = self.service.get_lager_yeasts()

        # Assert
        assert result == expected_yeasts
        assert len(result) == 2
        self.service.repository.get_lager_yeasts.assert_called_once()

    def test_get_yeasts_by_laboratory(self):
        """Test getting yeasts by laboratory."""
        # Arrange
        laboratory = "Wyeast"
        expected_yeasts = [
            Mock(),
            Mock()
        ]
        self.service.repository.get_by_laboratory.return_value = expected_yeasts

        # Act
        result = self.service.get_yeasts_by_laboratory(laboratory)

        # Assert
        assert result == expected_yeasts
        assert len(result) == 2
        self.service.repository.get_by_laboratory.assert_called_once_with(laboratory)

    def test_get_yeasts_by_temperature(self):
        """Test getting yeasts by temperature range."""
        # Arrange
        min_temp = 60.0
        max_temp = 75.0
        expected_yeasts = [
            Mock()
        ]
        self.service.repository.get_by_temperature_range.return_value = expected_yeasts

        # Act
        result = self.service.get_yeasts_by_temperature(min_temp, max_temp)

        # Assert
        assert result == expected_yeasts
        self.service.repository.get_by_temperature_range.assert_called_once_with(min_temp, max_temp)

    def test_get_high_attenuation_yeasts(self):
        """Test getting high attenuation yeasts."""
        # Arrange
        min_attenuation = 80.0
        expected_yeasts = [
            Mock()
        ]
        self.service.repository.get_high_attenuation_yeasts.return_value = expected_yeasts

        # Act
        result = self.service.get_high_attenuation_yeasts(min_attenuation)

        # Assert
        assert result == expected_yeasts
        self.service.repository.get_high_attenuation_yeasts.assert_called_once_with(min_attenuation)

    def test_get_high_attenuation_yeasts_default(self):
        """Test getting high attenuation yeasts with default minimum."""
        # Arrange
        expected_yeasts = [
            Mock()
        ]
        self.service.repository.get_high_attenuation_yeasts.return_value = expected_yeasts

        # Act
        result = self.service.get_high_attenuation_yeasts()

        # Assert
        assert result == expected_yeasts
        self.service.repository.get_high_attenuation_yeasts.assert_called_once_with(80.0)

    def test_get_yeasts_by_attenuation_range(self):
        """Test getting yeasts by attenuation range."""
        # Arrange
        min_attenuation = 70.0
        max_attenuation = 80.0
        expected_yeasts = [
            Mock()
        ]
        self.service.repository.get_by_attenuation_range.return_value = expected_yeasts

        # Act
        result = self.service.get_yeasts_by_attenuation_range(min_attenuation, max_attenuation)

        # Assert
        assert result == expected_yeasts
        self.service.repository.get_by_attenuation_range.assert_called_once_with(min_attenuation, max_attenuation)

    def test_describe_yeast_found(self):
        """Test describing a yeast when found."""
        # Arrange
        yeast_id = "yst_test123"
        mock_yeast = Mock()
        mock_yeast.name = "Test Yeast"
        mock_yeast.laboratory = "Test Labs"
        mock_yeast.product_id = "T001"
        mock_yeast.get_yeast_type_display.return_value = "Ale"
        mock_yeast.get_yeast_form_display.return_value = "Liquid"
        mock_yeast.temperature_range_display = "65-75°F"
        mock_yeast.attenuation_percent = 75.0
        mock_yeast.get_flocculation_display.return_value = "Medium"
        mock_yeast.alcohol_tolerance_percent = 12.0
        mock_yeast.description = "A versatile ale yeast"

        self.service.repository.get_by_id.return_value = mock_yeast

        # Act
        result = self.service.describe_yeast(yeast_id)

        # Assert
        assert "Test Yeast" in result
        assert "Test Labs" in result
        assert "T001" in result
        self.service.repository.get_by_id.assert_called_once_with(yeast_id)

    def test_describe_yeast_not_found(self):
        """Test describing a yeast when not found."""
        # Arrange
        yeast_id = "yst_nonexistent"
        self.service.repository.get_by_id.return_value = None

        # Act
        result = self.service.describe_yeast(yeast_id)

        # Assert
        assert f"Yeast with ID {yeast_id} not found." == result
        self.service.repository.get_by_id.assert_called_once_with(yeast_id)

    def test_get_recipe_yeasts(self):
        """Test getting yeast inclusions for a recipe."""
        # Arrange
        recipe_id = "rec_test123"
        expected_inclusions = [
            Mock(),
            Mock()
        ]
        self.service.inclusion_repository.get_by_recipe_with_yeast.return_value = expected_inclusions

        # Act
        result = self.service.get_recipe_yeasts(recipe_id)

        # Assert
        assert result == expected_inclusions
        assert len(result) == 2
        self.service.inclusion_repository.get_by_recipe_with_yeast.assert_called_once_with(recipe_id)

    def test_agent_accessible_decorators(self):
        """Test that methods have agent_accessible decorators."""
        # Check that key methods have the agent_accessible decorator
        assert hasattr(self.service.get_yeast_by_id, '_tool_metadata')
        assert hasattr(self.service.get_all_yeasts, '_tool_metadata')
        assert hasattr(self.service.search_yeasts, '_tool_metadata')
        assert hasattr(self.service.get_ale_yeasts, '_tool_metadata')
        assert hasattr(self.service.get_lager_yeasts, '_tool_metadata')
        assert hasattr(self.service.get_yeasts_by_laboratory, '_tool_metadata')
        assert hasattr(self.service.get_yeasts_by_temperature, '_tool_metadata')
        assert hasattr(self.service.get_high_attenuation_yeasts, '_tool_metadata')
        assert hasattr(self.service.get_yeasts_by_attenuation_range, '_tool_metadata')
        assert hasattr(self.service.describe_yeast, '_tool_metadata')
        assert hasattr(self.service.get_recipe_yeasts, '_tool_metadata')

    def test_service_has_correct_methods(self):
        """Test that service has the expected methods."""
        # Test that the service has the methods we expect
        expected_methods = [
            'get_yeast_by_id', 'get_all_yeasts', 'search_yeasts',
            'get_ale_yeasts', 'get_lager_yeasts', 'get_yeasts_by_laboratory',
            'get_yeasts_by_temperature', 'get_high_attenuation_yeasts',
            'get_yeasts_by_attenuation_range', 'describe_yeast', 'get_recipe_yeasts'
        ]

        for method_name in expected_methods:
            assert hasattr(self.service, method_name)
            assert callable(getattr(self.service, method_name))
