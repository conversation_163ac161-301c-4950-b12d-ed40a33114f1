"""
Tests for API views.

This module contains tests for Django Rest Framework API views for chat
and recipe functionality.
"""

import json
import pytest
from unittest.mock import patch, <PERSON><PERSON>, MagicMock
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status

from core.views.api import (
    RecipeDetailAPIView, ChatStreamAPIView, RecipeRefreshAPIView,
    get_master_brewer_agent
)
from core.models import Recipe
from core.serializers import RecipeSerializer, ChatMessageSerializer
from tests.factories import UserFactory, RecipeFactory

User = get_user_model()


class TestRecipeDetailAPIView(APITestCase):
    """Test the RecipeDetailAPIView class."""

    def setUp(self):
        """Set up test case."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)

        self.recipe = Recipe.objects.create(
            user=self.user,
            name='API Test Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

    def get_url(self):
        """Return the API recipe detail URL."""
        return reverse('api_recipe_detail', kwargs={'pk': self.recipe.pk})

    def test_get_recipe_detail_success(self):
        """Test successful recipe detail retrieval."""
        response = self.client.get(self.get_url())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], str(self.recipe.pk))
        self.assertEqual(response.data['name'], 'API Test Recipe')

    def test_get_recipe_detail_requires_authentication(self):
        """Test that recipe detail requires authentication."""
        self.client.force_authenticate(user=None)
        response = self.client.get(self.get_url())

        # DRF may return 401 or 403 depending on authentication backend
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])

    def test_get_recipe_detail_only_own_recipes(self):
        """Test that users can only access their own recipes."""
        # Create another user and their recipe
        other_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        other_recipe = Recipe.objects.create(
            user=other_user,
            name='Other Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

        # Try to access other user's recipe
        url = reverse('api_recipe_detail', kwargs={'pk': other_recipe.pk})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_get_nonexistent_recipe(self):
        """Test getting non-existent recipe returns 404."""
        url = reverse('api_recipe_detail', kwargs={'pk': 'nonexistent'})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_serializer_class_used(self):
        """Test that correct serializer class is used."""
        view = RecipeDetailAPIView()
        self.assertEqual(view.serializer_class, RecipeSerializer)


class TestChatStreamAPIView(APITestCase):
    """Test the ChatStreamAPIView class."""

    def setUp(self):
        """Set up test case."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)

        self.recipe = Recipe.objects.create(
            user=self.user,
            name='Chat Test Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

    def get_url(self):
        """Return the chat stream API URL."""
        return reverse('api_chat_stream', kwargs={'recipe_pk': self.recipe.pk})

    @patch('core.views.api.get_master_brewer_agent')
    def test_post_chat_message_success(self, mock_get_agent):
        """Test successful chat message processing."""
        # Mock agent and events
        mock_agent = Mock()
        mock_event = Mock(spec=['display'])  # Only allow display method
        mock_event.__class__.__name__ = 'AIMessageEvent'
        mock_event.display.return_value = 'Test response'
        mock_agent.run.return_value = [mock_event]
        mock_get_agent.return_value = mock_agent

        data = {'message': 'Test message'}
        response = self.client.post(self.get_url(), data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertEqual(len(response.data['events']), 1)
        self.assertEqual(response.data['events'][0]['content'], 'Test response')

    def test_post_chat_message_requires_authentication(self):
        """Test that chat endpoint requires authentication."""
        self.client.force_authenticate(user=None)
        data = {'message': 'Test message'}
        response = self.client.post(self.get_url(), data, format='json')

        # DRF may return 401 or 403 depending on authentication backend
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])

    def test_post_chat_message_invalid_data(self):
        """Test chat endpoint with invalid data."""
        data = {'invalid_field': 'Test message'}
        response = self.client.post(self.get_url(), data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_post_chat_message_empty_message(self):
        """Test chat endpoint with empty message."""
        data = {'message': ''}
        response = self.client.post(self.get_url(), data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_post_chat_message_only_own_recipes(self):
        """Test that users can only chat with their own recipes."""
        # Create another user and their recipe
        other_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        other_recipe = Recipe.objects.create(
            user=other_user,
            name='Other Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

        # Try to chat with other user's recipe
        url = reverse('api_chat_stream', kwargs={'recipe_pk': other_recipe.pk})
        data = {'message': 'Test message'}
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    @patch('core.views.api.get_master_brewer_agent')
    def test_post_chat_message_with_tool_calls(self, mock_get_agent):
        """Test chat message processing with tool calls."""
        # Mock agent and events with tool calls
        mock_agent = Mock()
        mock_event = Mock()
        mock_event.__class__.__name__ = 'ToolCallEvent'
        mock_event.display.return_value = 'Tool call event'
        mock_event.tool_calls = [
            Mock(id='call_1', tool_name='test_tool', args={'arg1': 'value1'})
        ]
        mock_agent.run.return_value = [mock_event]
        mock_get_agent.return_value = mock_agent

        data = {'message': 'Test message with tools'}
        response = self.client.post(self.get_url(), data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertEqual(len(response.data['events']), 1)
        self.assertIn('tool_calls', response.data['events'][0])

    @patch('core.views.api.get_master_brewer_agent')
    def test_post_chat_message_with_tool_outputs(self, mock_get_agent):
        """Test chat message processing with tool outputs."""
        # Mock agent and events with tool outputs
        mock_agent = Mock()
        mock_event = Mock(spec=['display', 'tool_outputs'])
        mock_event.__class__.__name__ = 'ToolOutputEvent'
        mock_event.display.return_value = 'Tool output event'
        mock_event.tool_outputs = [
            Mock(id='output_1', output='Tool result')
        ]
        mock_agent.run.return_value = [mock_event]
        mock_get_agent.return_value = mock_agent

        data = {'message': 'Test message with outputs'}
        response = self.client.post(self.get_url(), data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertEqual(len(response.data['events']), 1)
        self.assertIn('tool_outputs', response.data['events'][0])

    @patch('core.views.api.get_master_brewer_agent')
    def test_post_chat_message_agent_error(self, mock_get_agent):
        """Test chat message processing when agent raises error."""
        # Mock agent to raise exception
        mock_agent = Mock()
        mock_agent.run.side_effect = Exception('Agent error')
        mock_get_agent.return_value = mock_agent

        data = {'message': 'Test message'}
        response = self.client.post(self.get_url(), data, format='json')

        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.data['status'], 'error')
        self.assertIn('error', response.data)

    def test_get_method_not_allowed(self):
        """Test that GET method is not allowed."""
        response = self.client.get(self.get_url())
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)


class TestRecipeRefreshAPIView(APITestCase):
    """Test the RecipeRefreshAPIView class."""

    def setUp(self):
        """Set up test case."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)

        self.recipe = Recipe.objects.create(
            user=self.user,
            name='Refresh Test Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

    def get_url(self):
        """Return the recipe refresh API URL."""
        return reverse('api_recipe_refresh', kwargs={'pk': self.recipe.pk})

    def test_get_recipe_refresh_success(self):
        """Test successful recipe refresh."""
        response = self.client.get(self.get_url())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], str(self.recipe.pk))
        self.assertEqual(response.data['name'], 'Refresh Test Recipe')

    def test_get_recipe_refresh_requires_authentication(self):
        """Test that recipe refresh requires authentication."""
        self.client.force_authenticate(user=None)
        response = self.client.get(self.get_url())

        # DRF may return 401 or 403 depending on authentication backend
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])

    def test_get_recipe_refresh_only_own_recipes(self):
        """Test that users can only refresh their own recipes."""
        # Create another user and their recipe
        other_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        other_recipe = Recipe.objects.create(
            user=other_user,
            name='Other Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

        # Try to refresh other user's recipe
        url = reverse('api_recipe_refresh', kwargs={'pk': other_recipe.pk})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_get_nonexistent_recipe_refresh(self):
        """Test refreshing non-existent recipe returns 404."""
        url = reverse('api_recipe_refresh', kwargs={'pk': 'nonexistent'})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_serializer_class_used(self):
        """Test that correct serializer class is used."""
        view = RecipeRefreshAPIView()
        self.assertEqual(view.serializer_class, RecipeSerializer)


class TestGetMasterBrewerAgent(TestCase):
    """Test the get_master_brewer_agent function."""

    def setUp(self):
        """Set up test case."""
        # Reset global agent instance
        import core.views.api
        core.views.api._master_brewer_agent = None

    @patch('core.views.api.MasterBrewerAgent.create')
    def test_creates_agent_on_first_call(self, mock_create):
        """Test that agent is created on first call."""
        mock_agent = Mock()
        mock_create.return_value = mock_agent

        result = get_master_brewer_agent()

        self.assertEqual(result, mock_agent)
        mock_create.assert_called_once()

    @patch('core.views.api.MasterBrewerAgent.create')
    def test_reuses_agent_on_subsequent_calls(self, mock_create):
        """Test that agent is reused on subsequent calls."""
        mock_agent = Mock()
        mock_create.return_value = mock_agent

        # First call
        result1 = get_master_brewer_agent()
        # Second call
        result2 = get_master_brewer_agent()

        self.assertEqual(result1, mock_agent)
        self.assertEqual(result2, mock_agent)
        self.assertEqual(result1, result2)
        # Should only create once
        mock_create.assert_called_once()


class TestAPIViewIntegration(APITestCase):
    """Integration tests for API views."""

    def setUp(self):
        """Set up test case."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)

        self.recipe = Recipe.objects.create(
            user=self.user,
            name='Integration Test Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

    def test_recipe_detail_and_refresh_consistency(self):
        """Test that recipe detail and refresh return consistent data."""
        detail_url = reverse('api_recipe_detail', kwargs={'pk': self.recipe.pk})
        refresh_url = reverse('api_recipe_refresh', kwargs={'pk': self.recipe.pk})

        detail_response = self.client.get(detail_url)
        refresh_response = self.client.get(refresh_url)

        self.assertEqual(detail_response.status_code, status.HTTP_200_OK)
        self.assertEqual(refresh_response.status_code, status.HTTP_200_OK)

        # Both should return the same recipe data
        self.assertEqual(detail_response.data['id'], refresh_response.data['id'])
        self.assertEqual(detail_response.data['name'], refresh_response.data['name'])

    @patch('core.views.api.get_master_brewer_agent')
    def test_chat_and_refresh_workflow(self, mock_get_agent):
        """Test chat message followed by recipe refresh."""
        # Mock agent
        mock_agent = Mock()
        mock_event = Mock(spec=['display'])
        mock_event.__class__.__name__ = 'AIMessageEvent'
        mock_event.display.return_value = 'Recipe updated'
        mock_agent.run.return_value = [mock_event]
        mock_get_agent.return_value = mock_agent

        # Send chat message
        chat_url = reverse('api_chat_stream', kwargs={'recipe_pk': self.recipe.pk})
        chat_data = {'message': 'Update the recipe'}
        chat_response = self.client.post(chat_url, chat_data, format='json')

        self.assertEqual(chat_response.status_code, status.HTTP_200_OK)

        # Refresh recipe data
        refresh_url = reverse('api_recipe_refresh', kwargs={'pk': self.recipe.pk})
        refresh_response = self.client.get(refresh_url)

        self.assertEqual(refresh_response.status_code, status.HTTP_200_OK)
        self.assertEqual(refresh_response.data['id'], str(self.recipe.pk))
