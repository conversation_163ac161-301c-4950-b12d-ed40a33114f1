"""
Tests for authentication views.

This module contains tests for user authentication views including login,
logout, and registration functionality.
"""

import pytest
from unittest.mock import patch, Mock
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.contrib.messages import get_messages

from core.views.auth import CustomLoginView, CustomLogoutView, RegisterView
from tests.base import BaseViewTest
from tests.factories import UserFactory

User = get_user_model()


class TestCustomLoginView(BaseViewTest):
    """Test the CustomLoginView class."""

    def get_url(self):
        """Return the login URL."""
        return reverse('login')

    def test_view_requires_authentication(self):
        """Override - login page should be accessible to anonymous users."""
        self.client.logout()
        response = self.client.get(self.get_url())
        self.assertEqual(response.status_code, 200)

    def test_view_uses_correct_template(self):
        """Test that login view uses correct template."""
        self.client.logout()
        response = self.client.get(self.get_url())
        self.assertTemplateUsed(response, 'auth/login.html')

    def test_view_context_contains_page_title(self):
        """Test that login view context contains page title."""
        self.client.logout()
        response = self.client.get(self.get_url())
        self.assertEqual(response.context['page_title'], 'Login')

    def test_redirects_authenticated_user(self):
        """Test that authenticated users are redirected."""
        response = self.client.get(self.get_url())
        # Should redirect authenticated users
        self.assertEqual(response.status_code, 302)

    def test_successful_login_shows_welcome_message(self):
        """Test that successful login shows welcome message."""
        self.client.logout()

        # Create a user for login
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test'
        )

        response = self.client.post(self.get_url(), {
            'username': '<EMAIL>',  # Django auth uses 'username' field
            'password': 'testpass123'
        })

        # Should redirect after successful login
        self.assertEqual(response.status_code, 302)

        # Check for success message
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('Welcome back' in str(m) for m in messages))

    def test_invalid_login_shows_form_errors(self):
        """Test that invalid login shows form errors."""
        self.client.logout()

        response = self.client.post(self.get_url(), {
            'username': '<EMAIL>',
            'password': 'wrongpassword'
        })

        # Should stay on login page with errors
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Please enter a correct')

    def test_view_class_attributes(self):
        """Test that view has correct class attributes."""
        view = CustomLoginView()
        self.assertEqual(view.template_name, 'auth/login.html')
        self.assertTrue(view.redirect_authenticated_user)


class TestCustomLogoutView(BaseViewTest):
    """Test the CustomLogoutView class."""

    def get_url(self):
        """Return the logout URL."""
        return reverse('logout')

    def test_view_requires_authentication(self):
        """Override - logout should work for both authenticated and anonymous users."""
        self.client.logout()
        response = self.client.post(self.get_url())
        self.assertEqual(response.status_code, 302)

    def test_logout_shows_success_message(self):
        """Test that logout shows success message."""
        response = self.client.post(self.get_url())

        # Check for success message
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('successfully logged out' in str(m) for m in messages))

    def test_logout_redirects_to_login(self):
        """Test that logout redirects appropriately."""
        response = self.client.post(self.get_url())
        # Should redirect after logout
        self.assertEqual(response.status_code, 302)

    def test_logout_when_not_authenticated(self):
        """Test logout when user is not authenticated."""
        self.client.logout()
        response = self.client.post(self.get_url())

        # Should still work but no success message
        self.assertEqual(response.status_code, 302)


class TestRegisterView(BaseViewTest):
    """Test the RegisterView class."""

    def get_url(self):
        """Return the register URL."""
        return reverse('register')

    def test_view_requires_authentication(self):
        """Override - registration doesn't require authentication."""
        # Registration should be accessible to anonymous users
        self.client.logout()
        response = self.client.get(self.get_url())
        self.assertEqual(response.status_code, 200)

    def test_view_uses_correct_template(self):
        """Test that register view uses correct template."""
        self.client.logout()
        response = self.client.get(self.get_url())
        self.assertTemplateUsed(response, 'auth/register.html')

    def test_view_context_contains_page_title(self):
        """Test that register view context contains page title."""
        self.client.logout()
        response = self.client.get(self.get_url())
        self.assertEqual(response.context['page_title'], 'Register')

    def test_successful_registration_creates_user_and_logs_in(self):
        """Test that successful registration creates user and logs them in."""
        self.client.logout()

        user_data = {
            'email': '<EMAIL>',
            'first_name': 'New',
            'last_name': 'User',
            'password': 'newpass123'
        }

        response = self.client.post(self.get_url(), user_data)

        # Should redirect after successful registration
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, reverse('home'))

        # User should be created
        user = User.objects.get(email='<EMAIL>')
        self.assertEqual(user.first_name, 'New')
        self.assertEqual(user.last_name, 'User')

        # Password should be hashed
        self.assertTrue(user.check_password('newpass123'))

        # Check for welcome message
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('Welcome to Hoplogic' in str(m) for m in messages))

    def test_registration_with_invalid_data_shows_errors(self):
        """Test that registration with invalid data shows errors."""
        self.client.logout()

        # Missing required fields
        response = self.client.post(self.get_url(), {
            'email': '',
            'password': 'short'
        })

        # Should stay on registration page with errors
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.context['form'].errors)

    def test_registration_with_existing_email_shows_error(self):
        """Test that registration with existing email shows error."""
        self.client.logout()

        # Create existing user
        User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

        # Try to register with same email
        response = self.client.post(self.get_url(), {
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'newpass123'
        })

        # Should stay on registration page with errors
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.context['form'].errors)

    def test_view_class_attributes(self):
        """Test that view has correct class attributes."""
        view = RegisterView()
        self.assertEqual(view.model, User)
        self.assertEqual(view.template_name, 'auth/register.html')
        self.assertEqual(view.fields, ['email', 'first_name', 'last_name', 'password'])
        self.assertEqual(view.success_url, reverse('home'))


class TestAuthViewIntegration(TestCase):
    """Integration tests for authentication views."""

    def setUp(self):
        """Set up test case."""
        self.client = Client()

    def test_login_logout_flow(self):
        """Test complete login/logout flow."""
        # Create user
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Flow'
        )

        # Login
        response = self.client.post(reverse('login'), {
            'username': '<EMAIL>',
            'password': 'testpass123'
        })
        self.assertEqual(response.status_code, 302)

        # Verify logged in
        response = self.client.get(reverse('home'))
        self.assertEqual(response.status_code, 200)

        # Logout
        response = self.client.post(reverse('logout'))
        self.assertEqual(response.status_code, 302)

        # Verify logged out
        response = self.client.get(reverse('home'))
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_registration_login_flow(self):
        """Test complete registration and automatic login flow."""
        # Register
        response = self.client.post(reverse('register'), {
            'email': '<EMAIL>',
            'first_name': 'Register',
            'last_name': 'User',
            'password': 'newpass123'
        })
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, reverse('home'))

        # Should be automatically logged in
        response = self.client.get(reverse('home'))
        self.assertEqual(response.status_code, 200)

        # User should exist
        user = User.objects.get(email='<EMAIL>')
        self.assertEqual(user.first_name, 'Register')
