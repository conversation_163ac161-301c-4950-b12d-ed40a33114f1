"""
Tests for recipe views.

This module contains tests for recipe-related views including home page,
recipe collaboration, and recipe creation functionality.
"""

import pytest
from unittest.mock import patch, Mock
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.http import Http404

from core.views.recipe import HomeView, RecipeCollaborationView, CreateRecipeView
from core.models import Recipe
from tests.base import BaseViewTest
from tests.factories import UserFactory, RecipeFactory

User = get_user_model()


class TestHomeView(BaseViewTest):
    """Test the HomeView class."""

    def get_url(self):
        """Return the home URL."""
        return reverse('home')

    def test_view_uses_correct_template(self):
        """Test that home view uses correct template."""
        response = self.client.get(self.get_url())
        self.assertTemplateUsed(response, 'home.html')

    def test_view_context_contains_page_title(self):
        """Test that home view context contains page title."""
        response = self.client.get(self.get_url())
        self.assertEqual(response.context['page_title'], 'Recipe Collection')

    def test_view_shows_only_user_recipes(self):
        """Test that view shows only recipes owned by current user."""
        # Create another user and their recipe
        other_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        other_recipe = Recipe.objects.create(
            user=other_user,
            name='Other Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

        # Create recipe for current user
        user_recipe = Recipe.objects.create(
            user=self.user,
            name='My Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

        response = self.client.get(self.get_url())

        # Should only show user's recipe
        recipes = response.context['recipes']
        self.assertEqual(len(recipes), 1)
        self.assertEqual(recipes[0].id, user_recipe.id)
        self.assertNotIn(other_recipe, recipes)

    def test_view_orders_recipes_by_created_at_desc(self):
        """Test that recipes are ordered by creation date descending."""
        # Create multiple recipes with different creation times
        recipe1 = Recipe.objects.create(
            user=self.user,
            name='First Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )
        recipe2 = Recipe.objects.create(
            user=self.user,
            name='Second Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

        response = self.client.get(self.get_url())
        recipes = response.context['recipes']

        # Should be ordered newest first (by name since they're created quickly)
        recipe_names = [recipe.name for recipe in recipes]
        self.assertIn('First Recipe', recipe_names)
        self.assertIn('Second Recipe', recipe_names)
        self.assertEqual(len(recipes), 2)

    def test_view_with_no_recipes(self):
        """Test view when user has no recipes."""
        response = self.client.get(self.get_url())

        recipes = response.context['recipes']
        self.assertEqual(len(recipes), 0)

    def test_view_class_attributes(self):
        """Test that view has correct class attributes."""
        view = HomeView()
        self.assertEqual(view.model, Recipe)
        self.assertEqual(view.template_name, 'home.html')
        self.assertEqual(view.context_object_name, 'recipes')
        self.assertEqual(view.ordering, ['-created_at'])


class TestRecipeCollaborationView(BaseViewTest):
    """Test the RecipeCollaborationView class."""

    def setUp(self):
        """Set up test case."""
        super().setUp()
        self.recipe = Recipe.objects.create(
            user=self.user,
            name='Test Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

    def get_url(self):
        """Return the recipe collaboration URL."""
        return reverse('recipe_collaboration', kwargs={'pk': self.recipe.pk})

    def test_view_uses_correct_template(self):
        """Test that collaboration view uses correct template."""
        response = self.client.get(self.get_url())
        self.assertTemplateUsed(response, 'recipe_collaboration.html')

    def test_view_context_contains_recipe_and_page_title(self):
        """Test that view context contains recipe and page title."""
        response = self.client.get(self.get_url())

        self.assertEqual(response.context['recipe'], self.recipe)
        self.assertEqual(response.context['page_title'], f'Collaborating on {self.recipe.name}')

    def test_view_only_shows_user_owned_recipe(self):
        """Test that view only shows recipes owned by current user."""
        # Create another user and their recipe
        other_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        other_recipe = Recipe.objects.create(
            user=other_user,
            name='Other Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

        # Try to access other user's recipe
        url = reverse('recipe_collaboration', kwargs={'pk': other_recipe.pk})
        response = self.client.get(url)

        # Should return 404
        self.assertEqual(response.status_code, 404)

    def test_view_with_nonexistent_recipe(self):
        """Test view with non-existent recipe ID."""
        url = reverse('recipe_collaboration', kwargs={'pk': 'nonexistent'})
        response = self.client.get(url)

        # Should return 404
        self.assertEqual(response.status_code, 404)

    def test_view_class_attributes(self):
        """Test that view has correct class attributes."""
        view = RecipeCollaborationView()
        self.assertEqual(view.model, Recipe)
        self.assertEqual(view.template_name, 'recipe_collaboration.html')
        self.assertEqual(view.context_object_name, 'recipe')


class TestCreateRecipeView(BaseViewTest):
    """Test the CreateRecipeView class."""

    def get_url(self):
        """Return the create recipe URL."""
        return reverse('create_recipe')

    def test_view_only_accepts_post_requests(self):
        """Test that view only accepts POST requests."""
        # GET should not be allowed
        response = self.client.get(self.get_url())
        self.assertEqual(response.status_code, 405)  # Method not allowed

    def test_create_recipe_creates_new_recipe_and_redirects(self):
        """Test that POST creates new recipe and redirects to collaboration page."""
        initial_count = Recipe.objects.count()

        response = self.client.post(self.get_url())

        # Should redirect
        self.assertEqual(response.status_code, 302)

        # Should create new recipe
        self.assertEqual(Recipe.objects.count(), initial_count + 1)

        # Get the created recipe
        new_recipe = Recipe.objects.latest('created_at')

        # Should redirect to collaboration page
        expected_url = reverse('recipe_collaboration', kwargs={'pk': new_recipe.pk})
        self.assertEqual(response.url, expected_url)

    def test_created_recipe_has_correct_defaults(self):
        """Test that created recipe has correct default values."""
        response = self.client.post(self.get_url())

        # Get the created recipe
        new_recipe = Recipe.objects.latest('created_at')

        # Check default values
        self.assertEqual(new_recipe.user, self.user)
        self.assertEqual(new_recipe.name, 'New Recipe')
        self.assertEqual(new_recipe.description, '')
        self.assertEqual(new_recipe.batch_size_gallons, 5.0)
        self.assertEqual(new_recipe.mash_efficiency_percent, 75.0)
        self.assertEqual(new_recipe.target_original_gravity, 1.050)

    def test_multiple_recipe_creation(self):
        """Test creating multiple recipes."""
        initial_count = Recipe.objects.count()

        # Create first recipe
        response1 = self.client.post(self.get_url())
        self.assertEqual(response1.status_code, 302)

        # Create second recipe
        response2 = self.client.post(self.get_url())
        self.assertEqual(response2.status_code, 302)

        # Should have created 2 recipes
        self.assertEqual(Recipe.objects.count(), initial_count + 2)

        # Both should belong to the user
        user_recipes = Recipe.objects.filter(user=self.user)
        self.assertEqual(user_recipes.count(), initial_count + 2)


class TestRecipeViewIntegration(TestCase):
    """Integration tests for recipe views."""

    def setUp(self):
        """Set up test case."""
        self.client = Client()
        User = get_user_model()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_login(self.user)

    def test_complete_recipe_workflow(self):
        """Test complete recipe creation and collaboration workflow."""
        # Start at home page
        response = self.client.get(reverse('home'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['recipes']), 0)

        # Create new recipe
        response = self.client.post(reverse('create_recipe'))
        self.assertEqual(response.status_code, 302)

        # Should redirect to collaboration page
        new_recipe = Recipe.objects.latest('created_at')
        expected_url = reverse('recipe_collaboration', kwargs={'pk': new_recipe.pk})
        self.assertEqual(response.url, expected_url)

        # Visit collaboration page
        response = self.client.get(expected_url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['recipe'], new_recipe)

        # Go back to home page
        response = self.client.get(reverse('home'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['recipes']), 1)
        self.assertEqual(response.context['recipes'][0], new_recipe)

    def test_recipe_ownership_security(self):
        """Test that users can only access their own recipes."""
        # Create another user and their recipe
        other_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        other_recipe = Recipe.objects.create(
            user=other_user,
            name='Other Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

        # Try to access other user's recipe
        url = reverse('recipe_collaboration', kwargs={'pk': other_recipe.pk})
        response = self.client.get(url)

        # Should return 404 (not 403 to avoid revealing recipe existence)
        self.assertEqual(response.status_code, 404)

        # Home page should not show other user's recipes
        response = self.client.get(reverse('home'))
        self.assertEqual(len(response.context['recipes']), 0)
