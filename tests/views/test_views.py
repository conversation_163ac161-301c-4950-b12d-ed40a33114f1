"""
Tests for view imports and URL routing.

This module contains tests for view imports, URL patterns, and overall
view system integration.
"""

import pytest
from django.test import TestCase, Client
from django.urls import reverse, resolve
from django.contrib.auth import get_user_model
from django.http import Http404

from core.views import (
    CustomLoginView, CustomLogoutView, RegisterView,
    HomeView, RecipeCollaborationView, CreateRecipeView,
    RecipeDetailAPIView, ChatStreamAPIView, RecipeRefreshAPIView
)
from core.models import Recipe

User = get_user_model()


class TestViewImports(TestCase):
    """Test that all views can be imported correctly."""

    def test_auth_views_import(self):
        """Test that authentication views can be imported."""
        self.assertTrue(hasattr(CustomLoginView, 'as_view'))
        self.assertTrue(hasattr(CustomLogoutView, 'as_view'))
        self.assertTrue(hasattr(RegisterView, 'as_view'))

    def test_recipe_views_import(self):
        """Test that recipe views can be imported."""
        self.assertTrue(hasattr(HomeView, 'as_view'))
        self.assertTrue(hasattr(RecipeCollaborationView, 'as_view'))
        self.assertTrue(hasattr(CreateRecipeView, 'as_view'))

    def test_api_views_import(self):
        """Test that API views can be imported."""
        self.assertTrue(hasattr(RecipeDetailAPIView, 'as_view'))
        self.assertTrue(hasattr(ChatStreamAPIView, 'as_view'))
        self.assertTrue(hasattr(RecipeRefreshAPIView, 'as_view'))


class TestURLPatterns(TestCase):
    """Test URL pattern resolution."""

    def test_auth_url_patterns(self):
        """Test that authentication URLs resolve correctly."""
        # Login URL
        login_url = reverse('login')
        self.assertEqual(login_url, '/auth/login/')
        resolver = resolve(login_url)
        self.assertEqual(resolver.func.view_class, CustomLoginView)

        # Logout URL
        logout_url = reverse('logout')
        self.assertEqual(logout_url, '/auth/logout/')
        resolver = resolve(logout_url)
        self.assertEqual(resolver.func.view_class, CustomLogoutView)

        # Register URL
        register_url = reverse('register')
        self.assertEqual(register_url, '/auth/register/')
        resolver = resolve(register_url)
        self.assertEqual(resolver.func.view_class, RegisterView)

    def test_recipe_url_patterns(self):
        """Test that recipe URLs resolve correctly."""
        # Home URL
        home_url = reverse('home')
        self.assertEqual(home_url, '/')
        resolver = resolve(home_url)
        self.assertEqual(resolver.func.view_class, HomeView)

        # Create recipe URL
        create_url = reverse('create_recipe')
        self.assertEqual(create_url, '/recipe/create/')
        resolver = resolve(create_url)
        self.assertEqual(resolver.func.view_class, CreateRecipeView)

        # Recipe collaboration URL
        collab_url = reverse('recipe_collaboration', kwargs={'pk': 'test_id'})
        self.assertEqual(collab_url, '/recipe/test_id/')
        resolver = resolve(collab_url)
        self.assertEqual(resolver.func.view_class, RecipeCollaborationView)

    def test_api_url_patterns(self):
        """Test that API URLs resolve correctly."""
        # Recipe detail API URL
        detail_url = reverse('api_recipe_detail', kwargs={'pk': 'test_id'})
        self.assertEqual(detail_url, '/api/recipe/test_id/')
        resolver = resolve(detail_url)
        self.assertEqual(resolver.func.view_class, RecipeDetailAPIView)

        # Chat stream API URL
        chat_url = reverse('api_chat_stream', kwargs={'recipe_pk': 'test_id'})
        self.assertEqual(chat_url, '/api/recipe/test_id/chat/')
        resolver = resolve(chat_url)
        self.assertEqual(resolver.func.view_class, ChatStreamAPIView)

        # Recipe refresh API URL
        refresh_url = reverse('api_recipe_refresh', kwargs={'pk': 'test_id'})
        self.assertEqual(refresh_url, '/api/recipe/test_id/refresh/')
        resolver = resolve(refresh_url)
        self.assertEqual(resolver.func.view_class, RecipeRefreshAPIView)


class TestViewSystemIntegration(TestCase):
    """Test overall view system integration."""

    def setUp(self):
        """Set up test case."""
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

    def test_unauthenticated_user_redirects(self):
        """Test that unauthenticated users are redirected appropriately."""
        # Home page should redirect to login
        response = self.client.get(reverse('home'))
        self.assertEqual(response.status_code, 302)
        self.assertIn('/auth/login/', response.url)

        # Recipe creation should redirect to login
        response = self.client.post(reverse('create_recipe'))
        self.assertEqual(response.status_code, 302)
        self.assertIn('/auth/login/', response.url)

    def test_authenticated_user_access(self):
        """Test that authenticated users can access protected views."""
        self.client.force_login(self.user)

        # Home page should be accessible
        response = self.client.get(reverse('home'))
        self.assertEqual(response.status_code, 200)

        # Recipe creation should work
        response = self.client.post(reverse('create_recipe'))
        self.assertEqual(response.status_code, 302)  # Redirect to collaboration

    def test_view_inheritance_and_mixins(self):
        """Test that views use correct inheritance and mixins."""
        # Check LoginRequiredMixin usage
        from django.contrib.auth.mixins import LoginRequiredMixin

        self.assertTrue(issubclass(HomeView, LoginRequiredMixin))
        self.assertTrue(issubclass(RecipeCollaborationView, LoginRequiredMixin))
        self.assertTrue(issubclass(CreateRecipeView, LoginRequiredMixin))

        # Check Django generic view inheritance
        from django.views.generic import ListView, DetailView, CreateView, View
        from django.contrib.auth.views import LoginView, LogoutView

        self.assertTrue(issubclass(HomeView, ListView))
        self.assertTrue(issubclass(RecipeCollaborationView, DetailView))
        self.assertTrue(issubclass(CreateRecipeView, View))
        self.assertTrue(issubclass(CustomLoginView, LoginView))
        self.assertTrue(issubclass(CustomLogoutView, LogoutView))
        self.assertTrue(issubclass(RegisterView, CreateView))

    def test_view_permissions_consistency(self):
        """Test that view permissions are consistent across the system."""
        # All recipe views should require authentication
        recipe_views = [HomeView, RecipeCollaborationView, CreateRecipeView]
        for view_class in recipe_views:
            self.assertTrue(hasattr(view_class, 'dispatch'))
            # LoginRequiredMixin should be in MRO
            self.assertTrue(any('LoginRequiredMixin' in cls.__name__ for cls in view_class.__mro__))

    def test_error_handling(self):
        """Test error handling across views."""
        self.client.force_login(self.user)

        # Non-existent recipe should return 404
        response = self.client.get(reverse('recipe_collaboration', kwargs={'pk': 'nonexistent'}))
        self.assertEqual(response.status_code, 404)

    def test_csrf_protection(self):
        """Test CSRF protection on forms."""
        # Create a new client without CSRF middleware disabled
        from django.test import Client
        csrf_client = Client(enforce_csrf_checks=True)

        # Login form should require CSRF token
        response = csrf_client.post(reverse('login'), {
            'username': '<EMAIL>',
            'password': 'testpass123'
        })
        # Should fail due to missing CSRF token
        self.assertEqual(response.status_code, 403)

        # Registration form should require CSRF token
        response = csrf_client.post(reverse('register'), {
            'email': '<EMAIL>',
            'first_name': 'New',
            'last_name': 'User',
            'password': 'newpass123'
        })
        # Should fail due to missing CSRF token
        self.assertEqual(response.status_code, 403)


class TestViewContextProcessors(TestCase):
    """Test view context and template rendering."""

    def setUp(self):
        """Set up test case."""
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_login(self.user)

    def test_view_contexts_contain_required_data(self):
        """Test that view contexts contain required data."""
        # Home view should have recipes and page_title
        response = self.client.get(reverse('home'))
        self.assertIn('recipes', response.context)
        self.assertIn('page_title', response.context)

        # Create a recipe for collaboration test
        recipe = Recipe.objects.create(
            user=self.user,
            name='Context Test Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

        # Recipe collaboration view should have recipe and page_title
        response = self.client.get(reverse('recipe_collaboration', kwargs={'pk': recipe.pk}))
        self.assertIn('recipe', response.context)
        self.assertIn('page_title', response.context)

    def test_template_rendering(self):
        """Test that templates render without errors."""
        # Test auth templates (logout first for these)
        self.client.logout()
        response = self.client.get(reverse('login'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Login')

        response = self.client.get(reverse('register'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Register')

        # Login again for authenticated templates
        self.client.force_login(self.user)

        # Test recipe templates
        response = self.client.get(reverse('home'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Recipe Collection')

        # Create a recipe for collaboration template test
        recipe = Recipe.objects.create(
            user=self.user,
            name='Template Test Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

        response = self.client.get(reverse('recipe_collaboration', kwargs={'pk': recipe.pk}))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Collaborating on')


class TestViewSecurityFeatures(TestCase):
    """Test security features in views."""

    def setUp(self):
        """Set up test case."""
        self.client = Client()
        self.user1 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

    def test_recipe_ownership_enforcement(self):
        """Test that recipe ownership is properly enforced."""
        # Create recipe for user1
        recipe = Recipe.objects.create(
            user=self.user1,
            name='User1 Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

        # Login as user2
        self.client.force_login(self.user2)

        # Try to access user1's recipe
        response = self.client.get(reverse('recipe_collaboration', kwargs={'pk': recipe.pk}))
        self.assertEqual(response.status_code, 404)

    def test_user_data_isolation(self):
        """Test that user data is properly isolated."""
        # Create recipes for both users
        recipe1 = Recipe.objects.create(
            user=self.user1,
            name='User1 Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )
        recipe2 = Recipe.objects.create(
            user=self.user2,
            name='User2 Recipe',
            batch_size_gallons=5.0,
            mash_efficiency_percent=75.0,
            target_original_gravity=1.050
        )

        # Login as user1
        self.client.force_login(self.user1)
        response = self.client.get(reverse('home'))
        recipes = response.context['recipes']

        # Should only see user1's recipe
        self.assertEqual(len(recipes), 1)
        self.assertEqual(recipes[0].id, recipe1.id)

        # Login as user2
        self.client.force_login(self.user2)
        response = self.client.get(reverse('home'))
        recipes = response.context['recipes']

        # Should only see user2's recipe
        self.assertEqual(len(recipes), 1)
        self.assertEqual(recipes[0].id, recipe2.id)
