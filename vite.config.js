import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  build: {
    outDir: 'static/js/dist',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        'recipe-collaboration': 'src/recipe-collaboration.js'
      },
      output: {
        entryFileNames: '[name].js',
        chunkFileNames: '[name].js',
        assetFileNames: '[name].[ext]'
      }
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': 'http://localhost:8000',
      '/auth': 'http://localhost:8000'
    }
  },
  // For Django integration
  base: '/static/js/dist/'
})
